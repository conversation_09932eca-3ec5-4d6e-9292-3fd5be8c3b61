package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.yjzb.entity.ExpenseAiAnalysis;

/**
 * 办公费用AI解读分析服务接口
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
public interface ExpenseAiAnalysisService extends IService<ExpenseAiAnalysis> {

    /**
     * 执行AI分析
     *
     * @param indicatorId  指标ID
     * @param period       期间
     * @param analysisType 分析类型
     * @param inputParams  输入参数
     * @return 分析结果
     */
    String executeAnalysis(Long indicatorId, String period, String analysisType, String inputParams, boolean force);

    /**
     * 获取AI分析结果
     *
     * @param indicatorId  指标ID
     * @param period       期间
     * @param analysisType 分析类型
     * @return 分析结果
     */
    ExpenseAiAnalysis getAnalysisResult(Long indicatorId, String period, String analysisType);

    /**
     * 检查是否有缓存的分析结果
     *
     * @param indicatorId  指标ID
     * @param period       期间
     * @param analysisType 分析类型
     * @return 是否有缓存
     */
    boolean hasCachedResult(Long indicatorId, String period, String analysisType);
}
