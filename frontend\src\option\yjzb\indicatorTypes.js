export default {
  height: "auto",
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键ID",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "类型名称",
      prop: "typeName",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入指标类型名称",
          trigger: "blur",
        },
      ],
    },
    {
      label: "类型编码",
      prop: "typeCode",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入指标类型编码",
          trigger: "blur",
        },
      ],
    },
    {
      label: "类型描述",
      prop: "description",
      type: "input",
    },
    {
      label: "数据类型",
      prop: "dataType",
      type: "select",
      dicUrl: "/blade-system/dict-biz/dictionary?code=data_type",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      rules: [
        {
          required: true,
          message: "请输入数据类型",
          trigger: "blur",
        },
      ],
    },
    {
      label: "计量单位",
      prop: "unit",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "计算公式",
      prop: "calculationFormula",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "数据源配置",
      prop: "dataSourceConfig",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人ID",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门ID",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人ID",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "删除标记（0-未删除，1-已删除）",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
