/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity;
import java.util.List;

/**
 * 知识分类 视图对象
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "知识分类视图对象")
public class FinanceCategoryVO extends FinanceCategoryEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称")
    private String knowledgeName;

    /**
     * 上级分类名称
     */
    @Schema(description = "上级分类名称")
    private String parentName;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createUserName;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名")
    private String updateUserName;

    /**
     * 分类标签列表
     */
    @Schema(description = "分类标签列表")
    private List<String> tagList;

    /**
     * 子分类列表
     */
    @Schema(description = "子分类列表")
    private List<FinanceCategoryVO> children;

    /**
     * 文档数量
     */
    @Schema(description = "文档数量")
    private Integer documentCount;

    /**
     * 是否有子分类
     */
    @Schema(description = "是否有子分类")
    private Boolean hasChildren;
}
