package org.springblade.modules.yjzb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 办公费用预测实体类
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
@Data
@TableName("yjzb_expense_forecast")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "办公费用预测")
public class ExpenseForecast extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 指标ID
     */
    @Schema(description = "指标ID")
    private Long indicatorId;

    /**
     * 预测期间
     */
    @Schema(description = "预测期间")
    private String forecastPeriod;

    /**
     * 预测类型
     */
    @Schema(description = "预测类型")
    private String forecastType;

    /**
     * 预测值
     */
    @Schema(description = "预测值")
    private BigDecimal forecastValue;

    /**
     * 置信度
     */
    @Schema(description = "置信度")
    private BigDecimal confidenceLevel;

    /**
     * MLOps请求ID
     */
    @Schema(description = "MLOps请求ID")
    private String mlopsRequestId;

    /**
     * 输入数据
     */
    @Schema(description = "输入数据")
    private String inputData;
}
