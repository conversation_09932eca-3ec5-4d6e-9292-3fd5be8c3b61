<template>
  <basic-container>
    <div class="panel-header">
      <h4>资产负债表管理</h4>
      <el-button type="primary" size="small" @click="handleExport">导出</el-button>
    </div>
    <div class="filter-panel">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="选择月份">
          <el-date-picker
            v-model="filterForm.selectedMonth"
            type="month"
            placeholder="选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item label="资产负债指标名">
          <el-input v-model="filterForm.indicatorName" placeholder="请输入指标名" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="display: flex;">
      <div style="flex: 1;">
        <el-table :data="indicatorList" stripe border>
          <el-table-column prop="name" label="指标名称" width="180" />
          <el-table-column prop="value" label="数值" width="120" align="right" />
          <el-table-column prop="description" label="说明" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button size="mini" type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button size="mini" type="text" @click="handleAnalysis(row)">分析</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="width: 300px; margin-left: 20px;">
        <div class="warning-panel">
          <div class="panel-header">
            <h4>预警信息</h4>
            <el-badge :value="warningList.length" class="warning-badge">
              <i class="el-icon-warning"></i>
            </el-badge>
          </div>
          <div class="warning-list">
            <template v-if="warningList && warningList.length">
              <div
                v-for="item in warningList"
                :key="item.id"
                class="warning-item"
                @click="handleWarningClick(item)"
              >
                <div class="warning-content">
                  <div class="warning-title">{{ item.title }}</div>
                  <div class="warning-desc">{{ item.description }}</div>
                </div>
                <div class="warning-level" :class="`level-${item.level}`">
                  {{ item.levelName }}
                </div>
              </div>
            </template>
            <template v-else>
              <div class="warning-empty">暂无预警信息</div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="指标分析" v-model="analysisDialogVisible" width="900px" destroy-on-close @open="onAnalysisDialogOpen" @close="onAnalysisDialogClose">
      <div v-show="currentAnalysisIndicator">
        <div class="analysis-content">
          <div class="indicator-info">
            <h4>{{ currentAnalysisIndicator.name }}</h4>
            <p class="indicator-value">当前值：{{ currentAnalysisIndicator.value }}</p>
            <p class="indicator-desc">说明：{{ currentAnalysisIndicator.description }}</p>
          </div>
          <div class="trend-chart-section">
            <h4>趋势分析</h4>
            <div class="mock-chart">
              <div class="chart-placeholder">
                <div id="balanceLineChart" style="width: 100%; height: 300px;"></div>
              </div>
            </div>
          </div>
          <div class="ai-analysis-section">
            <h4>🤖 AI智能解读</h4>
            <div class="ai-content">
              <div class="analysis-item">
                <div class="analysis-label">趋势分析：</div>
                <div class="analysis-text">该指标近5个月呈现平稳或小幅波动趋势，暂无明显异常。</div>
              </div>
              <div class="analysis-item">
                <div class="analysis-label">风险评估：</div>
                <div class="analysis-text risk-low">
                  <el-tag type="success" size="small">低风险</el-tag>
                  当前指标水平正常。
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="analysisDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="exportAnalysis">导出分析报告</el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>
<script>
import * as echarts from 'echarts';
export default {
  name: 'BalanceSheetMgmt',
  data() {
    return {
      filterForm: {
        selectedMonth: '',
        indicatorName: ''
      },
      monthOptions: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05'],
      warningList: [
        { id: 1, title: '流动资产低于安全线', description: '流动资产低于安全线', level: 'high', levelName: '高风险' },
        { id: 2, title: '负债率高于行业平均', description: '负债率高于行业平均', level: 'medium', levelName: '中风险' }
      ],
      indicatorList: [
        { name: '流动资产合计', value: 1200000, description: '企业在一年内可以变现的资产总额' },
        { name: '非流动资产合计', value: 3500000, description: '企业在一年以上可以变现的资产总额' },
        { name: '资产总计', value: 4700000, description: '企业总资产' },
        { name: '流动负债合计', value: 800000, description: '企业在一年内需要偿还的负债总额' },
        { name: '非流动负债合计', value: 1200000, description: '企业在一年以上需要偿还的负债总额' },
        { name: '负债合计', value: 2000000, description: '企业总负债' },
        { name: '所有者权益（或股东权益）合计', value: 2700000, description: '企业所有者权益总额' },
        { name: '负债和所有者权益（或股东权益）总计', value: 4700000, description: '企业负债和所有者权益总额' }
      ],
      analysisDialogVisible: false,
      currentAnalysisIndicator: null,
      balanceLineChartInstance: null
    };
  },
  methods: {
    handleExport() { this.$message.success('导出中...'); },
    handleQuery() { this.$message.success('查询中...'); },
    handleReset() {
      this.filterForm = {
        selectedMonth: '',
        indicatorName: ''
      };
    },
    handleEdit(row) { this.$message.success(`编辑指标: ${row.name}`); },
    handleAnalysis(row) {
      this.currentAnalysisIndicator = row;
      this.analysisDialogVisible = true;
    },
    exportAnalysis() { this.$message.success('分析报告导出中...'); },
    onAnalysisDialogOpen() {
      this.$nextTick(() => {
        this.renderBalanceLineChart();
        if (this.balanceLineChartInstance) {
          this.balanceLineChartInstance.resize();
        }
      });
    },
    onAnalysisDialogClose() {
      if (this.balanceLineChartInstance) {
        this.balanceLineChartInstance.dispose();
        this.balanceLineChartInstance = null;
      }
    },
    renderBalanceLineChart() {
      const chartDom = document.getElementById('balanceLineChart');
      if (!chartDom) return;
      if (this.balanceLineChartInstance) {
        this.balanceLineChartInstance.dispose();
      }
      this.balanceLineChartInstance = echarts.init(chartDom);
      this.balanceLineChartInstance.setOption({
        title: { show: false },
        tooltip: { trigger: 'axis' },
        legend: { data: ['今年', '去年'] },
        grid: { left: 40, right: 20, bottom: 30, top: 30 },
        xAxis: { type: 'category', data: ['1月','2月','3月','4月','5月'] },
        yAxis: { type: 'value' },
        series: [
          { name: '今年', type: 'line', data: [120, 130, 125, 140, 135], smooth: false, label: { show: true, position: 'top' } },
          { name: '去年', type: 'line', data: [110, 120, 115, 130, 125], smooth: false, label: { show: true, position: 'top' } }
        ]
      });
      this.balanceLineChartInstance.resize();
    },
    handleWarningClick(item) {
      this.$message.warning(`查看预警详情：${item.title}`);
    }
  }
};
</script>
<style scoped>
.panel-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #eee; }
.filter-panel { margin-bottom: 15px; padding: 10px; background-color: #f5f7fa; border-radius: 4px; }
.mock-chart { border: 1px dashed #ddd; display: flex; align-items: center; justify-content: center; color: #999; text-align: center; background: #fafafa; width: 100%; height: 320px; min-height: 300px; }
.chart-placeholder { width: 100%; height: 100%; min-height: 300px; }
#balanceLineChart { width: 100% !important; height: 100% !important; min-height: 300px; }
.warning-panel {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}
.panel-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}
.warning-badge {
  position: relative;
}
.warning-list {
  min-height: 80px;
  max-height: 300px;
  overflow-y: auto;
  background: #fafbfc;
  border-radius: 4px;
  padding: 8px 0;
}
.warning-empty {
  color: #bbb;
  text-align: center;
  padding: 30px 0;
  font-size: 14px;
}
.warning-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}
.warning-item:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
}
.warning-content {
  flex: 1;
}
.warning-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
}
.warning-desc {
  font-size: 12px;
  color: #666;
}
.warning-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}
.warning-level.level-high {
  background: #fef0f0;
  color: #f56c6c;
}
.warning-level.level-medium {
  background: #fdf6ec;
  color: #e6a23c;
}
.warning-level.level-low {
  background: #f0f9ff;
  color: #409eff;
}
</style> 