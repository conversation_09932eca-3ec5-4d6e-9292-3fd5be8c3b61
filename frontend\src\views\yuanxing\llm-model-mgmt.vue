<template>
  <!-- 大模型管理 - LLM模型管理页面 -->
  <basic-container>
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <h3>LLM模型管理</h3>
      <div class="header-actions">
        <el-button type="primary" @click="addModel">
          <el-icon><plus /></el-icon> 添加模型
        </el-button>
        <el-button @click="testAllModels">
          <el-icon><cpu /></el-icon> 批量测试
        </el-button>
        <el-button @click="refreshData">
          <el-icon><refresh /></el-icon> 刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card total">
            <div class="stat-icon">
              <el-icon><cpu /></el-icon>
            </div>
            <div class="stat-content">
              <h3>{{ stats.total }}</h3>
              <p>总模型数</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card active">
            <div class="stat-icon">
              <el-icon><check /></el-icon>
            </div>
            <div class="stat-content">
              <h3>{{ stats.active }}</h3>
              <p>可用模型</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card error">
            <div class="stat-icon">
              <el-icon><warning /></el-icon>
            </div>
            <div class="stat-content">
              <h3>{{ stats.error }}</h3>
              <p>异常模型</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card usage">
            <div class="stat-icon">
              <el-icon><data-analysis /></el-icon>
            </div>
            <div class="stat-content">
              <h3>{{ stats.usage }}%</h3>
              <p>平均使用率</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-panel">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="模型名">
          <el-input v-model="filterForm.modelName" placeholder="请输入模型名" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filterForm.status" placeholder="选择状态" clearable style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="启用" value="enabled" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 模型列表 -->
    <div class="model-grid">
      <div v-for="model in modelList" :key="model.id" class="model-card">
        <div class="card-header">
          <div class="model-info">
            <h4>{{ model.name }}</h4>
            <p class="model-desc">{{ model.description }}</p>
          </div>
          <div class="model-status">
            <el-tag :type="getStatusTag(model.status)" size="small">
              {{ getStatusName(model.status) }}
            </el-tag>
          </div>
        </div>
        
        <div class="card-content">
          <div class="model-details">
            <div class="detail-item">
              <span class="label">模型类型:</span>
              <span class="value">{{ getTypeName(model.type) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">提供商:</span>
              <span class="value">{{ model.provider }}</span>
            </div>
            <div class="detail-item">
              <span class="label">API地址:</span>
              <span class="value">{{ model.apiUrl }}</span>
            </div>
            <div class="detail-item">
              <span class="label">调用次数:</span>
              <span class="value">{{ model.callCount.toLocaleString() }}</span>
            </div>
            <div class="detail-item">
              <span class="label">响应时间:</span>
              <span class="value">{{ model.avgResponseTime }}ms</span>
            </div>
            <div class="detail-item">
              <span class="label">成功率:</span>
              <span class="value">{{ model.successRate }}%</span>
            </div>
          </div>
          
          <div class="usage-chart">
            <div class="chart-header">
              <span>使用次数</span>
              <span class="usage-percent">{{ model.currentUsage }}次</span>
            </div>
            <div class="chart-bar">
              <div class="usage-bar" :style="{ width: model.currentUsage + '%' }"></div>
            </div>
          </div>
        </div>
        
        <div class="card-actions">
          <el-button size="small" @click="testModel(model)">
            <el-icon><cpu /></el-icon> 测试
          </el-button>
          <el-button size="small" @click="configModel(model)">
            <el-icon><setting /></el-icon> 配置
          </el-button>
          <el-button size="small" @click="viewStats(model)">
            <el-icon><data-analysis /></el-icon> 统计
          </el-button>
          <el-button 
            size="small" 
            @click="toggleStatus(model)"
            :type="model.status === 'active' ? 'warning' : 'success'"
          >
            {{ model.status === 'active' ? '禁用' : '启用' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑模型对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="模型名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入模型名称" />
        </el-form-item>
        
        <el-form-item label="模型类型" prop="type">
          <el-select v-model="formData.type" placeholder="选择模型类型">
            <el-option label="GPT系列" value="gpt" />
            <el-option label="Claude系列" value="claude" />
            <el-option label="本地模型" value="local" />
            <el-option label="其他模型" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="提供商" prop="provider">
          <el-input v-model="formData.provider" placeholder="如：OpenAI、Anthropic" />
        </el-form-item>
        
        <el-form-item label="API地址" prop="apiUrl">
          <el-input v-model="formData.apiUrl" placeholder="https://api.openai.com/v1/chat/completions" />
        </el-form-item>
        
        <el-form-item label="API密钥" prop="apiKey">
          <el-input 
            v-model="formData.apiKey" 
            type="password" 
            placeholder="请输入API密钥"
            show-password 
          />
        </el-form-item>
        
        <el-form-item label="模型描述" prop="description">
          <el-input 
            v-model="formData.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入模型描述"
          />
        </el-form-item>
        
        <el-form-item label="最大Token" prop="maxTokens">
          <el-input-number v-model="formData.maxTokens" :min="1" :max="100000" />
        </el-form-item>
        
        <el-form-item label="超时时间" prop="timeout">
          <el-input-number v-model="formData.timeout" :min="1" :max="300" />
          <span style="margin-left: 10px;">秒</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </template>
    </el-dialog>

    <!-- 模型测试对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      :title="`测试模型 - ${currentModel?.name}`"
      width="700px"
      destroy-on-close
    >
      <div class="test-section">
        <el-form label-width="100px">
          <el-form-item label="测试消息">
            <el-input
              v-model="testMessage"
              type="textarea"
              :rows="4"
              placeholder="请输入测试消息"
            />
          </el-form-item>
        </el-form>
        
        <div class="test-result" v-if="testResult">
          <h4>测试结果</h4>
          <div class="result-info">
            <el-tag :type="testResult.success ? 'success' : 'danger'">
              {{ testResult.success ? '成功' : '失败' }}
            </el-tag>
            <span v-if="testResult.responseTime">响应时间: {{ testResult.responseTime }}ms</span>
          </div>
          <el-input 
            v-model="testResult.response"
            type="textarea"
            :rows="6"
            readonly
            class="result-content"
          />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="testDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="runTest" :loading="testing">执行测试</el-button>
      </template>
    </el-dialog>

    <!-- 模型统计对话框 -->
    <el-dialog
      v-model="statsDialogVisible"
      :title="`统计信息 - ${currentModel?.name}`"
      width="800px"
      destroy-on-close
    >
      <div v-if="currentModel" class="stats-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <h4>调用次数</h4>
              <p>{{ currentModel.callCount.toLocaleString() }}</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <h4>成功率</h4>
              <p>{{ currentModel.successRate }}%</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <h4>平均响应时间</h4>
              <p>{{ currentModel.avgResponseTime }}ms</p>
            </div>
          </el-col>
        </el-row>
        
        <div class="chart-placeholder" style="margin-top: 20px;">
          <h4>使用趋势图</h4>
          <div style="height: 300px; border: 1px dashed #ddd; display: flex; align-items: center; justify-content: center; color: #999;">
            使用趋势图（可集成 ECharts）
          </div>
        </div>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import { Plus, Cpu, Refresh, Check, Warning, DataAnalysis, Setting } from '@element-plus/icons-vue'

export default {
  name: 'LLMModelMgmt',
  components: {
    Plus, Cpu, Refresh, Check, Warning, DataAnalysis, Setting
  },
  data() {
    return {
      loading: false,
      typeFilter: '',
      statusFilter: '',
      searchKeyword: '',
      currentPage: 1,
      pageSize: 12,
      total: 0,
      
      dialogVisible: false,
      testDialogVisible: false,
      statsDialogVisible: false,
      currentModel: null,
      testing: false,
      testMessage: '你好，请介绍一下你自己。',
      testResult: null,
      
      // 表单数据
      formData: {
        id: null,
        name: '',
        type: '',
        provider: '',
        apiUrl: '',
        apiKey: '',
        description: '',
        maxTokens: 4000,
        timeout: 30
      },
      
      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入模型名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择模型类型', trigger: 'change' }
        ],
        provider: [
          { required: true, message: '请输入提供商', trigger: 'blur' }
        ],
        apiUrl: [
          { required: true, message: '请输入API地址', trigger: 'blur' }
        ],
        apiKey: [
          { required: true, message: '请输入API密钥', trigger: 'blur' }
        ]
      },
      
      // 统计数据
      stats: {
        total: 8,
        active: 6,
        error: 1,
        usage: 65
      },
      
      // 模型列表数据
      modelList: [
        {
          id: 1,
          name: 'Qwen-32B',
          type: 'local',
          provider: '阿里云',
          apiUrl: 'http://localhost:8080/v1/chat/completions',
          description: '通义千问 Qwen-32B 大模型，本地部署版本',
          status: 'active',
          callCount: 3200,
          avgResponseTime: 2000,
          successRate: 100,
          currentUsage: 30,
          maxTokens: 8000,
          timeout: 60
        },
        {
          id: 2,
          name: 'Qwen-14B',
          type: 'local',
          provider: '阿里云',
          apiUrl: 'http://localhost:8080/v1/qwen-14b',
          description: '通义千问 Qwen-14B 大模型，本地部署版本',
          status: 'active',
          callCount: 2100,
          avgResponseTime: 1800,
          successRate: 100,
          currentUsage: 22,
          maxTokens: 8000,
          timeout: 60
        },
        {
          id: 3,
          name: 'Qwen-7B',
          type: 'local',
          provider: '阿里云',
          apiUrl: 'http://localhost:8080/v1/qwen-7B',
          description: '通义千问 Qwen-7B 大模型，本地部署版本',
          status: 'active',
          callCount: 1200,
          avgResponseTime: 1500,
          successRate: 100,
          currentUsage: 15,
          maxTokens: 4000,
          timeout: 30
        }
      ],
      filterForm: { modelName: '', status: '' }
    }
  },
  computed: {
    dialogTitle() {
      return this.formData.id ? '编辑模型' : '添加模型'
    }
  },
  methods: {
    // 工具方法
    getStatusTag(status) {
      const tagMap = {
        active: 'success',
        error: 'danger',
        disabled: 'info'
      }
      return tagMap[status] || 'info'
    },
    
    getStatusName(status) {
      const nameMap = {
        active: '可用',
        error: '异常',
        disabled: '禁用'
      }
      return nameMap[status] || status
    },
    
    getTypeName(type) {
      const nameMap = {
        gpt: 'GPT系列',
        claude: 'Claude系列',
        local: '本地模型',
        other: '其他模型'
      }
      return nameMap[type] || type
    },
    
    // 搜索和筛选
    handleSearch() {
      console.log('搜索条件:', {
        type: this.typeFilter,
        status: this.statusFilter,
        keyword: this.searchKeyword
      })
    },
    
    resetFilter() {
      this.typeFilter = ''
      this.statusFilter = ''
      this.searchKeyword = ''
      this.handleSearch()
    },
    
    // 数据操作
    refreshData() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('刷新成功')
      }, 1000)
    },
    
    // 模型操作
    addModel() {
      this.resetForm()
      this.dialogVisible = true
    },
    
    configModel(model) {
      this.formData = { ...model }
      this.dialogVisible = true
    },
    
    testModel(model) {
      this.currentModel = model
      this.testResult = null
      this.testDialogVisible = true
    },
    
    viewStats(model) {
      this.currentModel = model
      this.statsDialogVisible = true
    },
    
    toggleStatus(model) {
      const newStatus = model.status === 'active' ? 'disabled' : 'active'
      model.status = newStatus
      this.$message.success(`模型已${newStatus === 'active' ? '启用' : '禁用'}`)
    },
    
    testAllModels() {
      this.$message.info('批量测试功能')
    },
    
    // 测试功能
    runTest() {
      this.testing = true
      
      // 模拟API调用
      setTimeout(() => {
        const isSuccess = Math.random() > 0.2 // 80%成功率
        this.testResult = {
          success: isSuccess,
          responseTime: Math.floor(Math.random() * 2000) + 500,
          response: isSuccess 
            ? '你好！我是一个人工智能助手，很高兴为您服务。我可以帮助您回答问题、提供信息、协助完成各种任务。请告诉我您需要什么帮助？'
            : '错误：API调用失败，请检查网络连接和API配置。'
        }
        this.testing = false
      }, 2000)
    },
    
    // 表单操作
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.formData.id) {
            // 编辑
            const index = this.modelList.findIndex(item => item.id === this.formData.id)
            if (index > -1) {
              Object.assign(this.modelList[index], this.formData)
            }
            this.$message.success('编辑成功')
          } else {
            // 新增
            const newModel = {
              ...this.formData,
              id: Date.now(),
              status: 'active',
              callCount: 0,
              avgResponseTime: 0,
              successRate: 100,
              currentUsage: 0
            }
            this.modelList.push(newModel)
            this.$message.success('添加成功')
          }
          this.dialogVisible = false
        }
      })
    },
    
    resetForm() {
      this.formData = {
        id: null,
        name: '',
        type: '',
        provider: '',
        apiUrl: '',
        apiKey: '',
        description: '',
        maxTokens: 4000,
        timeout: 30
      }
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }
    },
    
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.handleSearch()
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      this.handleSearch()
    }
  },
  
  mounted() {
    this.total = this.modelList.length
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.stats-cards {
  margin-bottom: 20px;
  
  .stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    
    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      
      .el-icon {
        font-size: 24px;
      }
    }
    
    .stat-content {
      h3 {
        margin: 0 0 5px 0;
        font-size: 28px;
        font-weight: bold;
      }
      
      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }
    
    &.total {
      .stat-icon {
        background: #e6f7ff;
        color: #1890ff;
      }
      h3 { color: #1890ff; }
    }
    
    &.active {
      .stat-icon {
        background: #f6ffed;
        color: #52c41a;
      }
      h3 { color: #52c41a; }
    }
    
    &.error {
      .stat-icon {
        background: #fff2f0;
        color: #ff4d4f;
      }
      h3 { color: #ff4d4f; }
    }
    
    &.usage {
      .stat-icon {
        background: #f0f5ff;
        color: #722ed1;
      }
      h3 { color: #722ed1; }
    }
  }
}

.filter-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  margin-bottom: 20px;
}

.model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  
  .model-card {
    background: #fff;
    border-radius: 8px;
    border: 1px solid #ebeef5;
    overflow: hidden;
    transition: box-shadow 0.3s;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
      padding: 20px 20px 0;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      
      .model-info {
        flex: 1;
        
        h4 {
          margin: 0 0 8px 0;
          color: #303133;
          font-size: 16px;
        }
        
        .model-desc {
          margin: 0;
          color: #606266;
          font-size: 14px;
        }
      }
    }
    
    .card-content {
      padding: 20px;
      
      .model-details {
        margin-bottom: 20px;
        
        .detail-item {
          display: flex;
          margin-bottom: 8px;
          
          .label {
            width: 80px;
            color: #909399;
            font-size: 12px;
          }
          
          .value {
            flex: 1;
            color: #303133;
            font-size: 12px;
          }
        }
      }
      
      .usage-chart {
        .chart-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 12px;
          color: #606266;
        }
        
        .chart-bar {
          height: 6px;
          background: #f5f7fa;
          border-radius: 3px;
          overflow: hidden;
          
          .usage-bar {
            height: 100%;
            background: linear-gradient(90deg, #409eff, #67c23a);
            transition: width 0.3s;
          }
        }
      }
    }
    
    .card-actions {
      padding: 0 20px 20px;
      display: flex;
      gap: 8px;
    }
  }
}

.pagination-wrapper {
  text-align: center;
}

.test-section {
  .test-result {
    margin-top: 20px;
    
    h4 {
      margin: 0 0 10px 0;
      color: #303133;
    }
    
    .result-info {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    .result-content {
      margin-top: 10px;
    }
  }
}

.stats-content {
  .stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    
    h4 {
      margin: 0 0 10px 0;
      color: #606266;
      font-size: 14px;
    }
    
    p {
      margin: 0;
      color: #303133;
      font-size: 24px;
      font-weight: bold;
    }
  }
  
  .chart-placeholder {
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
    }
  }
}
</style> 