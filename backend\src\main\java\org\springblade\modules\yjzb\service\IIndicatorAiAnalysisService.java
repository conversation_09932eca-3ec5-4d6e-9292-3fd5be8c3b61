/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service;

import org.springblade.modules.yjzb.pojo.entity.IndicatorAiAnalysisEntity;
import org.springblade.core.mp.base.BaseService;

/**
 * 指标AI解读分析 服务类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-10
 */
public interface IIndicatorAiAnalysisService extends BaseService<IndicatorAiAnalysisEntity> {

    /**
     * 重新开始指定指标数据的AI分析：
     * - 使用指标数据的数值作为输入启动Dify工作流
     * - 写入一条AI解读分析记录（状态为RUNNING，保存workflowRunId与输入参数）
     * - 启动后台轮询任务每3分钟查询一次执行结果，完成后回写结果与状态
     *
     * @param indicatorValueId 指标数据ID（yjzb_indicator_values.id）
     * @return 新增的AI解读分析记录ID，失败返回null
     */
    Long restartAnalysisForIndicatorValue(Long indicatorValueId);

    /**
     * 查询指定指标数据（通过 indicatorValueId 定位 indicatorId+period）下最新一条AI解读分析记录
     *
     * @param indicatorValueId 指标数据ID
     * @return 最新的AI解读分析记录，可能为null
     */
    IndicatorAiAnalysisEntity getLatestByIndicatorValueId(Long indicatorValueId);

    /**
     * 启动当月某类型的总体AI解读（以 indicatorTypeId 作为存储的 indicatorId 记录）
     * - 聚合同类型当月所有指标数据、当年累计与年度预算等，调用Dify（blocking）
     * - 将outputs.text写入结果，状态置为COMPLETED
     *
     * @param indicatorTypeId 指标类型ID
     * @param period          月份 YYYY-MM
     * @return 新建AI解读记录ID；失败返回null
     */
    Long restartTypeMonthlyAnalysis(Long indicatorTypeId, String period);

    /**
     * 按 indicatorId + period 查询最新AI解读记录（用于类型级别查询）
     */
    IndicatorAiAnalysisEntity getLatestByIndicatorAndPeriod(Long indicatorId, String period);

    /**
     * 费用管理总览AI分析（类型级别）：传入 indicatorTypeId 与 period，聚合当月与当年累计数据并触发AI分析
     */
    Long restartExpenseOverviewAnalysis(Long indicatorTypeId, String period);

    /**
     * 资产负债总览AI分析（类型级别）：传入 indicatorTypeId 与 period，聚合当月与当年累计数据并触发AI分析
     */
    Long restartBalanceOverviewAnalysis(Long indicatorTypeId, String period);

    /**
     * 利润总览AI分析（类型级别）：传入 indicatorTypeId 与 period，聚合当月与当年累计数据并触发AI分析
     */
    Long restartProfitOverviewAnalysis(Long indicatorTypeId, String period);
}
