/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.pojo.entity.IndicatorImportHistoryEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorImportHistoryVO;

import java.util.Map;
import java.util.Objects;

/**
 * 指标导入历史记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
public class IndicatorImportHistoryWrapper extends BaseEntityWrapper<IndicatorImportHistoryEntity, IndicatorImportHistoryVO> {

	public static IndicatorImportHistoryWrapper build() {
		return new IndicatorImportHistoryWrapper();
	}

	@Override
	public IndicatorImportHistoryVO entityVO(IndicatorImportHistoryEntity indicatorImportHistory) {
		IndicatorImportHistoryVO indicatorImportHistoryVO = Objects.requireNonNull(BeanUtil.copy(indicatorImportHistory, IndicatorImportHistoryVO.class));

		// 设置导入类型名称
		if (Func.isNotBlank(indicatorImportHistory.getImportType())) {
			indicatorImportHistoryVO.setImportTypeName(getImportTypeName(indicatorImportHistory.getImportType()));
		}

		// 设置模板类型名称
		if (Func.isNotBlank(indicatorImportHistory.getTemplateType())) {
			indicatorImportHistoryVO.setTemplateTypeName(getTemplateTypeName(indicatorImportHistory.getTemplateType()));
		}

		// 设置导入状态名称
		if (Func.isNotBlank(indicatorImportHistory.getImportStatus())) {
			indicatorImportHistoryVO.setImportStatusName(getImportStatusName(indicatorImportHistory.getImportStatus()));
		}

		// 格式化文件大小
		if (indicatorImportHistory.getFileSize() != null) {
			indicatorImportHistoryVO.setFileSizeFormatted(formatFileSize(indicatorImportHistory.getFileSize()));
		}

		// 格式化导入耗时
		if (indicatorImportHistory.getImportDuration() != null) {
			indicatorImportHistoryVO.setImportDurationFormatted(formatDuration(indicatorImportHistory.getImportDuration()));
		}

		return indicatorImportHistoryVO;
	}

	/**
	 * 获取导入类型名称
	 */
	private String getImportTypeName(String importType) {
		Map<String, String> typeMap = Map.of(
			"excel", "Excel导入",
			"database", "数据库导入",
			"api", "API导入"
		);
		return typeMap.getOrDefault(importType, importType);
	}

	/**
	 * 获取模板类型名称
	 */
	private String getTemplateTypeName(String templateType) {
		Map<String, String> typeMap = Map.of(
			"profit", "利润表数据模板",
			"balance", "资产负债表数据模板",
			"expense", "三项费用数据模板",
			"tax", "税利指标明细表数据模板"
		);
		return typeMap.getOrDefault(templateType, templateType);
	}

	/**
	 * 获取导入状态名称
	 */
	private String getImportStatusName(String importStatus) {
		Map<String, String> statusMap = Map.of(
			"SUCCESS", "成功",
			"PARTIAL", "部分成功",
			"FAILED", "失败"
		);
		return statusMap.getOrDefault(importStatus, importStatus);
	}

	/**
	 * 格式化文件大小
	 */
	private String formatFileSize(Long fileSize) {
		if (fileSize == null || fileSize == 0) {
			return "0B";
		}

		String[] units = {"B", "KB", "MB", "GB"};
		int unitIndex = 0;
		double size = fileSize.doubleValue();

		while (size >= 1024 && unitIndex < units.length - 1) {
			size /= 1024;
			unitIndex++;
		}

		return String.format("%.1f%s", size, units[unitIndex]);
	}

	/**
	 * 格式化导入耗时
	 */
	private String formatDuration(Long duration) {
		if (duration == null || duration == 0) {
			return "0秒";
		}

		if (duration < 1000) {
			return duration + "毫秒";
		} else if (duration < 60000) {
			return String.format("%.1f秒", duration / 1000.0);
		} else {
			long minutes = duration / 60000;
			long seconds = (duration % 60000) / 1000;
			return minutes + "分" + seconds + "秒";
		}
	}

}
