package org.springblade.modules.yjzb.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "用于月度指标预测的特征输入(对应 费用指标_宽表.csv 的字段)")
public class IndicatorPredictFeatureDTO {
    @Schema(description = "指标ID(如 fuel_cost / water_electric_cost 或系统指标ID)")
    private String indicatorId;

    @Schema(description = "期间 YYYY-MM")
    private String period;

    @Schema(description = "当月值")
    private BigDecimal currentValue;
    @Schema(description = "lag1")
    private BigDecimal lag1Value;
    @Schema(description = "lag2")
    private BigDecimal lag2Value;
    @Schema(description = "lag3")
    private BigDecimal lag3Value;
    @Schema(description = "三月均值")
    private BigDecimal ma3Value;
    @Schema(description = "去年同期")
    private BigDecimal samePeriodLastYearValue;
    @Schema(description = "当年累计")
    private BigDecimal currentYearCumulativeValue;
    @Schema(description = "年度预算(全年合计)")
    private BigDecimal annualBudgetValue;
}
