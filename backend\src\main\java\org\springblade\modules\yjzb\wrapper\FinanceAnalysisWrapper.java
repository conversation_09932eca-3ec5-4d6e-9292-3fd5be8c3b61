package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisVO;

/**
 * 财务分析包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class FinanceAnalysisWrapper extends BaseEntityWrapper<FinanceAnalysisEntity, FinanceAnalysisVO> {

    public static FinanceAnalysisWrapper build() {
        return new FinanceAnalysisWrapper();
    }

    @Override
    public FinanceAnalysisVO entityVO(FinanceAnalysisEntity entity) {
        FinanceAnalysisVO vo = BeanUtil.copy(entity, FinanceAnalysisVO.class);
        return vo;
    }
}