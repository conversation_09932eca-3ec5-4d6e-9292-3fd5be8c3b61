/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.mapper;

import org.springblade.modules.yjzb.pojo.entity.IndicatorAnnualBudgetEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorAnnualBudgetVO;
import org.springblade.modules.yjzb.excel.IndicatorAnnualBudgetExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.List;

/**
 * 指标年度预算 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface IndicatorAnnualBudgetMapper extends BaseMapper<IndicatorAnnualBudgetEntity> {

    /**
     * 自定义分页
     *
     * @param page                  分页参数
     * @param indicatorAnnualBudget 查询参数
     * @return List<IndicatorAnnualBudgetVO>
     */
    List<IndicatorAnnualBudgetVO> selectIndicatorAnnualBudgetPage(IPage page,
            IndicatorAnnualBudgetVO indicatorAnnualBudget);

    /**
     * 获取导出数据
     *
     * @param queryWrapper 查询条件
     * @return List<IndicatorAnnualBudgetExcel>
     */
    List<IndicatorAnnualBudgetExcel> exportIndicatorAnnualBudget(
            @Param("ew") Wrapper<IndicatorAnnualBudgetEntity> queryWrapper);

    /**
     * 按指标与年份获取年度预算
     */
    IndicatorAnnualBudgetEntity findByIndicatorAndYear(@Param("indicatorId") Long indicatorId,
            @Param("year") Integer year);

    /**
     * 按指标类型与年份汇总年度预算
     * 规则：优先使用 midyear_budget（不为0/NULL），否则使用 initial_budget
     */
    BigDecimal sumBudgetByTypeAndYear(@Param("indicatorTypeId") Long indicatorTypeId, @Param("year") Integer year);

}
