import request from '@/axios';

// 按指标与年份获取预测结果（返回 forecastM02..forecastM12 等）
export const getByIndicatorAndYear = (indicatorId, year) => {
  return request({
    url: '/yjzb/indicatorForecast/getByIndicatorAndYear',
    method: 'get',
    params: { indicatorId, year }
  });
};

// 触发按指标预测当年2-12月并入库
export const predictYearly = (indicatorId) => {
  return request({
    url: '/yjzb/indicatorForecast/predict/yearly',
    method: 'post',
    params: { indicatorId }
  });
};


