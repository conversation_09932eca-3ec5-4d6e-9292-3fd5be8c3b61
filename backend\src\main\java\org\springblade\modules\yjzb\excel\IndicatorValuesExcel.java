/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.excel;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 指标数据 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class IndicatorValuesExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键ID")
	private Long id;
	/**
	 * 指标类型ID（关联指标类型表）
	 */
	@ColumnWidth(20)
	@ExcelProperty("指标类型ID（关联指标类型表）")
	private Long indicatorId;
	/**
	 * 数据期间（格式：YYYY-MM）
	 */
	@ColumnWidth(20)
	@ExcelProperty("数据期间（格式：YYYY-MM）")
	private String period;
	/**
	 * 指标数值
	 */
	@ColumnWidth(20)
	@ExcelProperty("指标数值")
	private BigDecimal value;
	/**
	 * 维度信息（JSON格式，存储多维度数据）
	 */
	@ColumnWidth(20)
	@ExcelProperty("维度信息（JSON格式，存储多维度数据）")
	private String dimensions;
	/**
	 * 数据来源
	 */
	@ColumnWidth(20)
	@ExcelProperty("数据来源")
	private String dataSource;
	/**
	 * 计算状态（PENDING-待计算、PROCESSING-计算中、COMPLETED-已完成、FAILED-失败）
	 */
	@ColumnWidth(20)
	@ExcelProperty("计算状态（PENDING-待计算、PROCESSING-计算中、COMPLETED-已完成、FAILED-失败）")
	private String calculationStatus;
	/**
	 * 删除标记（0-未删除，1-已删除）
	 */
	@ColumnWidth(20)
	@ExcelProperty("删除标记（0-未删除，1-已删除）")
	private Integer isDeleted;

}
