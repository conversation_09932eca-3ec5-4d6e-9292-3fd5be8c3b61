/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.mapper.FinanceKnowledgeMapper;
import org.springblade.modules.yjzb.pojo.entity.FinanceKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.dto.FinanceKnowledgeDTO;
import org.springblade.modules.yjzb.pojo.vo.FinanceKnowledgeVO;
import org.springblade.modules.yjzb.service.IFinanceKnowledgeService;
import org.springblade.modules.yjzb.service.IDifyService;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 财务知识库 服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceKnowledgeServiceImpl extends BaseServiceImpl<FinanceKnowledgeMapper, FinanceKnowledgeEntity> implements IFinanceKnowledgeService {

    private final IDifyService difyService;

    @Override
    public IPage<FinanceKnowledgeVO> selectFinanceKnowledgePage(IPage<FinanceKnowledgeVO> page, FinanceKnowledgeVO financeKnowledge) {
        return baseMapper.selectFinanceKnowledgePage(page, financeKnowledge);
    }

    @Override
    public FinanceKnowledgeVO getFinanceKnowledgeById(Long id) {
        return baseMapper.selectFinanceKnowledgeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFinanceKnowledge(FinanceKnowledgeDTO financeKnowledgeDTO) {
        FinanceKnowledgeEntity entity = new FinanceKnowledgeEntity();
        BeanUtils.copyProperties(financeKnowledgeDTO, entity);
        
        boolean result = saveOrUpdate(entity);
        
        // 异步同步到Dify
        if (result && Boolean.TRUE.equals(financeKnowledgeDTO.getSyncDify())) {
            asyncSyncToDify(entity.getId());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFinanceKnowledge(FinanceKnowledgeDTO financeKnowledgeDTO) {
        FinanceKnowledgeEntity entity = new FinanceKnowledgeEntity();
        BeanUtils.copyProperties(financeKnowledgeDTO, entity);
        
        boolean result = updateById(entity);
        
        // 异步同步到Dify
        if (result && Boolean.TRUE.equals(financeKnowledgeDTO.getSyncDify())) {
            asyncSyncToDify(entity.getId());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFinanceKnowledge(String ids) {
        return removeByIds(Func.toLongList(ids));
    }

    @Override
    public boolean syncToDify(Long id) {
        try {
            FinanceKnowledgeEntity entity = getById(id);
            if (entity == null) {
                log.error("知识库不存在，ID: {}", id);
                return false;
            }
            
            String datasetId;
            if (Func.isBlank(entity.getDatasetId())) {
                // 创建新的知识库
                datasetId = difyService.createDataset(entity.getName(), entity.getDescription());
                if (Func.isNotBlank(datasetId)) {
                    entity.setDatasetId(datasetId);
                    updateById(entity);
                }
            } else {
                // 更新现有知识库
                datasetId = entity.getDatasetId();
                difyService.updateDataset(datasetId, entity.getName(), entity.getDescription());
            }
            
            return Func.isNotBlank(datasetId);
        } catch (Exception e) {
            log.error("同步知识库到Dify失败，ID: {}", id, e);
            return false;
        }
    }

    @Async
    public void asyncSyncToDify(Long id) {
        try {
            syncToDify(id);
        } catch (Exception e) {
            log.error("异步同步知识库到Dify失败，ID: {}", id, e);
        }
    }
}
