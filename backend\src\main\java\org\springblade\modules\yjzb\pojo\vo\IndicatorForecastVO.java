package org.springblade.modules.yjzb.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(description = "指标预测结果 视图对象")
public class IndicatorForecastVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long indicatorId;
    private String indicatorName;
    private Integer year;
    private BigDecimal forecastM02;
    private BigDecimal forecastM03;
    private BigDecimal forecastM04;
    private BigDecimal forecastM05;
    private BigDecimal forecastM06;
    private BigDecimal forecastM07;
    private BigDecimal forecastM08;
    private BigDecimal forecastM09;
    private BigDecimal forecastM10;
    private BigDecimal forecastM11;
    private BigDecimal forecastM12;
}
