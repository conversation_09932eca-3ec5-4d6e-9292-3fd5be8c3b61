<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.IndicatorForecastMapper">

    <resultMap id="indicatorForecastResultMap" type="org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity">
        <result column="id" property="id"/>
        <result column="indicator_id" property="indicatorId"/>
        <result column="indicator_name" property="indicatorName"/>
        <result column="year" property="year"/>
        <result column="forecast_m02" property="forecastM02"/>
        <result column="forecast_m03" property="forecastM03"/>
        <result column="forecast_m04" property="forecastM04"/>
        <result column="forecast_m05" property="forecastM05"/>
        <result column="forecast_m06" property="forecastM06"/>
        <result column="forecast_m07" property="forecastM07"/>
        <result column="forecast_m08" property="forecastM08"/>
        <result column="forecast_m09" property="forecastM09"/>
        <result column="forecast_m10" property="forecastM10"/>
        <result column="forecast_m11" property="forecastM11"/>
        <result column="forecast_m12" property="forecastM12"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="selectIndicatorForecastPage" resultMap="indicatorForecastResultMap">
        SELECT * FROM yjzb_indicator_forecast WHERE is_deleted = 0
    </select>

    <select id="exportIndicatorForecast" resultType="org.springblade.modules.yjzb.excel.IndicatorForecastExcel">
        SELECT * FROM yjzb_indicator_forecast ${ew.customSqlSegment}
    </select>

    <select id="findByIndicatorAndYear" resultMap="indicatorForecastResultMap">
        SELECT *
        FROM yjzb_indicator_forecast
        WHERE is_deleted = 0
          AND indicator_id = #{indicatorId}
          AND year = #{year}
        LIMIT 1
    </select>

</mapper>


