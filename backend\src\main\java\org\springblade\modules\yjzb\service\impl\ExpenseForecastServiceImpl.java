package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.yjzb.entity.ExpenseForecast;
import org.springblade.modules.yjzb.mapper.ExpenseForecastMapper;
import org.springblade.modules.yjzb.service.ExpenseForecastService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 办公费用预测服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExpenseForecastServiceImpl extends ServiceImpl<ExpenseForecastMapper, ExpenseForecast>
        implements ExpenseForecastService {

    private final ObjectMapper objectMapper;
    private final RestTemplate restTemplate;

    @Value("${mlops.predict.url:http://10.77.115.134:80/mlops-dispatch/predict}")
    private String mlopsPredictUrl;

    @Value("${mlops.predict.jobid:cca93b7bca0445b1a25c84d8000dc22f}")
    private String mlopsJobId;

    @Value("${mlops.predict.modelid:1955543232530817025}")
    private String mlopsModelId;

    @Override
    public ExpenseForecast executeForecast(Long indicatorId, String forecastPeriod, String forecastType,
            String inputData) {
        log.info("开始执行办公费用预测 - indicatorId: {}, forecastPeriod: {}, forecastType: {}", indicatorId, forecastPeriod,
                forecastType);

        // 检查是否有缓存结果
        if (hasCachedForecast(indicatorId, forecastPeriod, forecastType)) {
            log.info("发现缓存的预测结果，直接返回");
            return getForecastResult(indicatorId, forecastPeriod, forecastType);
        }

        // 创建预测记录
        ExpenseForecast forecast = new ExpenseForecast();
        forecast.setIndicatorId(indicatorId);
        forecast.setForecastPeriod(forecastPeriod);
        forecast.setForecastType(forecastType);
        forecast.setInputData(inputData);

        // 保存到数据库
        save(forecast);

        try {
            // 调用MLOps API进行预测
            ForecastResult mlopsResult = callMlopsApi(forecastType, inputData);

            // 更新预测结果
            forecast.setForecastValue(mlopsResult.getForecastValue());
            forecast.setConfidenceLevel(mlopsResult.getConfidenceLevel());
            forecast.setMlopsRequestId(mlopsResult.getRequestId());

            updateById(forecast);

            log.info("费用预测完成 - forecastId: {}", forecast.getId());
            return forecast;

        } catch (Exception e) {
            log.error("费用预测失败", e);
            throw new RuntimeException("费用预测失败: " + e.getMessage());
        }
    }

    @Override
    public ExpenseForecast getForecastResult(Long indicatorId, String forecastPeriod, String forecastType) {
        LambdaQueryWrapper<ExpenseForecast> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpenseForecast::getIndicatorId, indicatorId)
                .eq(ExpenseForecast::getForecastPeriod, forecastPeriod)
                .eq(ExpenseForecast::getForecastType, forecastType)
                .orderByDesc(ExpenseForecast::getCreateTime)
                .last("LIMIT 1");

        return getOne(queryWrapper);
    }

    @Override
    public boolean hasCachedForecast(Long indicatorId, String forecastPeriod, String forecastType) {
        LambdaQueryWrapper<ExpenseForecast> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpenseForecast::getIndicatorId, indicatorId)
                .eq(ExpenseForecast::getForecastPeriod, forecastPeriod)
                .eq(ExpenseForecast::getForecastType, forecastType);

        return count(queryWrapper) > 0;
    }

    /**
     * 调用MLOps API进行预测
     *
     * @param forecastType 预测类型
     * @param inputData    输入数据
     * @return 预测结果
     */
    private ForecastResult callMlopsApi(String forecastType, String inputData) {
        try {
            log.info("调用MLOps API - forecastType: {}, inputData: {}", forecastType, inputData);

            // 解析输入数据
            Map<String, Object> inputMap;
            try {
                inputMap = objectMapper.readValue(inputData, Map.class);
            } catch (Exception e) {
                log.error("解析输入数据失败", e);
                inputMap = Map.of("raw_input", inputData);
            }

            // 准备MLOps请求参数
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, String> body = new java.util.HashMap<>();
            body.put("jobid", mlopsJobId);
            body.put("modelid", mlopsModelId);
            body.put("forecast_type", forecastType);
            body.put("input_data", inputData);

            // 添加其他必要的参数
            if (inputMap.containsKey("indicatorId")) {
                body.put("indicator_id", String.valueOf(inputMap.get("indicatorId")));
            }
            if (inputMap.containsKey("period")) {
                body.put("period", String.valueOf(inputMap.get("period")));
            }

            HttpEntity<Map<String, String>> request = new HttpEntity<>(body, headers);

            // 调用MLOps API
            ResponseEntity<Map> response = restTemplate.postForEntity(mlopsPredictUrl, request, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();

                Object success = responseBody.get("success");
                Object data = responseBody.get("data");

                if (success instanceof Boolean && (Boolean) success && data != null) {
                    // 解析预测结果
                    BigDecimal forecastValue = parseForecastValue(data);
                    BigDecimal confidenceLevel = parseConfidenceLevel(responseBody);
                    String requestId = generateRequestId();

                    log.info("MLOps预测成功 - forecastValue: {}, confidenceLevel: {}", forecastValue, confidenceLevel);

                    return new ForecastResult(forecastValue, confidenceLevel, requestId);
                } else {
                    log.warn("MLOps API返回失败状态: {}", responseBody);
                }
            } else {
                log.warn("MLOps API调用失败: status={}", response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("调用MLOps API异常", e);
        }

        // 如果MLOps调用失败，返回默认值
        log.warn("MLOps调用失败，使用默认预测值");
        return new ForecastResult(
                new BigDecimal("100000.00"),
                new BigDecimal("0.75"),
                "mlops_request_" + System.currentTimeMillis());
    }

    /**
     * 解析预测值
     */
    private BigDecimal parseForecastValue(Object data) {
        try {
            if (data instanceof Number) {
                return new BigDecimal(data.toString());
            } else if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                Object value = dataMap.get("forecast_value") != null ? dataMap.get("forecast_value")
                        : dataMap.get("value");
                if (value instanceof Number) {
                    return new BigDecimal(value.toString());
                }
            } else if (data instanceof String) {
                return new BigDecimal((String) data);
            }
        } catch (Exception e) {
            log.error("解析预测值失败", e);
        }
        return new BigDecimal("100000.00");
    }

    /**
     * 解析置信度
     */
    private BigDecimal parseConfidenceLevel(Map<String, Object> responseBody) {
        try {
            Object confidence = responseBody.get("confidence_level") != null ? responseBody.get("confidence_level")
                    : responseBody.get("confidence");

            if (confidence instanceof Number) {
                return new BigDecimal(confidence.toString());
            } else if (confidence instanceof String) {
                return new BigDecimal((String) confidence);
            }
        } catch (Exception e) {
            log.error("解析置信度失败", e);
        }
        return new BigDecimal("0.75");
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "mlops_request_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 预测结果内部类
     */
    private static class ForecastResult {
        private final BigDecimal forecastValue;
        private final BigDecimal confidenceLevel;
        private final String requestId;

        public ForecastResult(BigDecimal forecastValue, BigDecimal confidenceLevel, String requestId) {
            this.forecastValue = forecastValue;
            this.confidenceLevel = confidenceLevel;
            this.requestId = requestId;
        }

        public BigDecimal getForecastValue() {
            return forecastValue;
        }

        public BigDecimal getConfidenceLevel() {
            return confidenceLevel;
        }

        public String getRequestId() {
            return requestId;
        }
    }
}
