# 指标分析工具

本工具用于查询指标数据并执行AI分析，包含以下功能：

1. 查询yjzb_indicator_types获取所有指标类型
2. 查询yjzb_indicator_values表中period大于等于2025-01的数据，并调用restartAnalysisForIndicatorValue方法
3. 执行不同类型的总览分析

## 环境准备

### 使用Docker

1. 构建Docker镜像

```bash
docker build -t jupyter-indicator-analysis -f ../Dockerfile.jupyter .
```

2. 运行Docker容器

```bash
docker run -p 8888:8888 -v $(pwd):/app/notebooks jupyter-indicator-analysis
```

3. 在浏览器中访问 http://localhost:8888 打开Jupyter Notebook

### 本地环境

1. 安装依赖

```bash
pip install jupyter notebook pandas numpy matplotlib psycopg2-binary requests python-dotenv
```

2. 启动Jupyter Notebook

```bash
jupyter notebook
```

## 配置

1. 复制环境变量示例文件

```bash
cp .env.example .env
```

2. 根据实际环境修改.env文件中的配置

## 使用方法

### 查询指标类型

运行以下命令查询所有指标类型：

```bash
python query_indicator_types.py
```

### 执行指标数据AI分析

运行以下命令查询yjzb_indicator_values表中period大于等于2025-01的数据，并调用restartAnalysisForIndicatorValue方法：

```bash
python run_indicator_analysis.py
```

执行结果将保存在`indicator_analysis_results.csv`文件中，包含以下字段：
- indicator_value_id: 指标数据ID
- indicator_id: 指标ID
- period: 期间
- value: 指标值
- ai_record_id: AI分析记录ID
- status: 执行状态（成功/失败）

### 执行总览分析

运行以下命令执行不同类型的总览分析：

```bash
python run_overview_analysis.py
```

执行结果将保存在`overview_analysis_results.csv`文件中。

## 参数说明

在`.env`文件中可以配置以下参数：

- DB_HOST: 数据库主机地址
- DB_PORT: 数据库端口
- DB_NAME: 数据库名称
- DB_USER: 数据库用户名
- DB_PASSWORD: 数据库密码
- API_BASE_URL: API基础URL
- MIN_PERIOD: 查询指标数据的最小期间（格式：YYYY-MM）