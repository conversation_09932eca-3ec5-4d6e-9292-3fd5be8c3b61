<template>
  <div class="deep-analysis-container">
    <!-- 头部标题和控制区 -->
    <div class="analysis-header">
      <div class="header-left">
        <h2 class="analysis-title">
          <i class="el-icon-data-analysis"></i>
          办公费用深度分析
        </h2>
        <p class="analysis-subtitle">基于历史数据的多维度智能分析</p>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="analysisDateRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          format="YYYY-MM"
          value-format="YYYY-MM"
          @change="handleDateRangeChange"
          style="margin-right: 12px;"
        />
        <el-button type="primary" @click="refreshAnalysis" :loading="loading">
          <i class="el-icon-refresh"></i>
          刷新分析
        </el-button>
        <el-button @click="exportReport">
          <i class="el-icon-download"></i>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 核心指标概览 -->
    <div class="metrics-overview">
      <div class="metric-card" v-for="metric in coreMetrics" :key="metric.key">
        <div class="metric-icon" :class="metric.iconClass">
          <i :class="metric.icon"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metric.value }}</div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-trend" :class="metric.trendClass">
            <i :class="metric.trendIcon"></i>
            {{ metric.trend }}
          </div>
        </div>
      </div>
    </div>

    <!-- 分析内容区域 -->
    <el-tabs v-model="activeTab" class="analysis-tabs">
      <!-- 异常费用检测 -->
      <el-tab-pane label="异常费用检测" name="anomaly">
        <div class="tab-content">
          <div class="section-header">
            <h3>异常费用检测与分析</h3>
            <el-tag :type="anomalyLevel.type" size="small">
              {{ anomalyLevel.text }}
            </el-tag>
          </div>
          
          <el-row :gutter="20">
            <el-col :span="16">
              <div class="chart-container">
                <div class="chart-title">异常费用时间分布</div>
                <div id="anomalyTimeChart" style="width: 100%; height: 400px;"></div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="anomaly-list">
                <div class="list-title">异常费用清单</div>
                <div class="anomaly-item" v-for="item in anomalyList" :key="item.id">
                  <div class="anomaly-header">
                    <span class="anomaly-category">{{ item.category }}</span>
                    <el-tag :type="item.levelType" size="mini">{{ item.level }}</el-tag>
                  </div>
                  <div class="anomaly-details">
                    <div class="anomaly-amount">¥{{ formatNumber(item.amount) }}</div>
                    <div class="anomaly-date">{{ item.date }}</div>
                    <div class="anomaly-reason">{{ item.reason }}</div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>

      <!-- 预算执行分析 -->
      <el-tab-pane label="预算执行分析" name="budget">
        <div class="tab-content">
          <div class="section-header">
            <h3>年度预算执行分析</h3>
            <div class="budget-summary">
              <span>总体执行率: </span>
              <el-progress 
                :percentage="overallBudgetRate" 
                :color="getBudgetProgressColor(overallBudgetRate)"
                :show-text="false"
                style="width: 200px; margin-right: 10px;"
              />
              <span class="budget-rate">{{ overallBudgetRate }}%</span>
            </div>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-container">
                <div class="chart-title">各类别预算执行情况</div>
                <div id="budgetExecutionChart" style="width: 100%; height: 350px;"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container">
                <div class="chart-title">预算偏差分析</div>
                <div id="budgetDeviationChart" style="width: 100%; height: 350px;"></div>
              </div>
            </el-col>
          </el-row>

          <div class="budget-table-container">
            <div class="chart-title">详细预算执行表</div>
            <el-table :data="budgetExecutionData" stripe style="width: 100%">
              <el-table-column prop="category" label="费用类别" width="150"></el-table-column>
              <el-table-column prop="budget" label="年度预算" width="120" align="right">
                <template #default="{ row }">¥{{ formatNumber(row.budget) }}</template>
              </el-table-column>
              <el-table-column prop="actual" label="实际支出" width="120" align="right">
                <template #default="{ row }">¥{{ formatNumber(row.actual) }}</template>
              </el-table-column>
              <el-table-column prop="rate" label="执行率" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getBudgetRateType(row.rate)" size="small">
                    {{ row.rate }}%
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="deviation" label="预算偏差" width="120" align="right">
                <template #default="{ row }">
                  <span :class="row.deviation > 0 ? 'text-danger' : 'text-success'">
                    ¥{{ formatNumber(Math.abs(row.deviation)) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="remaining" label="剩余预算" width="120" align="right">
                <template #default="{ row }">¥{{ formatNumber(row.remaining) }}</template>
              </el-table-column>
              <el-table-column label="趋势" width="100" align="center">
                <template #default="{ row }">
                  <i :class="row.trendIcon" :style="{ color: row.trendColor }"></i>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>

      <!-- 趋势分析 -->
      <el-tab-pane label="趋势分析" name="trend">
        <div class="tab-content">
          <div class="section-header">
            <h3>历史趋势与季节性分析</h3>
            <el-radio-group v-model="trendViewType" size="small">
              <el-radio-button label="monthly">月度趋势</el-radio-button>
              <el-radio-button label="quarterly">季度趋势</el-radio-button>
              <el-radio-button label="yearly">年度对比</el-radio-button>
            </el-radio-group>
          </div>

          <div class="chart-container">
            <div class="chart-title">费用趋势分析</div>
            <div id="trendAnalysisChart" style="width: 100%; height: 450px;"></div>
          </div>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="8">
              <div class="chart-container">
                <div class="chart-title">季节性模式</div>
                <div id="seasonalPatternChart" style="width: 100%; height: 300px;"></div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="chart-container">
                <div class="chart-title">同比增长率</div>
                <div id="yoyGrowthChart" style="width: 100%; height: 300px;"></div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="chart-container">
                <div class="chart-title">环比增长率</div>
                <div id="momGrowthChart" style="width: 100%; height: 300px;"></div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>

      <!-- 预测分析 -->
      <el-tab-pane label="预测分析" name="forecast">
        <div class="tab-content">
          <div class="section-header">
            <h3>费用预测与预算建议</h3>
            <el-select v-model="forecastPeriod" size="small" style="width: 150px;">
              <el-option label="未来3个月" value="3"></el-option>
              <el-option label="未来6个月" value="6"></el-option>
              <el-option label="未来12个月" value="12"></el-option>
            </el-select>
          </div>

          <div class="chart-container">
            <div class="chart-title">费用预测模型</div>
            <div id="forecastChart" style="width: 100%; height: 400px;"></div>
          </div>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <div class="forecast-summary">
                <h4>预测摘要</h4>
                <div class="forecast-item" v-for="item in forecastSummary" :key="item.period">
                  <div class="forecast-period">{{ item.period }}</div>
                  <div class="forecast-amount">¥{{ formatNumber(item.amount) }}</div>
                  <div class="forecast-confidence">置信度: {{ item.confidence }}%</div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="budget-recommendations">
                <h4>预算建议</h4>
                <div class="recommendation-item" v-for="rec in budgetRecommendations" :key="rec.category">
                  <div class="rec-category">{{ rec.category }}</div>
                  <div class="rec-suggestion">{{ rec.suggestion }}</div>
                  <div class="rec-amount">建议预算: ¥{{ formatNumber(rec.recommendedBudget) }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { expenseAnalyzer } from '@/utils/expenseAnalyzer';

export default {
  name: 'OfficeExpenseDeepAnalysis',
  data() {
    return {
      loading: false,
      analysisDateRange: [],
      activeTab: 'anomaly',
      trendViewType: 'monthly',
      forecastPeriod: '6',
      
      // 核心指标
      coreMetrics: [],
      
      // 异常检测
      anomalyLevel: { type: 'warning', text: '中等风险' },
      anomalyList: [],
      
      // 预算分析
      overallBudgetRate: 0,
      budgetExecutionData: [],
      budgetAnalysisResult: null,
      
      // 趋势分析
      trendData: [],
      seasonalData: {},
      growthData: {},
      categoryTrends: {},
      trendInsights: [],
      
      // 预测分析
      forecastSummary: [],
      budgetRecommendations: [],
      forecastScenarios: {},
      forecastRisks: {},
      forecastInsights: [],
      categoryForecasts: {},
      
      // 图表实例
      charts: {}
    };
  },
  mounted() {
    this.initializeDateRange();
    this.loadAnalysisData();
  },
  beforeDestroy() {
    // 销毁所有图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) chart.dispose();
    });
  },
  methods: {
    // 初始化日期范围
    initializeDateRange() {
      const now = new Date();
      const endMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
      const startMonth = `${now.getFullYear() - 1}-${String(now.getMonth() + 1).padStart(2, '0')}`;
      this.analysisDateRange = [startMonth, endMonth];
    },
    
    // 日期范围变化处理
    handleDateRangeChange() {
      this.loadAnalysisData();
    },
    
    // 刷新分析
    refreshAnalysis() {
      this.loadAnalysisData();
    },
    
    // 导出报告
    exportReport() {
      this.$message.info('报告导出功能开发中...');
    },
    
    // 加载分析数据
    async loadAnalysisData() {
      this.loading = true;
      try {
        // 初始化分析器并加载数据
        const dataLoaded = await expenseAnalyzer.loadData(
          '/数据/fenxi2/办公费用.csv',
          '/数据/fenxi2/二级分类特征工程数据.csv'
        );

        if (!dataLoaded) {
          throw new Error('数据加载失败');
        }

        // 加载各类分析数据
        await this.loadCoreMetrics();
        await this.loadAnomalyData();
        await this.loadBudgetData();
        await this.loadTrendData();
        await this.loadForecastData();

        // 渲染所有图表
        this.$nextTick(() => {
          this.renderAllCharts();
        });
      } catch (error) {
        console.error('加载分析数据失败:', error);
        this.$message.error('加载分析数据失败，使用模拟数据展示');

        // 使用模拟数据作为后备
        await this.loadMockData();
        this.$nextTick(() => {
          this.renderAllCharts();
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载模拟数据
    async loadMockData() {
      await this.loadCoreMetrics();
      await this.loadAnomalyData();
      await this.loadBudgetData();
      await this.loadTrendData();
      await this.loadForecastData();
    },
    
    // 加载核心指标
    async loadCoreMetrics() {
      // 模拟数据，实际应该从CSV数据计算
      this.coreMetrics = [
        {
          key: 'totalExpense',
          label: '总费用',
          value: '¥2,456,789',
          icon: 'el-icon-money',
          iconClass: 'metric-icon-primary',
          trend: '+12.5%',
          trendClass: 'trend-up',
          trendIcon: 'el-icon-arrow-up'
        },
        {
          key: 'anomalyCount',
          label: '异常项目',
          value: '23',
          icon: 'el-icon-warning',
          iconClass: 'metric-icon-warning',
          trend: '-8.3%',
          trendClass: 'trend-down',
          trendIcon: 'el-icon-arrow-down'
        },
        {
          key: 'budgetRate',
          label: '预算执行率',
          value: '87.2%',
          icon: 'el-icon-pie-chart',
          iconClass: 'metric-icon-success',
          trend: '+5.1%',
          trendClass: 'trend-up',
          trendIcon: 'el-icon-arrow-up'
        },
        {
          key: 'efficiency',
          label: '费用效率',
          value: '92.8%',
          icon: 'el-icon-data-line',
          iconClass: 'metric-icon-info',
          trend: '+2.7%',
          trendClass: 'trend-up',
          trendIcon: 'el-icon-arrow-up'
        }
      ];
    },
    
    // 加载异常数据
    async loadAnomalyData() {
      try {
        // 使用分析器检测异常
        const anomalies = expenseAnalyzer.detectAnomalies();

        this.anomalyList = anomalies.slice(0, 20).map((anomaly, index) => ({
          id: index + 1,
          category: anomaly.category,
          amount: anomaly.amount,
          date: anomaly.date,
          level: this.mapSeverityToLevel(anomaly.severity),
          levelType: this.mapSeverityToType(anomaly.severity),
          reason: anomaly.reason,
          details: anomaly.details
        }));

        // 设置异常等级
        const criticalCount = anomalies.filter(item => item.severity === 'critical').length;
        const highCount = anomalies.filter(item => item.severity === 'high').length;

        if (criticalCount > 3) {
          this.anomalyLevel = { type: 'danger', text: '严重风险' };
        } else if (criticalCount > 0 || highCount > 5) {
          this.anomalyLevel = { type: 'danger', text: '高风险' };
        } else if (highCount > 2) {
          this.anomalyLevel = { type: 'warning', text: '中等风险' };
        } else {
          this.anomalyLevel = { type: 'success', text: '低风险' };
        }
      } catch (error) {
        console.error('加载异常数据失败:', error);
        // 使用模拟数据作为后备
        this.anomalyList = [
          {
            id: 1,
            category: '其他杂项',
            amount: 266486.96,
            date: '2024-05',
            level: '高风险',
            levelType: 'danger',
            reason: '单月支出异常增长647%'
          }
        ];
        this.anomalyLevel = { type: 'warning', text: '中等风险' };
      }
    },

    // 映射严重程度到级别
    mapSeverityToLevel(severity) {
      const mapping = {
        'critical': '严重',
        'high': '高风险',
        'medium': '中风险',
        'low': '低风险'
      };
      return mapping[severity] || '未知';
    },

    // 映射严重程度到类型
    mapSeverityToType(severity) {
      const mapping = {
        'critical': 'danger',
        'high': 'danger',
        'medium': 'warning',
        'low': 'info'
      };
      return mapping[severity] || 'info';
    },

    // 加载预算数据
    async loadBudgetData() {
      try {
        // 使用分析器进行预算分析
        const budgetAnalysis = expenseAnalyzer.analyzeBudgetExecution();

        this.overallBudgetRate = budgetAnalysis.overallSummary.overallRate;
        this.budgetExecutionData = budgetAnalysis.categoryAnalysis.map(item => ({
          category: item.category,
          budget: item.budget,
          actual: item.actual,
          rate: item.rate,
          deviation: item.deviation,
          remaining: item.remaining,
          trendIcon: this.getTrendIcon(item.trend),
          trendColor: this.getTrendColor(item.trend),
          riskLevel: item.riskLevel,
          recommendations: item.recommendations
        }));

        // 存储完整的预算分析结果
        this.budgetAnalysisResult = budgetAnalysis;
      } catch (error) {
        console.error('加载预算数据失败:', error);
        this.overallBudgetRate = 87.2;
        this.budgetExecutionData = [];
      }
    },

    // 获取趋势图标
    getTrendIcon(trend) {
      const icons = {
        'increasing': 'el-icon-arrow-up',
        'decreasing': 'el-icon-arrow-down',
        'stable': 'el-icon-minus'
      };
      return icons[trend] || 'el-icon-minus';
    },

    // 获取趋势颜色
    getTrendColor(trend) {
      const colors = {
        'increasing': '#f56c6c',
        'decreasing': '#67c23a',
        'stable': '#909399'
      };
      return colors[trend] || '#909399';
    },

    // 加载趋势数据
    async loadTrendData() {
      try {
        // 使用分析器进行趋势分析
        const trendAnalysis = expenseAnalyzer.analyzeTrends();

        this.trendData = trendAnalysis.historicalTrends;
        this.seasonalData = trendAnalysis.seasonalPatterns;
        this.growthData = trendAnalysis.growthAnalysis;
        this.categoryTrends = trendAnalysis.categoryTrends;
        this.trendInsights = trendAnalysis.insights;

        console.log('趋势分析完成:', trendAnalysis);
      } catch (error) {
        console.error('加载趋势数据失败:', error);
        this.trendData = [];
        this.seasonalData = {};
        this.growthData = {};
        this.categoryTrends = {};
        this.trendInsights = [];
      }
    },

    // 加载预测数据
    async loadForecastData() {
      try {
        // 使用分析器进行预测分析
        const forecastPeriods = parseInt(this.forecastPeriod);
        const forecastAnalysis = expenseAnalyzer.generateForecast(forecastPeriods);

        this.forecastSummary = forecastAnalysis.totalForecast.map(item => ({
          period: item.period,
          amount: item.amount,
          confidence: item.confidence
        }));

        this.budgetRecommendations = forecastAnalysis.budgetRecommendations;
        this.forecastScenarios = forecastAnalysis.scenarios;
        this.forecastRisks = forecastAnalysis.riskAssessment;
        this.forecastInsights = forecastAnalysis.insights;
        this.categoryForecasts = forecastAnalysis.categoryForecasts;

        console.log('预测分析完成:', forecastAnalysis);
      } catch (error) {
        console.error('加载预测数据失败:', error);
        this.forecastSummary = [
          { period: '未来3个月', amount: 180000, confidence: 85 },
          { period: '未来6个月', amount: 360000, confidence: 78 },
          { period: '未来12个月', amount: 720000, confidence: 65 }
        ];
        this.budgetRecommendations = [
          {
            category: '办公用品费',
            type: 'budget_utilization',
            priority: 'medium',
            title: '预算利用建议',
            description: '建议适当增加预算，支持业务发展',
            suggestedBudget: 400000,
            actions: ['加快相关项目进度', '评估预算合理性']
          }
        ];
      }
    },

    // 加载CSV数据
    async loadCSVData() {
      // 这里应该实现实际的CSV数据加载
      // 由于浏览器环境限制，这里返回模拟数据
      return [
        {
          年月: '2024-05',
          二级分类: '其他杂项',
          月度总金额: 266486.96,
          月度平均金额: 38069.57,
          月度交易次数: 7,
          较上月增长率: 0.647,
          较去年同期增长率: 8.87,
          年度预算: 788594.05,
          当年累计金额: 504084.77,
          预算完成率: 0.639,
          预算偏差: 0.222
        }
        // 更多数据...
      ];
    },

    // 异常检测算法
    detectAnomalies(data) {
      return data.filter(item => {
        // 检测条件：
        // 1. 月度总金额超过平均值的3倍
        // 2. 较上月增长率超过200%
        // 3. 预算偏差超过50%
        const avgAmount = data.reduce((sum, d) => sum + d.月度总金额, 0) / data.length;

        return item.月度总金额 > avgAmount * 3 ||
               Math.abs(item.较上月增长率) > 2.0 ||
               Math.abs(item.预算偏差) > 0.5;
      });
    },

    // 获取异常等级
    getAnomalyLevel(growthRate) {
      const rate = Math.abs(growthRate);
      if (rate > 5.0) return '高风险';
      if (rate > 2.0) return '中风险';
      return '低风险';
    },

    // 获取异常等级类型
    getAnomalyLevelType(growthRate) {
      const rate = Math.abs(growthRate);
      if (rate > 5.0) return 'danger';
      if (rate > 2.0) return 'warning';
      return 'info';
    },

    // 获取异常原因
    getAnomalyReason(item) {
      const growthRate = item.较上月增长率;
      if (Math.abs(growthRate) > 2.0) {
        return `单月支出异常${growthRate > 0 ? '增长' : '下降'}${Math.abs(growthRate * 100).toFixed(0)}%`;
      }
      if (Math.abs(item.预算偏差) > 0.5) {
        return `预算偏差过大：${(item.预算偏差 * 100).toFixed(1)}%`;
      }
      return '支出金额异常';
    },

    // 分析预算执行
    analyzeBudgetExecution(data) {
      const categoryMap = new Map();

      data.forEach(item => {
        if (!categoryMap.has(item.二级分类)) {
          categoryMap.set(item.二级分类, {
            category: item.二级分类,
            budget: item.年度预算,
            actual: item.当年累计金额,
            rate: Math.round(item.预算完成率 * 100),
            deviation: item.当年累计金额 - item.年度预算,
            remaining: Math.max(0, item.年度预算 - item.当年累计金额)
          });
        }
      });

      const categoryData = Array.from(categoryMap.values()).map(item => ({
        ...item,
        trendIcon: item.rate > 90 ? 'el-icon-arrow-up' : item.rate < 50 ? 'el-icon-arrow-down' : 'el-icon-minus',
        trendColor: item.rate > 90 ? '#f56c6c' : item.rate < 50 ? '#67c23a' : '#909399'
      }));

      const totalBudget = categoryData.reduce((sum, item) => sum + item.budget, 0);
      const totalActual = categoryData.reduce((sum, item) => sum + item.actual, 0);
      const overallRate = Math.round((totalActual / totalBudget) * 100);

      return { overallRate, categoryData };
    },

    // 分析趋势
    analyzeTrends(data) {
      // 按月份和类别分组
      const trendMap = new Map();

      data.forEach(item => {
        const key = `${item.年月}-${item.二级分类}`;
        trendMap.set(key, {
          date: item.年月,
          category: item.二级分类,
          amount: item.月度总金额,
          growthRate: item.较上月增长率,
          yoyGrowthRate: item.较去年同期增长率
        });
      });

      return Array.from(trendMap.values());
    },

    // 生成预测
    generateForecast(data) {
      // 简单的线性预测模型
      const summary = [
        { period: '未来3个月', amount: 180000, confidence: 85 },
        { period: '未来6个月', amount: 360000, confidence: 78 },
        { period: '未来12个月', amount: 720000, confidence: 65 }
      ];

      const recommendations = [
        {
          category: '办公用品费',
          suggestion: '建议适当增加预算，支持业务发展',
          recommendedBudget: 400000
        },
        {
          category: '其他杂项',
          suggestion: '需要加强费用控制，避免异常支出',
          recommendedBudget: 600000
        }
      ];

      return { summary, recommendations };
    },
    
    // 渲染所有图表
    renderAllCharts() {
      this.renderAnomalyTimeChart();
      this.renderBudgetExecutionChart();
      this.renderBudgetDeviationChart();
      this.renderTrendAnalysisChart();
      this.renderSeasonalPatternChart();
      this.renderYoyGrowthChart();
      this.renderMomGrowthChart();
      this.renderForecastChart();
    },
    
    // 渲染异常时间分布图
    renderAnomalyTimeChart() {
      const chartDom = document.getElementById('anomalyTimeChart');
      if (!chartDom) return;

      if (this.charts.anomalyTime) {
        this.charts.anomalyTime.dispose();
      }

      this.charts.anomalyTime = echarts.init(chartDom);

      // 模拟异常数据
      const anomalyData = [
        { date: '2022-04', value: 161289.05, category: '其他杂项' },
        { date: '2024-04', value: 161782.08, category: '其他杂项' },
        { date: '2024-05', value: 266486.96, category: '其他杂项' },
        { date: '2024-07', value: 222661.97, category: '其他杂项' }
      ];

      const option = {
        title: {
          text: '异常费用时间分布',
          left: 'center',
          textStyle: { fontSize: 14, fontWeight: 'normal' }
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            const data = params[0];
            return `${data.axisValue}<br/>
                    ${data.seriesName}: ¥${this.formatNumber(data.value)}<br/>
                    类别: ${anomalyData[data.dataIndex]?.category || ''}`;
          }
        },
        xAxis: {
          type: 'category',
          data: anomalyData.map(item => item.date),
          axisLabel: { fontSize: 12 }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: (value) => `¥${(value / 10000).toFixed(0)}万`,
            fontSize: 12
          }
        },
        series: [{
          name: '异常金额',
          type: 'bar',
          data: anomalyData.map(item => item.value),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#ff6b6b' },
              { offset: 1, color: '#ffa726' }
            ])
          },
          emphasis: {
            itemStyle: { color: '#ff5722' }
          }
        }]
      };

      this.charts.anomalyTime.setOption(option);
    },

    // 渲染预算执行图表
    renderBudgetExecutionChart() {
      const chartDom = document.getElementById('budgetExecutionChart');
      if (!chartDom) return;

      if (this.charts.budgetExecution) {
        this.charts.budgetExecution.dispose();
      }

      this.charts.budgetExecution = echarts.init(chartDom);

      // 模拟预算执行数据
      const budgetData = [
        { category: '办公用品费', budget: 377215.93, actual: 377215.93, rate: 100 },
        { category: '其他杂项', budget: 788594.05, actual: 788594.05, rate: 100 },
        { category: '日用品费用', budget: 198230.11, actual: 198230.11, rate: 100 },
        { category: '茶叶费', budget: 99290.29, actual: 99290.29, rate: 100 },
        { category: '水费', budget: 242663.76, actual: 242663.76, rate: 100 }
      ];

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        legend: {
          data: ['预算金额', '实际支出'],
          bottom: 0
        },
        xAxis: {
          type: 'category',
          data: budgetData.map(item => item.category),
          axisLabel: {
            rotate: 45,
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: (value) => `¥${(value / 10000).toFixed(0)}万`,
            fontSize: 12
          }
        },
        series: [
          {
            name: '预算金额',
            type: 'bar',
            data: budgetData.map(item => item.budget),
            itemStyle: { color: '#409eff' }
          },
          {
            name: '实际支出',
            type: 'bar',
            data: budgetData.map(item => item.actual),
            itemStyle: { color: '#67c23a' }
          }
        ]
      };

      this.charts.budgetExecution.setOption(option);
    },

    // 渲染预算偏差图表
    renderBudgetDeviationChart() {
      const chartDom = document.getElementById('budgetDeviationChart');
      if (!chartDom) return;

      if (this.charts.budgetDeviation) {
        this.charts.budgetDeviation.dispose();
      }

      this.charts.budgetDeviation = echarts.init(chartDom);

      // 模拟预算偏差数据
      const deviationData = [
        { category: '办公用品费', deviation: 0 },
        { category: '其他杂项', deviation: 0 },
        { category: '日用品费用', deviation: 0 },
        { category: '茶叶费', deviation: 0 },
        { category: '水费', deviation: 0 }
      ];

      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            const data = params[0];
            const deviation = data.value;
            const status = deviation > 0 ? '超支' : deviation < 0 ? '节余' : '持平';
            return `${data.axisValue}<br/>
                    预算偏差: ¥${this.formatNumber(Math.abs(deviation))}<br/>
                    状态: ${status}`;
          }
        },
        xAxis: {
          type: 'category',
          data: deviationData.map(item => item.category),
          axisLabel: {
            rotate: 45,
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: (value) => `¥${(value / 10000).toFixed(0)}万`,
            fontSize: 12
          }
        },
        series: [{
          name: '预算偏差',
          type: 'bar',
          data: deviationData.map(item => item.deviation),
          itemStyle: {
            color: (params) => {
              return params.value > 0 ? '#f56c6c' : params.value < 0 ? '#67c23a' : '#909399';
            }
          }
        }]
      };

      this.charts.budgetDeviation.setOption(option);
    },

    // 渲染趋势分析图表
    renderTrendAnalysisChart() {
      const chartDom = document.getElementById('trendAnalysisChart');
      if (!chartDom) return;

      if (this.charts.trendAnalysis) {
        this.charts.trendAnalysis.dispose();
      }

      this.charts.trendAnalysis = echarts.init(chartDom);

      // 模拟趋势数据
      const months = ['2022-01', '2022-02', '2022-03', '2022-04', '2022-05', '2022-06',
                     '2022-07', '2022-08', '2022-09', '2022-10', '2022-11', '2022-12',
                     '2023-01', '2023-02', '2023-03', '2023-04', '2023-05', '2023-06',
                     '2023-07', '2023-08', '2023-09', '2023-10', '2023-11', '2023-12',
                     '2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'];

      const trendData = [
        { name: '办公用品费', data: [23430.8, 16012.1, 21465.15, 19956.0, 28596.82, 13094.5] },
        { name: '其他杂项', data: [55054.46, 1099.0, 1325.0, 161289.05, 6943.0, 0] },
        { name: '日用品费用', data: [30573.7, 45947.1, 27388.4, 48953.5, 32465.4, 25432.0] }
      ];

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' }
        },
        legend: {
          data: trendData.map(item => item.name),
          bottom: 0
        },
        xAxis: {
          type: 'category',
          data: months.slice(0, 6),
          axisLabel: { fontSize: 12 }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: (value) => `¥${(value / 10000).toFixed(0)}万`,
            fontSize: 12
          }
        },
        series: trendData.map((item, index) => ({
          name: item.name,
          type: 'line',
          data: item.data,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: { width: 3 },
          itemStyle: {
            color: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399'][index]
          }
        }))
      };

      this.charts.trendAnalysis.setOption(option);
    },

    // 渲染季节性模式图表
    renderSeasonalPatternChart() {
      const chartDom = document.getElementById('seasonalPatternChart');
      if (!chartDom) return;

      if (this.charts.seasonalPattern) {
        this.charts.seasonalPattern.dispose();
      }

      this.charts.seasonalPattern = echarts.init(chartDom);

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: { fontSize: 12 }
        },
        series: [{
          name: '季节性分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: { show: false },
          data: [
            { value: 25, name: 'Q1春季', itemStyle: { color: '#67c23a' } },
            { value: 30, name: 'Q2夏季', itemStyle: { color: '#409eff' } },
            { value: 20, name: 'Q3秋季', itemStyle: { color: '#e6a23c' } },
            { value: 25, name: 'Q4冬季', itemStyle: { color: '#f56c6c' } }
          ]
        }]
      };

      this.charts.seasonalPattern.setOption(option);
    },

    // 渲染同比增长率图表
    renderYoyGrowthChart() {
      const chartDom = document.getElementById('yoyGrowthChart');
      if (!chartDom) return;

      if (this.charts.yoyGrowth) {
        this.charts.yoyGrowth.dispose();
      }

      this.charts.yoyGrowth = echarts.init(chartDom);

      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            const data = params[0];
            return `${data.axisValue}<br/>同比增长率: ${data.value}%`;
          }
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLabel: { fontSize: 12 }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%',
            fontSize: 12
          }
        },
        series: [{
          name: '同比增长率',
          type: 'line',
          data: [12.5, -8.3, 15.7, 22.1, -5.2, 8.9],
          smooth: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ])
          },
          lineStyle: { color: '#409eff', width: 3 },
          itemStyle: { color: '#409eff' }
        }]
      };

      this.charts.yoyGrowth.setOption(option);
    },

    // 渲染环比增长率图表
    renderMomGrowthChart() {
      const chartDom = document.getElementById('momGrowthChart');
      if (!chartDom) return;

      if (this.charts.momGrowth) {
        this.charts.momGrowth.dispose();
      }

      this.charts.momGrowth = echarts.init(chartDom);

      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            const data = params[0];
            return `${data.axisValue}<br/>环比增长率: ${data.value}%`;
          }
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLabel: { fontSize: 12 }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%',
            fontSize: 12
          }
        },
        series: [{
          name: '环比增长率',
          type: 'bar',
          data: [0, -31.7, 34.1, -7.0, 43.3, -54.2],
          itemStyle: {
            color: (params) => {
              return params.value > 0 ? '#67c23a' : '#f56c6c';
            }
          }
        }]
      };

      this.charts.momGrowth.setOption(option);
    },

    // 渲染预测图表
    renderForecastChart() {
      const chartDom = document.getElementById('forecastChart');
      if (!chartDom) return;

      if (this.charts.forecast) {
        this.charts.forecast.dispose();
      }

      this.charts.forecast = echarts.init(chartDom);

      const months = ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06',
                     '2024-07', '2024-08', '2024-09', '2024-10', '2024-11', '2024-12'];
      const historicalData = [45000, 52000, 48000, 55000, 62000, 58000];
      const forecastData = [null, null, null, null, null, null, 60000, 65000, 63000, 68000, 70000, 72000];

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' }
        },
        legend: {
          data: ['历史数据', '预测数据', '置信区间'],
          bottom: 0
        },
        xAxis: {
          type: 'category',
          data: months,
          axisLabel: { fontSize: 12 }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: (value) => `¥${(value / 10000).toFixed(0)}万`,
            fontSize: 12
          }
        },
        series: [
          {
            name: '历史数据',
            type: 'line',
            data: historicalData.concat(Array(6).fill(null)),
            lineStyle: { color: '#409eff', width: 3 },
            itemStyle: { color: '#409eff' },
            symbol: 'circle',
            symbolSize: 6
          },
          {
            name: '预测数据',
            type: 'line',
            data: forecastData,
            lineStyle: { color: '#67c23a', width: 3, type: 'dashed' },
            itemStyle: { color: '#67c23a' },
            symbol: 'circle',
            symbolSize: 6
          }
        ]
      };

      this.charts.forecast.setOption(option);
    },

    // 工具方法
    formatNumber(value) {
      if (!value) return '0';
      return parseFloat(value).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    
    getBudgetProgressColor(rate) {
      if (rate >= 90) return '#f56c6c';
      if (rate >= 70) return '#e6a23c';
      if (rate >= 50) return '#409eff';
      return '#67c23a';
    },
    
    getBudgetRateType(rate) {
      if (rate >= 95) return 'danger';
      if (rate >= 80) return 'warning';
      if (rate >= 50) return 'success';
      return 'info';
    }
  }
};
</script>

<style scoped>
.deep-analysis-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 头部样式 */
.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.analysis-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.analysis-title i {
  color: #409eff;
  font-size: 28px;
}

.analysis-subtitle {
  margin: 4px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 核心指标概览 */
.metrics-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.metric-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.metric-icon-primary {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
}

.metric-icon-warning {
  background: linear-gradient(135deg, #e6a23c, #f7ba2a);
  color: white;
}

.metric-icon-success {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.metric-icon-info {
  background: linear-gradient(135deg, #909399, #b4bccc);
  color: white;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.metric-trend {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

/* 分析标签页 */
.analysis-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.analysis-tabs ::v-deep .el-tabs__header {
  margin: 0;
  background: #f8f9fa;
  padding: 0 24px;
}

.analysis-tabs ::v-deep .el-tabs__nav-wrap::after {
  display: none;
}

.analysis-tabs ::v-deep .el-tabs__item {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-weight: 500;
}

.tab-content {
  padding: 24px;
}

/* 区块头部 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.budget-summary {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.budget-rate {
  font-weight: 600;
  color: #303133;
}

/* 图表容器 */
.chart-container {
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.chart-title {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

/* 异常费用列表 */
.anomaly-list {
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  height: 400px;
  overflow-y: auto;
}

.list-title {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  position: sticky;
  top: 0;
  z-index: 1;
}

.anomaly-item {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  transition: background-color 0.3s ease;
}

.anomaly-item:hover {
  background: #f8f9fa;
}

.anomaly-item:last-child {
  border-bottom: none;
}

.anomaly-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.anomaly-category {
  font-weight: 600;
  color: #303133;
}

.anomaly-details {
  font-size: 12px;
  color: #606266;
}

.anomaly-amount {
  font-size: 16px;
  font-weight: 600;
  color: #f56c6c;
  margin-bottom: 4px;
}

.anomaly-date {
  margin-bottom: 4px;
}

.anomaly-reason {
  color: #909399;
}

/* 预算表格 */
.budget-table-container {
  margin-top: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.text-danger {
  color: #f56c6c;
}

.text-success {
  color: #67c23a;
}

/* 预测分析 */
.forecast-summary,
.budget-recommendations {
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  padding: 20px;
}

.forecast-summary h4,
.budget-recommendations h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.forecast-item,
.recommendation-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f2f5;
}

.forecast-item:last-child,
.recommendation-item:last-child {
  border-bottom: none;
}

.forecast-period,
.rec-category {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.forecast-amount,
.rec-amount {
  font-size: 18px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 4px;
}

.forecast-confidence,
.rec-suggestion {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics-overview {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .deep-analysis-container {
    padding: 12px;
  }

  .analysis-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: space-between;
  }

  .metrics-overview {
    grid-template-columns: 1fr;
  }

  .metric-card {
    padding: 16px;
  }

  .tab-content {
    padding: 16px;
  }
}
</style>
