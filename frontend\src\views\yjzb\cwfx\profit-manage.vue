<template>
    <!-- 财务税费管控 - 利润管理页面（真实数据版本） -->
    <basic-container style="overflow-y: auto; max-height: none;">
      <!-- 顶部筛选条件 -->
      <div class="filter-panel">
        <el-form :model="filterForm" inline label-width="80px">
          <el-form-item label="选择月份">
            <el-date-picker
              v-model="filterForm.selectedMonth"
              type="month"
              placeholder="选择月份"
              format="YYYY-MM"
              value-format="YYYY-MM"
              @change="handleMonthChange"
            />
          </el-form-item>
          <el-form-item label="指标名称">
            <el-input v-model="filterForm.expenseName" placeholder="请输入指标名称" style="width: 200px;" />
          </el-form-item>
          <el-form-item label="过滤空值">
            <el-switch
              v-model="filterForm.filterEmpty"
              active-text="是"
              inactive-text="否"
              active-color="#409eff"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
  
      <!-- 统计面板 -->
      <div class="tech-dashboard">
        <!-- 主要统计数据展示 -->
        <div class="main-stats-panel">
          <div class="stats-header">
            <div class="header-left">
              <h3 class="dashboard-title">
                <span class="title-icon">📊</span>
                总体概览
              </h3>
            </div>
            <div class="header-right">
              <el-button 
                type="primary" 
                class="ai-analysis-btn"
                @click="openMonthlyTypeDialog"
              >
                <i class="el-icon-cpu"></i>
                当月AI费用解读
              </el-button>
              <button
                class="collapse-btn"
                @click="toggleDashboard"
                :aria-label="dashboardCollapsed ? '展开看板' : '收起看板'"
                :title="dashboardCollapsed ? '展开看板' : '收起看板'"
              >
                {{ dashboardCollapsed ? '∨' : '∧' }}
              </button>
            </div>
          </div>
          
          <!-- 数据网格（利润表总体概览 - 六大核心指标：按所选月份 period） -->
          <div class="stats-grid" v-show="!dashboardCollapsed">
            <div class="kpi-card">
              <div class="kpi-label">营业总收入</div>
              <div class="kpi-amount">¥{{ formatNumber(operatingRevenue) }}</div>
            </div>
            <div class="kpi-card">
              <div class="kpi-label">营业总成本</div>
              <div class="kpi-amount">¥{{ formatNumber(operatingCost) }}</div>
            </div>
            <div class="kpi-card">
              <div class="kpi-label">营业利润</div>
              <div class="kpi-amount">¥{{ formatNumber(operatingProfit) }}</div>
            </div>
            <div class="kpi-card">
              <div class="kpi-label">利润总额</div>
              <div class="kpi-amount">¥{{ formatNumber(totalProfitAmount) }}</div>
            </div>
            <div class="kpi-card">
              <div class="kpi-label">净利润</div>
              <div class="kpi-amount">¥{{ formatNumber(netProfitAmount) }}</div>
            </div>
            <div class="kpi-card">
              <div class="kpi-label">综合收益总额</div>
              <div class="kpi-amount">¥{{ formatNumber(totalComprehensiveIncome) }}</div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- 主要内容区域 -->
      <el-row :gutter="20">
        <!-- 左侧：费用清单表格 -->
        <el-col :span="14">
          <div class="expense-table-panel">
            <div class="panel-header">
              <h4>指标科目</h4>
  
            </div>
            
            <el-table :data="displayExpenseList" stripe border v-loading="loading">
              <el-table-column label="当前月份" width="120">
                <template #default="{ row }">
                  {{ row.period }}
                </template>
              </el-table-column>
              <el-table-column prop="indicatorName" label="指标名称" width="400">
                <template #default="{ row }">
                  <el-tag size="small">
                    {{ row.indicatorName }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="value" label="金额(元)" width="220" align="right">
                <template #default="{ row }">
                  <span :class="{ 'expense-abnormal': row.isAbnormal }">
                    {{ formatNumber(row.value) }}
                  </span>
                </template>
              </el-table-column>
  
              <el-table-column label="操作" width="160">
                <template #default="{ row }">
                  <el-button size="mini" type="text" @click="handleAnalysis(row)">AI分析</el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div style="margin-top: 15px; text-align: right;">
              <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="pageInfo.total"
                :page-size="pageInfo.pageSize"
                :current-page="pageInfo.currentPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-col>
  
        <!-- 右侧：异常费用预警 -->
        <el-col :span="10">
          <!-- 异常费用预警 -->
          <div class="warning-panel">
            <div class="panel-header">
              <h4>异常费用预警</h4>
              <el-badge :value="abnormalExpenses.length" class="warning-badge">
                <i class="el-icon-warning"></i>
              </el-badge>
            </div>
            <div class="warning-list">
              <div
                v-for="item in abnormalExpenses"
                :key="item.id"
                class="warning-item"
                @click="handleWarningClick(item)"
              >
                <div class="warning-content">
                  <div class="warning-title">{{ item.title }}</div>
                  <div class="warning-desc">{{ item.description }}</div>
                </div>
                <div class="warning-level" :class="`level-${item.level}`">
                  {{ item.levelName }}
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
  
      <!-- AI分析弹窗（单条） -->
      <el-dialog title="AI分析" v-model="analysisDialogVisible" width="900px" destroy-on-close @open="onAnalysisDialogOpen" @close="onAnalysisDialogClose">
        <div v-if="currentAnalysisExpense">
          <div class="analysis-content">
            <!-- 费用基本信息 -->
            <div class="expense-info">
              <h4>{{ currentAnalysisExpense.indicatorName }} - {{ currentAnalysisExpense.period }}</h4>
              <p class="expense-amount">金额：¥{{ formatNumber(currentAnalysisExpense.value) }}</p>
              <p class="expense-desc">说明：{{ currentAnalysisExpense.dataSource }}</p>
            </div>
            
            <!-- 费用趋势分析图 -->
            <div class="trend-chart-section">
              <h4>费用趋势分析</h4>
              <div id="aiTrendChart" style="width: 100%; height: 320px;"></div>
            </div>
            
            <!-- AI智能解读 -->
            <div class="ai-analysis-section">
              <div class="ai-header">
                <h4 class="ai-title">🤖 AI智能解读</h4>
                <div class="ai-status-right">
                  <template v-if="aiAnalysisContent">
                    <el-tag :type="getStatusTagType(aiAnalysisContent.executeStatus)" size="small">
                      {{ getStatusText(aiAnalysisContent.executeStatus) }}
                    </el-tag>
                    <span v-if="aiAnalysisContent.executeStatus==='RUNNING'" class="ai-status-hint">分析进行中，请稍后查看结果...</span>
                  </template>
                </div>
              </div>
              <div class="ai-content">
                <div v-if="aiAnalysisLoading">正在加载AI解读...</div>
                <div v-else-if="!aiAnalysisContent">暂无AI解读，点击"重新分析"开始。</div>
                <template v-else>
                <div v-if="aiAnswerText || aiThinkText" class="analysis-item">
                  <div class="analysis-text">
                    <!-- 思考过程折叠块 -->
                    <div v-if="aiThinkText" class="think-box">
                      <div class="think-header" @click="aiThinkCollapsed = !aiThinkCollapsed">
                        <span>{{ aiThinkCollapsed ? '展开' : '收起' }}思考过程</span>
                        <i :class="aiThinkCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
                      </div>
                      <div v-show="!aiThinkCollapsed" class="think-content">
                        <pre style="white-space:pre-wrap;word-break:break-word;">{{ aiThinkText }}</pre>
                      </div>
                    </div>
                    <!-- 正文 -->
                    <div v-if="aiAnswerText" class="ai-answer" v-html="aiAnswerHtml()"></div>
                  </div>
                </div>
                </template>
              </div>
            </div>
          </div>
        </div>
        
        <template #footer>
          <el-button @click="analysisDialogVisible = false">关闭</el-button>
          <el-button @click="rerunAnalysis" :loading="aiTrendChartLoading"><i class="el-icon-refresh"></i> 重新分析</el-button>
          <el-button type="primary" @click="exportAnalysis" disabled>导出分析报告</el-button>
        </template>
      </el-dialog>
  
      <!-- 当月类型AI解读弹窗 -->
      <el-dialog title="当月类型AI费用解读" v-model="typeDialogVisible" width="900px" destroy-on-close>
        <div class="analysis-content">
          <!-- <div class="expense-info">
            <h4>类型：{{ indicatorTypeId }} - {{ filterForm.selectedMonth }}</h4>
            <p class="expense-desc">包含当月该类型下全部指标及年度累计、预算等信息的AI解读</p>
          </div> -->
          <div class="ai-analysis-section">
            <div class="ai-header">
              <h4 class="ai-title">🤖 AI智能解读</h4>
              <div class="ai-status-right">
                <template v-if="typeAiContent">
                  <el-tag :type="getStatusTagType(typeAiContent.executeStatus)" size="small">
                    {{ getStatusText(typeAiContent.executeStatus) }}
                  </el-tag>
                  <span v-if="typeAiContent.executeStatus==='RUNNING'" class="ai-status-hint">分析进行中，请稍后查看结果...</span>
                </template>
              </div>
            </div>
            <div class="ai-content">
              <div v-if="typeAiLoading">正在加载AI解读...</div>
              <div v-else-if="!typeAiContent">暂无AI解读，点击右下角"重新生成记录"开始。</div>
              <template v-else>
                <div v-if="typeAiAnswerText || typeAiThinkText" class="analysis-item">
                  <div class="analysis-text">
                    <div v-if="typeAiThinkText" class="think-box">
                      <div class="think-header" @click="typeAiThinkCollapsed = !typeAiThinkCollapsed">
                        <span>{{ typeAiThinkCollapsed ? '展开' : '收起' }}思考过程</span>
                        <i :class="typeAiThinkCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
                      </div>
                      <div v-show="!typeAiThinkCollapsed" class="think-content">
                        <pre style="white-space:pre-wrap;word-break:break-word;">{{ typeAiThinkText }}</pre>
                      </div>
                    </div>
                    <div v-if="typeAiAnswerText" class="ai-answer" v-html="renderTypeAiHtml()"></div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
        <template #footer>
          <el-button @click="typeDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="restartMonthlyTypeAnalysisAction" :loading="typeAiLoading">
            <i class="el-icon-refresh"></i> 重新生成记录
          </el-button>
        </template>
      </el-dialog>
    
    </basic-container>
  </template>
  
  <script>
  import * as echarts from 'echarts';
  import { marked } from 'marked';
  import DOMPurify from 'dompurify';
  import { getList as getIndicatorValues, getListByType as getIndicatorValuesByType, getStatistics, getListByIndicator, restartAiAnalysis, getLatestAiAnalysis, restartMonthlyTypeAnalysis, getLatestMonthlyTypeAnalysis, restartProfitOverviewAnalysis } from "@/api/yjzb/indicatorValues";
  import { sumByTypeAndYear, getByIndicatorAndYear } from "@/api/yjzb/indicatorAnnualBudget";
  import { getByIndicatorAndYear as getForecastByIndicatorAndYear, predictYearly as triggerPredictYearly } from "@/api/yjzb/indicatorForecast";
  
  export default {
    name: 'ProfitManage',
    data() {
      return {
        loading: false,
        // 指标类型ID - 费用相关指标
        indicatorTypeId: '1952621204878041089',
        // 指标类型ID - 资产负债/利润类（用于KPI计算取数）。如后端区分不同类型，请替换为对应类型ID
        kpiIndicatorTypeId: '1952621204878041089',
        // 看板折叠状态
        dashboardCollapsed: false,
        // 筛选表单
        filterForm: {
          selectedMonth: (() => {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            return `${year}-${month}`;
          })(),
          expenseName: '',
          filterEmpty: true // 默认过滤空值
        },
        // 分页信息
        pageInfo: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        },
        // 分析弹窗
        analysisDialogVisible: false,
        currentAnalysisExpense: null,
        // 折线图
        aiTrendChartInstance: null,
        aiTrendChartLoading: false,
        // AI分析结果
        aiAnalysisLoading: false,
        aiAnalysisContent: null,
        aiThinkCollapsed: true,
        aiThinkText: '',
        aiAnswerText: '',
        // 类型解读弹窗
        typeDialogVisible: false,
        typeAiLoading: false,
        typeAiContent: null,
        typeAiThinkCollapsed: true,
        typeAiThinkText: '',
        typeAiAnswerText: '',
        // 数据列表
        expenseList: [],
        displayExpenseList: [],
        // 统计数据
        currentMonthTotal: 0,
        lastMonthTotal: 0,
        lastYearTotal: 0,
        currentMonthGrowth: 0,
        // 新增统计数据（原逻辑）
        cumulativeExpense: 0,
        annualBudget: 0,
        completionRate: 0,
        // 异常费用预警
        abnormalExpenses: [],
        // 利润表六大核心指标（当期数）
        operatingRevenue: 0,
        operatingCost: 0,
        operatingProfit: 0,
        totalProfitAmount: 0,
        netProfitAmount: 0,
        totalComprehensiveIncome: 0
      };
    },
    async mounted() {
      // 直接加载费用数据，新接口已经包含指标名称
      this.loadExpenseData();
      this.loadStatistics();
      // 预加载当年预测结果（渲染趋势/卡片可用）
      this.preloadYearlyForecast();
      // 加载利润表六大核心指标
      await this.loadProfitCoreIndicators();
    },
    methods: {
      // 获取当前月份字符串 YYYY-MM
      getCurrentMonth() {
        const now = new Date();
        const y = now.getFullYear();
        const m = String(now.getMonth() + 1).padStart(2, '0');
        return `${y}-${m}`;
      },
      // 预加载预测结果（按当前筛选月份确定年份，取该类型下的每个指标逐个获取预测，可按需优化为后端批量接口）
      async preloadYearlyForecast() {
        try {
          const [year] = (this.filterForm.selectedMonth || this.getCurrentMonth()).split('-').map(Number);
          // 这里仅示例：不批量拉取，具体渲染处再按需调用
          this._currentYear = year;
        } catch {}
      },
      // 单位换算统一为元
      toYuan(value, unit) {
        const u = (unit || '').toString();
        const num = parseFloat(value);
        const v = isNaN(num) ? 0 : num;
        if (u.includes('万')) return v * 10000;
        return v;
      },
      // 加载利润表六大核心指标（按所选月份 period）
      async loadProfitCoreIndicators() {
        try {
          const period = this.filterForm.selectedMonth || this.getCurrentMonth();
          const page = 1, size = 800;
          const params = { period };
          // KPI指标也需要应用过滤空值设置
          if (this.filterForm.filterEmpty) {
            params.filterEmpty = true;
          }
          const resp = await getIndicatorValuesByType(page, size, this.kpiIndicatorTypeId, params);
          const records = resp?.data?.data?.records || [];

          const pickByNames = (names) => {
            const r = records.find(x => names.some(n => (x?.indicatorName || '').includes(n)));
            if (!r) return 0;
            return this.toYuan(r.value, r.indicatorUnit || r.unit);
          };

          // 名称容错（不同系统命名可能略有差异）
          this.operatingRevenue = pickByNames(['营业总收入', '营业收入合计', '营业收入']);
          this.operatingCost = pickByNames(['营业总成本', '营业成本合计', '营业成本']);
          this.operatingProfit = pickByNames(['营业利润']);
          this.totalProfitAmount = pickByNames(['利润总额']);
          this.netProfitAmount = pickByNames(['净利润']);
          this.totalComprehensiveIncome = pickByNames(['综合收益总额', '综合收益合计']);
        } catch (e) {
          console.error('加载利润表核心指标失败:', e);
          this.operatingRevenue = 0;
          this.operatingCost = 0;
          this.operatingProfit = 0;
          this.totalProfitAmount = 0;
          this.netProfitAmount = 0;
          this.totalComprehensiveIncome = 0;
        }
      },

      // 加载年度预算（按指标类型与年份汇总预算）
      async loadAnnualBudget() {
        try {
          const selectedMonth = this.filterForm.selectedMonth || (() => {
            const currentDate = new Date();
            return `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
          })();
          const [year] = selectedMonth.split('-').map(Number);
          const res = await sumByTypeAndYear(this.indicatorTypeId, year);
          const data = res?.data?.data;
          // 后端返回 BigDecimal 或数字，统一转为 number
          const budget = data != null ? parseFloat(data) : 0;
          this.annualBudget = isNaN(budget) ? 0 : budget;
        } catch (e) {
          console.error('加载年度预算失败:', e);
          this.annualBudget = 0;
        }
      },
      // 加载费用数据
      async loadExpenseData() {
        this.loading = true;
        try {
          const params = {
            ...this.buildQueryParams()
          };
          
          // 使用新的按指标类型查询接口
          const response = await getIndicatorValuesByType(
            this.pageInfo.currentPage, 
            this.pageInfo.pageSize, 
            this.indicatorTypeId,
            params
          );
          
          if (response.data && response.data.data) {
            const data = response.data.data;
            this.pageInfo.total = data.total;
            this.expenseList = this.processExpenseData(data.records);
            // 后端已支持名称模糊过滤，前端直接展示
            this.displayExpenseList = this.expenseList;
            this.generateAbnormalWarnings();
          }
        } catch (error) {
          console.error('加载费用数据失败:', error);
          this.$message.error('加载费用数据失败');
        } finally {
          this.loading = false;
        }
      },
  
      // 构建查询参数
      buildQueryParams() {
        const params = {};
        
        if (this.filterForm.selectedMonth) {
          params.period = this.filterForm.selectedMonth;
        }
        // 透传名称模糊筛选到后端
        if ((this.filterForm.expenseName || '').trim()) {
          params.indicatorName_like = (this.filterForm.expenseName || '').trim();
        }
        // 过滤空值参数
        if (this.filterForm.filterEmpty) {
          params.filterEmpty = true;
        }
        
        return params;
      },
  
      // 处理费用数据，添加异常标识
      processExpenseData(records) {
        return records.map(record => {
          // 新接口已经返回了indicatorName，不需要再从映射中获取
          const indicatorName = record.indicatorName || `指标${record.indicatorId}`;
          const isAbnormal = this.checkIfAbnormal(record);
          
          return {
            ...record,
            indicatorName,
            isAbnormal
          };
        });
      },

      // 本地模糊过滤：按"指标名称/指标名"过滤当前页数据
      applyLocalFilter() {
        // 后端已过滤，前端不再二次过滤，保持分页一致
        this.displayExpenseList = Array.isArray(this.expenseList) ? [...this.expenseList] : [];
      },
  
      // 检查是否为异常数据
      checkIfAbnormal(record) {
        // 简单的异常检测逻辑：值过高或计算状态为失败
        const value = parseFloat(record.value);
        return value > 8000 || record.calculationStatus === 'FAILED';
      },
  
      // 加载统计数据
      async loadStatistics() {
        try {
          // 使用搜索框选中的月份作为本月，如果没有选择则使用当前月份
          const selectedMonth = this.filterForm.selectedMonth || (() => {
            const currentDate = new Date();
            return `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
          })();
          
          // 解析选中的月份
          const [year, month] = selectedMonth.split('-').map(Number);
          
          // 计算上月
          const lastMonthDate = new Date(year, month - 2, 1); // month-2 因为月份从0开始
          const lastMonth = `${lastMonthDate.getFullYear()}-${String(lastMonthDate.getMonth() + 1).padStart(2, '0')}`;
          
          // 计算去年同期
          const lastYearMonth = `${year - 1}-${String(month).padStart(2, '0')}`;
          
          // 并行获取三个月份的统计数据
          const [currentData, lastData, lastYearData] = await Promise.all([
            this.getMonthStatistics(selectedMonth),
            this.getMonthStatistics(lastMonth),
            this.getMonthStatistics(lastYearMonth)
          ]);
          console.log(currentData, lastData, lastYearData);
          
          this.currentMonthTotal = parseFloat(currentData.totalValue) || 0;
          this.lastMonthTotal = parseFloat(lastData.totalValue) || 0;
          this.lastYearTotal = parseFloat(lastYearData.totalValue) || 0;
          
                   // 计算增长率
           if (this.lastYearTotal > 0) {
             this.currentMonthGrowth = ((this.currentMonthTotal - this.lastYearTotal) / this.lastYearTotal) * 100;
           } else {
             this.currentMonthGrowth = 0;
           }
           
           // 计算累计费用费用（年初到当前月份）
           await this.calculateCumulativeExpense();
           // 加载年度预算
           await this.loadAnnualBudget();
  
           // 可按需读取某个指标的预测结果（示例：列表第一条）并在界面中展示
           if (this.expenseList && this.expenseList.length > 0) {
             const indId = this.expenseList[0].indicatorId;
             try {
               const forecastRes = await getForecastByIndicatorAndYear(indId, year);
               // forecastRes.data.data 包含 forecastM02..forecastM12，可在后续图表或卡片使用
               this.firstIndicatorForecast = forecastRes?.data?.data || null;
             } catch {}
           }
           
           // 计算费用完成率
           if (this.annualBudget > 0) {
             this.completionRate = Math.round((this.cumulativeExpense / this.annualBudget) * 100);
           } else {
             this.completionRate = 0;
           }
          
        } catch (error) {
          console.error('加载统计数据失败:', error);
        }
      },
  
      // 获取指定月份的统计数据
      async getMonthStatistics(period) {
        try {
          // 使用新的统计接口
          const params = {};
          // 统计数据也需要应用过滤空值设置
          if (this.filterForm.filterEmpty) {
            params.filterEmpty = true;
          }
          const response = await getStatistics(period, this.indicatorTypeId, params);
          if (response.data && response.data.data) {
            return response.data.data;
          }
          return {
            totalCount: 0,
            totalValue: 0,
            avgValue: 0,
            maxValue: 0,
            minValue: 0,
            completedCount: 0,
            failedCount: 0,
            pendingCount: 0,
            processingCount: 0
          };
        } catch (error) {
          console.error(`获取${period}月份统计数据失败:`, error);
          return {
            totalCount: 0,
            totalValue: 0,
            avgValue: 0,
            maxValue: 0,
            minValue: 0,
            completedCount: 0,
            failedCount: 0,
            pendingCount: 0,
            processingCount: 0
          };
        }
      },
  
      // 生成异常预警
      generateAbnormalWarnings() {
        this.abnormalExpenses = [];
        
        this.expenseList.forEach(expense => {
          if (expense.isAbnormal) {
            const value = parseFloat(expense.value);
            let level = 'low';
            let levelName = '低风险';
            
            if (value > 9000) {
              level = 'high';
              levelName = '高风险';
            } else if (value > 7000) {
              level = 'medium';
              levelName = '中风险';
            }
            
            this.abnormalExpenses.push({
              id: expense.id,
              title: `${expense.indicatorName}异常`,
              description: `${expense.period}月份${expense.indicatorName}金额异常：¥${this.formatNumber(expense.value)}`,
              level,
              levelName
            });
          }
        });
      },
  
      // 计算累计费用费用
      async calculateCumulativeExpense() {
        try {
          const selectedMonth = this.filterForm.selectedMonth || (() => {
            const currentDate = new Date();
            return `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
          })();
          
          const [year, month] = selectedMonth.split('-').map(Number);
          let totalCumulative = 0;
          
          // 计算年初到当前月份的累计费用
          for (let i = 1; i <= month; i++) {
            const monthStr = `${year}-${String(i).padStart(2, '0')}`;
            const monthData = await this.getMonthStatistics(monthStr);
            totalCumulative += parseFloat(monthData.totalValue) || 0;
          }
          
          this.cumulativeExpense = totalCumulative;
        } catch (error) {
          console.error('计算累计费用失败:', error);
          this.cumulativeExpense = 0;
        }
      },
  
      // 获取完成率样式类
      getCompletionRateClass() {
        if (this.completionRate >= 90) return 'danger';
        if (this.completionRate >= 70) return 'warning';
        if (this.completionRate >= 50) return 'info';
        return 'success';
      },
  
      // 获取进度条颜色
      getProgressColor() {
        if (this.completionRate >= 90) return '#f56c6c';
        if (this.completionRate >= 70) return '#e6a23c';
        if (this.completionRate >= 50) return '#409eff';
        return '#67c23a';
      },
  
  
      // 获取柱状图宽度
      getBarWidth(value, type) {
        const maxValue = Math.max(this.currentMonthTotal, this.lastMonthTotal, this.lastYearTotal);
        if (maxValue === 0) return '0%';
        return Math.round((value / maxValue) * 100) + '%';
      },
  
      // 获取进度条样式类
      getProgressClass() {
        if (this.completionRate >= 90) return 'progress-danger';
        if (this.completionRate >= 70) return 'progress-warning';
        if (this.completionRate >= 50) return 'progress-info';
        return 'progress-success';
      },
  
      // 获取平均月费用
      getAverageMonthly() {
        const selectedMonth = this.filterForm.selectedMonth || this.getCurrentMonth();
        const [year, month] = selectedMonth.split('-').map(Number);
        return month > 0 ? Math.round(this.cumulativeExpense / month) : 0;
      },
  
      // 获取预计年终费用
      getEstimatedAnnual() {
        const avgMonthly = this.getAverageMonthly();
        return avgMonthly * 12;
      },
  
      // 获取剩余预算
      getRemainingBudget() {
        return Math.max(0, this.annualBudget - this.cumulativeExpense);
      },
  
      // 获取剩余预算样式类
      getRemainingBudgetClass() {
        const remaining = this.getRemainingBudget();
        const percentage = (remaining / this.annualBudget) * 100;
        if (percentage < 10) return 'budget-critical';
        if (percentage < 30) return 'budget-warning';
        return 'budget-normal';
      },
  
      // 获取预算状态样式类
      getBudgetStatusClass() {
        if (this.completionRate >= 95) return 'status-critical';
        if (this.completionRate >= 80) return 'status-warning';
        if (this.completionRate >= 50) return 'status-normal';
        return 'status-good';
      },
  
      // 获取预算状态文本
      getBudgetStatusText() {
        if (this.completionRate >= 95) return '预算紧张';
        if (this.completionRate >= 80) return '预算告警';
        if (this.completionRate >= 50) return '执行正常';
        return '预算充足';
      },
  
      // 当月AI费用解读
      // 打开当月类型解读弹窗并加载最新结果
      async openMonthlyTypeDialog() {
        this.typeDialogVisible = true;
        await this.loadLatestMonthlyTypeAnalysis();
      },
      async loadLatestMonthlyTypeAnalysis() {
        try {
          this.typeAiLoading = true;
          const period = this.filterForm.selectedMonth;
          const res = await getLatestMonthlyTypeAnalysis(this.indicatorTypeId, period);
          const entity = res?.data?.data;
          if (entity) {
            this.typeAiContent = entity;
            this.parseTypeAiResultText(entity.result || '');
          } else {
            this.typeAiContent = null;
            this.parseTypeAiResultText('');
          }
        } catch (e) {
          console.error('加载类型AI解读失败:', e);
        } finally {
          this.typeAiLoading = false;
        }
      },
      async restartMonthlyTypeAnalysisAction() {
        try {
          this.typeAiLoading = true;
          const period = this.filterForm.selectedMonth;
          // 利润页：改为调用“利润总览AI分析”
          const resp = await restartProfitOverviewAnalysis(this.indicatorTypeId, period);
          if (resp?.data?.success) {
            this.$message.success('已重新生成记录，将自动刷新结果');
            await this.loadLatestMonthlyTypeAnalysis();
          } else {
            this.$message.error(resp?.data?.msg || '重新生成失败');
          }
        } catch (e) {
          console.error('重新生成类型AI解读失败:', e);
          this.$message.error('重新生成失败');
        } finally {
          this.typeAiLoading = false;
        }
      },
      parseTypeAiResultText(text) {
        try {
          const pattern = /<think>([\s\S]*?)<\/think>/i;
          const match = typeof text === 'string' ? text.match(pattern) : null;
          if (match && match[1] != null) {
            this.typeAiThinkText = match[1].trim();
            this.typeAiAnswerText = text.replace(pattern, '').trim();
          } else {
            this.typeAiThinkText = '';
            this.typeAiAnswerText = (text || '').trim();
          }
        } catch (e) {
          this.typeAiThinkText = '';
          this.typeAiAnswerText = (text || '').trim();
        }
      },
      renderTypeAiHtml() {
        const text = this.typeAiAnswerText || '';
        const raw = marked.parse(text, { breaks: true, gfm: true });
        const safe = DOMPurify.sanitize(raw);
        return safe;
      },
  
      // 折叠/展开看板
      toggleDashboard() {
        this.dashboardCollapsed = !this.dashboardCollapsed;
      },
   
      // 月份变化处理
      handleMonthChange(value) {
        if (value) {
          // 月份变化时自动触发查询
          this.handleFilter();
        }
      },
      
      // 查询筛选
      handleFilter() {
        this.pageInfo.currentPage = 1;
        this.loadExpenseData();
        this.loadStatistics(); // 重新加载统计数据，使用新的选中月份
        this.loadProfitCoreIndicators(); // 刷新利润表核心指标
      },
      
      // ========== KPI 计算：参考 数据/指标公式/指标描述.md ==========
      async loadProfitOverviewKpis() {
        try {
          const period = this.filterForm.selectedMonth || this.getCurrentMonth();
          const [yearStr, monthStr] = (period || '').split('-');
          const year = parseInt(yearStr || String(new Date().getFullYear()), 10);
          const month = Math.min(12, Math.max(1, parseInt(monthStr || '12', 10)));
          const lastYear = year - 1;

          // 1) 利润总额（本年累计、上年累计）与净利润（当年累计）
          const [profitThis, profitLast, netProfitThis] = await Promise.all([
            this.fetchIndicatorYearToDate('利润总额', year),
            this.fetchIndicatorYearToDate('利润总额', lastYear),
            this.fetchIndicatorYearToDate('净利润', year)
          ]);

          // 2) 年初所有者权益（取上年12月期末值）
          const equityBOY = await this.fetchBalanceAtYearEnd('归属于母公司所有者权益（或股东权益）合计', lastYear, 12);

          // 3) 卷烟库存（期末/期初）与卷烟主营业务成本（年度累计）
          const [cigEnding, cigBeginning, cogsCig] = await Promise.all([
            // 期末（取选中月份期末值）
            this.fetchBalanceAtYearEnd('库存商品（产成品）', year, month, '卷烟'),
            // 期初（取上年12月期末值）
            this.fetchBalanceAtYearEnd('库存商品（产成品）', lastYear, 12, '卷烟'),
            this.fetchIndicatorYearToDate('主营业务成本', year, '卷烟')
          ]);

          // 计算1：当年利润增长率
          const profitLastSafe = (profitLast || 0);
          this.profitGrowthRate = profitLastSafe > 0 ? ((profitThis - profitLastSafe) / profitLastSafe) * 100 : 0;

          // 计算2：国有资产保值增值率
          const equityBOYSafe = (equityBOY || 0);
          this.stateAssetPreservationRate = equityBOYSafe > 0 ? ((equityBOYSafe + (netProfitThis || 0)) / equityBOYSafe) * 100 : 0;

          // 计算3：卷烟存货周转率
          const avgCigInventory = ((cigEnding || 0) + (cigBeginning || 0)) / 2;
          this.cigaretteInventoryTurnover = avgCigInventory > 0 ? (cogsCig || 0) / avgCigInventory : 0;
        } catch (e) {
          console.error('加载利润KPI失败:', e);
          this.profitGrowthRate = 0;
          this.stateAssetPreservationRate = 0;
          this.cigaretteInventoryTurnover = 0;
        }
      },
      
      // 获取某指标的"年度累计"（按 period_like=YYYY- 查询后汇总 value）
      async fetchIndicatorYearToDate(indicatorName, year, bizDimension) {
        try {
          // 这里采用"按指标名称"匹配：服务端返回 records 含 indicatorName/indicatorCode
          const page = 1, size = 500;
          const resp = await getIndicatorValuesByType(page, size, this.kpiIndicatorTypeId, { period_like: `${year}-` });
          const records = resp?.data?.data?.records || [];
          const filtered = records.filter(r => {
            const nameOk = (r.indicatorName || '').includes(indicatorName);
            const bizOk = bizDimension ? ((r.dimensions || '').includes(bizDimension) || (r.indicatorName || '').includes(bizDimension)) : true;
            return nameOk && bizOk;
          });
          const total = filtered.reduce((acc, r) => acc + (parseFloat(r.value) || 0), 0);
          return total;
        } catch (e) {
          return 0;
        }
      },
      
      // 获取某资产负债项目在某年某月的期末值（以 period=YYYY-12 为例）
      async fetchBalanceAtYearEnd(itemName, year, month, bizDimension) {
        try {
          const page = 1, size = 500;
          const period = `${year}-${String(month).padStart(2, '0')}`;
          const resp = await getIndicatorValuesByType(page, size, this.kpiIndicatorTypeId, { period });
          const records = resp?.data?.data?.records || [];
          const match = records.find(r => {
            const nameOk = (r.indicatorName || '').includes(itemName);
            const bizOk = bizDimension ? ((r.dimensions || '').includes(bizDimension) || (r.indicatorName || '').includes(bizDimension)) : true;
            return nameOk && bizOk;
          });
          return match ? (parseFloat(match.value) || 0) : 0;
        } catch (e) {
          return 0;
        }
      },

      // 显示百分比/次数
      formatPercent(v) {
        const num = parseFloat(v);
        if (!isFinite(num)) return '0%';
        return `${num.toFixed(2)}%`;
      },
      formatTimes(v) {
        const num = parseFloat(v);
        if (!isFinite(num)) return '0次';
        return `${num.toFixed(2)}次`;
      },
      
      // 重置筛选
      resetFilter() {
        // 重置为当前月份
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        
        this.filterForm = {
          selectedMonth: `${year}-${month}`,
          expenseName: '',
          filterEmpty: true // 重置时保持默认过滤空值
        };
        this.handleFilter();
      },
      
      // 分页
      handleSizeChange(size) {
        this.pageInfo.pageSize = size;
        this.loadExpenseData();
      },
      
      handleCurrentChange(page) {
        this.pageInfo.currentPage = page;
        this.loadExpenseData();
      },
      
      // 点击预警
      handleWarningClick(item) {
        //this.$message.warning(`查看预警详情：${item.title}`);
      },
      
      // 费用分析
      handleAnalysis(row) {
        this.currentAnalysisExpense = row;
        this.analysisDialogVisible = true;
      },
      
      // 导出分析报告
      exportAnalysis() {
        this.$message.info('导出分析报告功能开发中');
      },
  
      // 分析弹窗打开
      async onAnalysisDialogOpen() {
        // 加载折线图数据
        await this.renderAiTrendChart();
        // 加载AI解读
        await this.loadLatestAiAnalysis();
      },
  
      // 分析弹窗关闭
      onAnalysisDialogClose() {
        if (this.aiTrendChartInstance) {
          this.aiTrendChartInstance.dispose();
          this.aiTrendChartInstance = null;
        }
      },
  
      // 渲染折线图：全年12个月展示
      // - 预算累计：1-12月全量（按年度预算等分）
      // - 实际累计：只到选中月份，其余月份留空
      // - 预测累计：从选中月份起使用模拟值（YTD月均）到12月，虚线显示
      async renderAiTrendChart() {
        try {
          this.aiTrendChartLoading = true;
          if (!this.currentAnalysisExpense) return;
          const chartDom = document.getElementById('aiTrendChart');
          if (!chartDom) return;
          if (this.aiTrendChartInstance) {
            this.aiTrendChartInstance.dispose();
          }
          this.aiTrendChartInstance = echarts.init(chartDom);
          this.aiTrendChartInstance.showLoading();
  
          // 解析年份与截止月份（选中为8月则 endMonthSelected=8，但x轴固定展示12个月）
          const period = this.currentAnalysisExpense.period || this.filterForm.selectedMonth;
          const [yearStr, monthStr] = (period || '').split('-');
          const year = parseInt(yearStr || String(new Date().getFullYear()), 10);
          const endMonthSelected = Math.min(12, Math.max(1, parseInt(monthStr || '12', 10)));
  
          // 1) 获取该指标的年度预算（优先年中预算，其次初始预算），按12个月均分为月度预算线
          const indicatorId = this.currentAnalysisExpense.indicatorId;
          let annual = 0;
          try {
            const budgetRes = await getByIndicatorAndYear(indicatorId, year);
            const bd = budgetRes?.data?.data || {};
            const mid = bd.midyearBudget != null ? parseFloat(bd.midyearBudget) : 0;
            const initB = bd.initialBudget != null ? parseFloat(bd.initialBudget) : 0;
            annual = (mid && !isNaN(mid) && mid !== 0) ? mid : (initB && !isNaN(initB) ? initB : 0);
          } catch (e) {
            // 忽略，annual 保持为 0
          }
          // 使用"分"（整数）避免小数误差
          const annualCents = Math.round((annual || 0) * 100);
  
          // 2) 获取该费用指标的当年每月实际值（用于1-12月计算）
          const allMonths = Array.from({ length: 12 }, (_, i) => `${year}-${String(i + 1).padStart(2, '0')}`);
          const pageSize = 200;
          const params = { period_like: `${year}-` };
          // 折线图数据也需要应用过滤空值设置
          if (this.filterForm.filterEmpty) {
            params.filterEmpty = true;
          }
          const resp = await getListByIndicator(1, pageSize, indicatorId, params);
          const records = resp?.data?.data?.records || [];
          const monthToValueCents = {};
          records.forEach(r => {
            if (r.period && r.value != null) {
              const v = parseFloat(r.value);
              monthToValueCents[r.period] = Math.round((isNaN(v) ? 0 : v) * 100);
            }
          });
          // 逐月累计（分）：
          const monthlyActualsCents = allMonths.map(m => monthToValueCents[m] || 0);
          const budgetSeries = [];
          for (let i = 0; i < 12; i++) {
            const accBudgetCents = Math.floor(annualCents * (i + 1) / 12);
            budgetSeries.push(Number((accBudgetCents / 100).toFixed(2)));
          }
  
          // 实际累计：仅到选中月份，其余置为null
          const actualSeries = new Array(12).fill(null);
          let accActualCentsAtSelected = 0;
          for (let i = 0; i < endMonthSelected; i++) {
            accActualCentsAtSelected += monthlyActualsCents[i];
            actualSeries[i] = Number((accActualCentsAtSelected / 100).toFixed(2));
          }
  
          // 预测累计：若有后端预测结果表，则用其2-12月当月预测值累计；否则回退到YTD月均
          const forecastSeries = new Array(12).fill(null);
          let forecastMonthlyCents = new Array(12).fill(0);
          try {
            const fRes = await getForecastByIndicatorAndYear(indicatorId, year);
            const f = fRes?.data?.data;
            if (f) {
              // 将当月预测值转为分
              for (let i = 1; i <= 12; i++) {
                let v = 0;
                if (i >= 2) {
                  const key = `forecastM${String(i).padStart(2, '0')}`;
                  const raw = f[key];
                  if (raw != null) v = Math.round(parseFloat(raw) * 100);
                }
                forecastMonthlyCents[i - 1] = v;
              }
              // 组合：选中月份之前采用实际累计；选中月份及之后= 实际累计到选中月 + 预测当月起累计
              let runningPred = 0; // 从选中月份起的预测累计（分）
              for (let i = 0; i < 12; i++) {
                if (i < endMonthSelected) {
                  // 直接用实际累计曲线（包含选中月份锚点）
                  forecastSeries[i] = actualSeries[i];
                } else {
                  runningPred += forecastMonthlyCents[i] || 0;
                  const cumul = accActualCentsAtSelected + runningPred;
                  forecastSeries[i] = Number((cumul / 100).toFixed(2));
                }
              }
            } else {
              // 回退到YTD月均
              if (endMonthSelected > 0) {
                const avgMonthlyCents = Math.round(accActualCentsAtSelected / endMonthSelected) || 0;
                let runningForecastCents = accActualCentsAtSelected;
                forecastSeries[endMonthSelected - 1] = Number((runningForecastCents / 100).toFixed(2));
                for (let i = endMonthSelected; i < 12; i++) {
                  runningForecastCents += avgMonthlyCents;
                  forecastSeries[i] = Number((runningForecastCents / 100).toFixed(2));
                }
              }
            }
          } catch (e) {
            // 回退到YTD月均
            if (endMonthSelected > 0) {
              const avgMonthlyCents = Math.round(accActualCentsAtSelected / endMonthSelected) || 0;
              let runningForecastCents = accActualCentsAtSelected;
              forecastSeries[endMonthSelected - 1] = Number((runningForecastCents / 100).toFixed(2));
              for (let i = endMonthSelected; i < 12; i++) {
                runningForecastCents += avgMonthlyCents;
                forecastSeries[i] = Number((runningForecastCents / 100).toFixed(2));
              }
            }
          }
  
          // 计算当月值（实际/预测）：当前累计 - 上一个累计
          const actualMonthly = new Array(12).fill(null);
          for (let i = 0; i < 12; i++) {
            if (actualSeries[i] != null) {
              const prev = (i > 0 && actualSeries[i - 1] != null) ? actualSeries[i - 1] : 0;
              actualMonthly[i] = Number((actualSeries[i] - prev).toFixed(2));
            }
          }
          const anchorIndex = Math.max(0, endMonthSelected - 1);
          const forecastMonthly = new Array(12).fill(null);
          for (let i = 0; i < 12; i++) {
            if (forecastSeries[i] != null) {
              if (i > anchorIndex && forecastSeries[i - 1] != null) {
                forecastMonthly[i] = Number((forecastSeries[i] - forecastSeries[i - 1]).toFixed(2));
              } else {
                forecastMonthly[i] = null;
              }
            }
          }
  
          const vm = this;
          this.aiTrendChartInstance.setOption({
            tooltip: {
              trigger: 'item',
              triggerOn: 'mousemove',
              axisPointer: { type: 'cross' },
              confine: true,
              formatter(params) {
                const items = Array.isArray(params) ? params : (params ? [params] : []);
                if (items.length === 0) return '';
                const title = items[0]?.axisValue || '';
                const lines = items.map(it => {
                  const v = Array.isArray(it.value) ? it.value[1] : it.value;
                  const num = (typeof v === 'number') ? v : parseFloat(v);
                  const totalText = (num == null || isNaN(num)) ? '-' : `¥${vm.formatNumber(num)}`;
                  let monthText = '';
                  if (typeof it.dataIndex === 'number') {
                    if (it.seriesName === '实际累计') {
                      const mv = actualMonthly[it.dataIndex];
                      if (mv != null && !isNaN(mv)) monthText = `；当月：¥${vm.formatNumber(mv)}`;
                    } else if (it.seriesName === '预测累计') {
                      const mv = forecastMonthly[it.dataIndex];
                      if (mv != null && !isNaN(mv)) monthText = `；当月：¥${vm.formatNumber(mv)}`;
                    }
                  }
                  return `${it.marker}${it.seriesName}：${totalText}${monthText}`;
                });
                return `${title}<br/>${lines.join('<br/>')}`;
              }
            },
            legend: { data: ['预算值', '实际累计', '预测累计'] },
            grid: { left: 40, right: 20, bottom: 30, top: 30 },
            xAxis: { type: 'category', data: allMonths.map(m => `${parseInt(m.split('-')[1])}月`) },
            yAxis: {
              type: 'value',
              axisLabel: {
                formatter: (value) => (typeof value === 'number' ? value.toFixed(2) : value)
              }
            },
            series: [
              { name: '预算值', type: 'line', data: budgetSeries, smooth: false, showSymbol: true, symbol: 'circle', symbolSize: 6, label: { show: false }, lineStyle: { type: 'dashed', width: 2, color: '#909399' } },
              { name: '预测累计', type: 'line', data: forecastSeries, smooth: false, connectNulls: true, showSymbol: true, symbol: 'circle', symbolSize: 6, label: { show: true, position: 'top', formatter: (p) => (typeof p.value === 'number' ? p.value.toFixed(2) : p.value) }, lineStyle: { width: 2, color: '#409EFF' } },
              { name: '实际累计', type: 'line', data: actualSeries, smooth: false, connectNulls: false, showSymbol: true, symbol: 'circle', symbolSize: 6, label: { show: true, position: 'top', formatter: (p) => (typeof p.value === 'number' ? p.value.toFixed(2) : p.value) }, lineStyle: { width: 2, color: '#F59E0B' } },  
          ]
          });
          // 悬停即可显示金额，依赖 tooltip.formatter，无需点击事件
        } catch (e) {
          console.error('渲染AI趋势图失败:', e);
          this.$message.error('加载趋势数据失败');
        } finally {
          if (this.aiTrendChartInstance) this.aiTrendChartInstance.hideLoading();
          this.aiTrendChartLoading = false;
        }
      },
  
  
      // 重新分析
      async rerunAnalysis() {
        if (!this.currentAnalysisExpense) return;
        try {
          this.aiTrendChartLoading = true;
          
          // 先触发重新预测当年
          const indicatorId = this.currentAnalysisExpense.indicatorId;
          if (indicatorId) {
            try {
              const predictRes = await triggerPredictYearly(indicatorId);
              if (predictRes?.data?.success) {
                this.$message.success('已触发重新预测当年数据');
              } else {
                console.warn('触发重新预测失败:', predictRes?.data?.msg);
              }
            } catch (e) {
              console.error('触发重新预测失败', e);
            }
          }
          
          // 然后重新开始AI分析
          const indicatorValueId = this.currentAnalysisExpense.id;
          const resp = await restartAiAnalysis(indicatorValueId);
          if (resp?.data?.success) {
            this.$message.success('已重新开始AI分析，稍后自动刷新结果');
            // 立即刷新一次结果；随后开始短期轮询一段时间
            await this.loadLatestAiAnalysis();
            // 2分钟内每20秒查询一次，直到状态为COMPLETED/FAILED或超时
            const start = Date.now();
            const poll = async () => {
              const elapsed = Date.now() - start;
              if (elapsed > 120000) return; // 超过2分钟停止轻量轮询（后台仍每3分钟轮询）
              const ok = await this.loadLatestAiAnalysis(true);
              if (!ok) return; // 已完成或失败，停止
              setTimeout(poll, 20000);
            };
            setTimeout(poll, 20000);
          } else {
            this.$message.error(resp?.data?.msg || '启动AI分析失败');
          }
        } catch (e) {
          console.error('重新分析失败:', e);
          this.$message.error('重新分析失败');
        } finally {
          this.aiTrendChartLoading = false;
        }
        // 折线图保持与现状一致重绘
        await this.renderAiTrendChart();
      },
  
      // 加载最新AI解读
      async loadLatestAiAnalysis(polling = false) {
        if (!this.currentAnalysisExpense) return false;
        try {
          this.aiAnalysisLoading = !polling;
          const indicatorValueId = this.currentAnalysisExpense.id;
          const res = await getLatestAiAnalysis(indicatorValueId);
          const entity = res?.data?.data;
          if (entity) {
            this.aiAnalysisContent = entity;
            this.parseAiResultText(entity.result || '');
            // 返回true表示仍需继续轮询（RUNNING状态），false表示已完成或失败或无记录
            return entity.executeStatus === 'RUNNING';
          } else {
            this.aiAnalysisContent = null;
            this.parseAiResultText('');
            return false;
          }
        } catch (e) {
          console.error('加载AI解读失败:', e);
          return false;
        } finally {
          this.aiAnalysisLoading = false;
        }
      },
  
      // 解析AI结果文本：提取<think>块为思考过程，其余为正文
      parseAiResultText(text) {
        try {
          const pattern = /<think>([\s\S]*?)<\/think>/i;
          const match = typeof text === 'string' ? text.match(pattern) : null;
          if (match && match[1] != null) {
            this.aiThinkText = match[1].trim();
            this.aiAnswerText = text.replace(pattern, '').trim();
          } else {
            this.aiThinkText = '';
            this.aiAnswerText = (text || '').trim();
          }
        } catch (e) {
          this.aiThinkText = '';
          this.aiAnswerText = (text || '').trim();
        }
      },
  
      // 将正文中的 **XX** 转为 <strong>XX</strong>，并将"趋势分析/异常检测/优化建议/风险评估"加粗+字号+2px
      aiAnswerHtml() {
        const text = this.aiAnswerText || '';
        const raw = marked.parse(text, { breaks: true, gfm: true });
        const safe = DOMPurify.sanitize(raw);
        return safe;
      },
  
      // 格式化数字
      formatNumber(value) {
        if (!value) return '0';
        return parseFloat(value).toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });
      },
  
      // 格式化增长率
      formatGrowthRate(rate) {
        if (rate > 0) {
          return `同比增长 ${rate.toFixed(1)}%`;
        } else if (rate < 0) {
          return `同比下降 ${Math.abs(rate).toFixed(1)}%`;
        } else {
          return '与去年同期持平';
        }
      },
  
      // 获取趋势样式类
      getTrendClass(rate) {
        if (rate > 0) return 'up';
        if (rate < 0) return 'down';
        return 'normal';
      },
  
      // 获取趋势图标
      getTrendIcon(rate) {
        if (rate > 0) return 'el-icon-arrow-up';
        if (rate < 0) return 'el-icon-arrow-down';
        return 'el-icon-minus';
      },
  
      // 获取状态标签类型
      getStatusTagType(status) {
        const statusMap = {
          'COMPLETED': 'success',
          'PROCESSING': 'warning',
          'PENDING': 'info',
          'FAILED': 'danger'
        };
        return statusMap[status] || 'info';
      },
  
      // 获取状态文本
      getStatusText(status) {
        const statusMap = {
          'COMPLETED': '已完成',
          'PROCESSING': '计算中',
          'PENDING': '待计算',
          'FAILED': '失败'
        };
        return statusMap[status] || status;
      }
    }
  };
  </script>
  
  <style scoped>
  .filter-panel {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
  
  /* 科技感统计面板样式 */
  .tech-dashboard {
    margin-bottom: 20px;
  }
  
  .main-stats-panel {
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 12px;
    padding: 24px;
    color: #303133;
    position: relative;
    overflow: visible;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .main-stats-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(64,158,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }
  
  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    position: relative;
    z-index: 1;
  }
  
  .dashboard-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .title-icon {
    font-size: 28px;
  }
  
  .update-time {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
  
  .ai-analysis-btn {
    background: #409eff;
    border: 1px solid #409eff;
    color: white;
    border-radius: 8px;
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  
  .ai-analysis-btn:hover {
    background: #66b1ff;
    border-color: #66b1ff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-bottom: 24px;
    position: relative;
    z-index: 1;
    align-items: stretch;
    min-height: 260px;
    overflow: visible;
  }

  /* 新版KPI卡片样式（与资产负债页保持一致风格） */
  .kpi-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 18px 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.04);
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .kpi-card.accent {
    border-color: #409eff;
    background: linear-gradient(180deg, rgba(64,158,255,0.06), rgba(64,158,255,0.02));
  }
  .kpi-primary {
    grid-column: span 1;
    background: linear-gradient(135deg, #409eff, #66b1ff);
    color: #fff;
  }
  .kpi-title {
    font-size: 14px;
    opacity: 0.95;
  }
  .kpi-value {
    font-weight: 700;
    margin-top: 8px;
  }
  .kpi-percent {
    font-size: 34px;
    letter-spacing: 0.5px;
  }
  .kpi-sub {
    margin-top: 6px;
    font-size: 12px;
    opacity: 0.9;
  }
  .kpi-label { font-size: 12px; color: #909399; }
  .kpi-amount { margin-top: 6px; font-size: 20px; font-weight: 700; color: #303133; }
  
  .stat-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
  }
  
  .stat-item:hover {
    background: #e3f2fd;
    border-color: #409eff;
    transform: translateY(-4px);
    box-shadow: 0 4px 20px rgba(64, 158, 255, 0.1);
  }
  
  .primary-stat {
    grid-row: span 2;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
  }
  
  .stat-value-large {
    font-size: 36px;
    font-weight: 700;
    color: #409eff;
    margin: 16px 0;
  }
  
  .stat-value {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 8px 0;
  }
  
  .stat-bar {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 12px;
  }
  
  .bar-fill {
    height: 100%;
    border-radius: 2px;
    transition: width 0.8s ease;
  }
  
  .primary-bar {
    background: linear-gradient(90deg, #00f5ff, #0080ff);
  }
  
  .secondary-bar {
    background: linear-gradient(90deg, #ff6b6b, #ffa726);
  }
  
  .tertiary-bar {
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
  }
  
  .comparison-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: auto;
  }
  
  .secondary-stat {
    padding: 16px !important;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 120px;
  }
  
  .annual-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: auto;
  }
  
  .annual-stats .secondary-stat {
    min-height: 120px;
  }
  
  .annual-stats .secondary-stat:first-child {
    justify-content: flex-start;
  }
  
  .annual-stats .secondary-stat:first-child .stat-value {
    margin: 8px 0 12px 0;
  }
  
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .progress-label {
    font-size: 14px;
    color: #606266;
  }
  
  .progress-percentage {
    font-size: 18px;
    font-weight: 600;
    color: #409eff;
  }
  
  .progress-track {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
  }
  
  .progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease;
  }
  
  .progress-success { background: linear-gradient(90deg, #4facfe, #00f2fe); }
  .progress-info { background: linear-gradient(90deg, #43e97b, #38f9d7); }
  .progress-warning { background: linear-gradient(90deg, #fa709a, #fee140); }
  .progress-danger { background: linear-gradient(90deg, #ff9a9e, #fecfef); }
  
  .progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }
  
  /* 定向覆盖：仅卡片内文本信息条，背景透明、黑字、字号与剩余预算一致 */
  .annual-stats .secondary-stat > .progress-info {
    background: transparent;
    color: #000000;
    font-size: 14px;
  }
  
  .metrics-row {
    display: flex;
    justify-content: center;
    gap: 16px;
    position: relative;
    z-index: 1;
  }
  
  .metric-item {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.3s ease;
    flex: 1;
    max-width: 300px;
  }
  
  .metric-item:hover {
    background: #e3f2fd;
    border-color: #409eff;
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(64, 158, 255, 0.1);
  }
  
  .metric-icon {
    font-size: 24px;
    margin-right: 12px;
  }
  
  .metric-content {
    flex: 1;
  }
  
  .metric-label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }
  
  .metric-value {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
  
  .metric-status {
    font-size: 14px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
  }
  
  .budget-normal { color: #4ade80; }
  .budget-warning { color: #fbbf24; }
  .budget-critical { color: #ef4444; }
  
  .status-good { background: rgba(74, 222, 128, 0.2); color: #4ade80; }
  .status-normal { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
  .status-warning { background: rgba(251, 191, 36, 0.2); color: #fbbf24; }
  .status-critical { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
  
  .stat-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
  }
  
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 10px;
  }
  
  .stat-trend {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }
  
  .stat-trend.up { color: #f56c6c; }
  .stat-trend.down { color: #67c23a; }
  .stat-trend.normal { color: #909399; }
  
  .stat-desc {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
  }
  
  .stat-progress {
    margin-top: 10px;
  }
  
  .expense-table-panel,
  .warning-panel {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
  }
  
  .panel-header h4 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }
  
  .panel-actions {
    display: flex;
    gap: 10px;
  }
  
  .expense-abnormal {
    color: #f56c6c;
    font-weight: bold;
  }
  
  .warning-badge {
    position: relative;
  }
  
  .warning-list {
    max-height: 300px;
    overflow-y: auto;
  }
  
  .warning-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
  }
  
  .warning-item:hover {
    background-color: #f5f7fa;
    border-color: #409eff;
  }
  
  .warning-content {
    flex: 1;
  }
  
  .warning-title {
    font-size: 14px;
    color: #303133;
    margin-bottom: 5px;
  }
  
  .warning-desc {
    font-size: 12px;
    color: #666;
  }
  
  .warning-level {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
  }
  
  .warning-level.level-high {
    background: #fef0f0;
    color: #f56c6c;
  }
  
  .warning-level.level-medium {
    background: #fdf6ec;
    color: #e6a23c;
  }
  
  .warning-level.level-low {
    background: #f0f9ff;
    color: #409eff;
  }
  
  /* 分析弹窗样式 */
  .analysis-content {
    padding: 10px 0;
  }
  
  .expense-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
  }
  
  .expense-info h4 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 18px;
  }
  
  .expense-amount {
    font-size: 16px;
    font-weight: bold;
    color: #e6a23c;
    margin: 5px 0;
  }
  
  .expense-desc {
    color: #666;
    margin: 5px 0 0 0;
  }
  
  .trend-chart-section {
    margin-bottom: 25px;
  }
  
  .trend-chart-section h4 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 16px;
  }
  
  .mock-chart {
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    padding: 20px;
  }
  
  .chart-placeholder {
    text-align: center;
    color: #666;
    font-size: 14px;
  }
  
  .trend-data {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    margin-top: 20px;
    height: 120px;
  }
  
  .trend-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
  
  .trend-item .month {
    font-size: 12px;
    color: #666;
  }
  
  .trend-item .bar {
    width: 30px;
    border-radius: 3px 3px 0 0;
    transition: all 0.3s;
  }
  
  .trend-item .amount {
    font-size: 12px;
    font-weight: bold;
    color: #303133;
  }
  
  .ai-analysis-section h4 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 16px;
  }
  
  .ai-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .ai-title {
    margin: 0;
  }
  .ai-status-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .ai-status-hint {
    margin-left: 8px;
    color: #909399;
  }
  .ai-answer :deep(.ai-sec-title) {
    font-weight: 700;
    font-size: 18px; /* 深度选择器，作用于 v-html 注入的内容 */
    display: inline-block;
    margin-top: 8px;
  }
  .ai-answer {
    white-space: pre-wrap;
    word-break: break-word;
    margin-top: 8px;
  }
  
  .ai-content {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
  }
  /* 强制重置 markdown 元素的上下外边距为 0（在 AI 内容容器内生效） */
.ai-content :deep(h1),
.ai-content :deep(h2),
.ai-content :deep(h3),
.ai-content :deep(h4),
.ai-content :deep(h5),
.ai-content :deep(h6),
.ai-content :deep(p),
.ai-content :deep(ul),
.ai-content :deep(ol),
.ai-content :deep(li),
.ai-content :deep(pre),
.ai-content :deep(code),
.ai-content :deep(blockquote),
.ai-content :deep(table),
.ai-content :deep(thead),
.ai-content :deep(tbody),
.ai-content :deep(tr),
.ai-content :deep(th),
.ai-content :deep(td) {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
  .analysis-item {
    margin-bottom: 15px;
  }
  
  .analysis-item:last-child {
    margin-bottom: 0;
  }
  
  .analysis-label {
    font-weight: bold;
    color: #303133;
    margin-bottom: 5px;
    font-size: 14px;
  }
  
  .analysis-text {
    color: #666;
    line-height: 1;
  }
  
  /* 思考过程折叠样式 */
  .think-box {
    border: 1px dashed #dcdfe6;
    border-radius: 6px;
    background: #fafafa;
  }
  .think-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    cursor: pointer;
    color: #606266;
  }
  .think-header:hover {
    background: #f2f6fc;
  }
  .think-content {
    padding: 8px 12px 12px 12px;
  }
  
  .risk-low {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .collapse-btn {
    margin-left: 10px;
    background: transparent;
    border: 1px solid #d9d9d9;
    color: #409eff;
    font-size: 16px;
    font-weight: bold;
    width: 28px;
    height: 28px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
  
    align-items: center;
    justify-content: center;
    line-height: 1;
  }
  
  .collapse-btn:hover {
    background: #f0f8ff;
    border-color: #409eff;
    color: #409eff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }
  </style>
  