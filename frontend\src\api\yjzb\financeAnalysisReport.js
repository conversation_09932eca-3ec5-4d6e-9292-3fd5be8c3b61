import request from '@/axios';

/**
 * 分页查询财务分析报告列表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export const getReportList = (params) => {
  return request({
    url: '/yjzb/finance-analysis-report/list',
    method: 'get',
    params
  });
}

/**
 * 获取报告统计信息
 * @returns {Promise} API响应
 */
export const getReportStatistics = () => {
  return request({
    url: '/yjzb/finance-analysis-report/statistics',
    method: 'get'
  });
}

/**
 * 查看报告详情
 * @param {number} id - 报告ID
 * @returns {Promise} API响应
 */
export const getReportDetail = (id) => {
  return request({
    url: `/yjzb/finance-analysis-report/detail/${id}`,
    method: 'get'
  });
}

/**
 * 生成新报告
 * @param {Object} data - 生成报告参数
 * @returns {Promise} API响应
 */
export const generateReport = (data) => {
  return request({
    url: '/yjzb/finance-analysis-report/generate',
    method: 'post',
    data
  });
}

/**
 * 删除报告
 * @param {string} ids - 报告ID集合
 * @returns {Promise} API响应
 */
export const removeReports = (ids) => {
  return request({
    url: '/yjzb/finance-analysis-report/remove',
    method: 'post',
    params: { ids }
  });
}

/**
 * 下载报告
 * @param {number} id - 报告ID
 * @returns {Promise} API响应
 */
export const downloadReport = (id) => {
  return request({
    url: `/yjzb/finance-analysis-report/download/${id}`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 重试生成报告
 * @param {number} id - 报告ID
 * @returns {Promise} API响应
 */
export const retryGenerateReport = (id) => {
  return request({
    url: `/yjzb/finance-analysis-report/retry/${id}`,
    method: 'post'
  });
}
