package org.springblade.modules.yjzb.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorForecastVO;

import java.util.Objects;

public class IndicatorForecastWrapper extends BaseEntityWrapper<IndicatorForecastEntity, IndicatorForecastVO> {

    public static IndicatorForecastWrapper build() {
        return new IndicatorForecastWrapper();
    }

    @Override
    public IndicatorForecastVO entityVO(IndicatorForecastEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, IndicatorForecastVO.class);
    }

    public IPage<IndicatorForecastVO> pageVO(IPage<IndicatorForecastEntity> pages) {
        return pages.convert(this::entityVO);
    }
}
