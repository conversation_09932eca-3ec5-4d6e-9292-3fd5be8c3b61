/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.pojo.vo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import java.io.Serial;

/**
 * 指标数据明细 视图实体类
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
@Schema(description = "IndicatorValuesDetailVO对象")
@EqualsAndHashCode(callSuper = true)
public class IndicatorValuesDetailVO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 指标类型ID（关联指标类型表）
     */
    @Schema(description = "指标类型ID（关联指标类型表）")
    private Long indicatorId;

    /**
     * 指标类型名称
     */
    @Schema(description = "指标类型名称")
    private String indicatorName;

    /**
     * 数据期间（格式：YYYY-MM）
     */
    @Schema(description = "数据期间（格式：YYYY-MM）")
    private String period;

    /**
     * 凭证号
     */
    @Schema(description = "凭证号")
    private String voucherNo;

    /**
     * 数据摘要
     */
    @Schema(description = "数据摘要")
    private String dataSummary;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 分类
     */
    @Schema(description = "分类")
    private String category;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private Long createUser;

    /**
     * 创建用户名称
     */
    @Schema(description = "创建用户名称")
    private String createUserName;

    /**
     * 创建部门ID
     */
    @Schema(description = "创建部门ID")
    private Long createDept;

    /**
     * 创建部门名称
     */
    @Schema(description = "创建部门名称")
    private String createDeptName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新用户ID
     */
    @Schema(description = "更新用户ID")
    private Long updateUser;

    /**
     * 更新用户名称
     */
    @Schema(description = "更新用户名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 状态（1-正常，0-禁用）
     */
    @Schema(description = "状态（1-正常，0-禁用）")
    private Integer status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 是否删除（1-已删除，0-未删除）
     */
    @Schema(description = "是否删除（1-已删除，0-未删除）")
    private Integer isDeleted;

}
