import request from '@/axios';

/**
 * 办公费用AI解读分析API
 */

// 执行AI分析
export const executeExpenseAnalysis = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/execute',
    method: 'post',
    params: params
  });
};

// 获取AI分析结果
export const getExpenseAnalysisResult = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/result',
    method: 'get',
    params: params
  });
};

// 检查是否有缓存结果
export const hasCachedExpenseAnalysis = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/has-cache',
    method: 'get',
    params: params
  });
};

// 执行费用预测
export const executeExpenseForecast = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/forecast',
    method: 'post',
    params: params
  });
};

// 获取预测结果
export const getExpenseForecastResult = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/forecast-result',
    method: 'get',
    params: params
  });
};
