# 办公费用AI解读分析功能说明

## 功能概述

本功能实现了办公费用深度分析的AI解读，将原来直接调用Dify API的方式改为后端存储模式，提高性能和用户体验。

## 主要特性

1. **后端存储**：AI分析结果存储在数据库中，避免重复分析
2. **缓存机制**：相同参数的分析结果直接从数据库读取
3. **MLOps集成**：费用预测调用MLOps接口
4. **异步处理**：支持长时间运行的AI分析任务

## 数据库表结构

### yjzb_expense_ai_analysis（办公费用AI解读分析表）
- `id`: 主键
- `indicator_id`: 指标ID
- `period`: 期间
- `analysis_type`: 分析类型（overview, structure, trend, volatility, growth, budget, forecast）
- `input_params`: 输入参数（JSON格式）
- `execute_time`: 执行时间
- `execute_status`: 执行状态（PENDING, RUNNING, COMPLETED, FAILED）
- `result`: AI分析结果
- `think_process`: AI思考过程
- `answer_content`: AI回答内容
- `workflow_run_id`: Dify工作流运行ID

### yjzb_expense_forecast（办公费用预测表）
- `id`: 主键
- `indicator_id`: 指标ID
- `forecast_period`: 预测期间
- `forecast_type`: 预测类型（monthly, quarterly, yearly）
- `forecast_value`: 预测值
- `confidence_level`: 置信度
- `mlops_request_id`: MLOps请求ID
- `input_data`: 输入数据（JSON格式）

## API接口

### 1. 执行AI分析
```
POST /yjzb/expense-ai-analysis/execute
参数：
- indicatorId: 指标ID
- period: 期间
- analysisType: 分析类型
- inputParams: 输入参数（JSON字符串）
```

### 2. 获取AI分析结果
```
GET /yjzb/expense-ai-analysis/result
参数：
- indicatorId: 指标ID
- period: 期间
- analysisType: 分析类型
```

### 3. 检查缓存结果
```
GET /yjzb/expense-ai-analysis/has-cache
参数：
- indicatorId: 指标ID
- period: 期间
- analysisType: 分析类型
```

### 4. 执行费用预测
```
POST /yjzb/expense-ai-analysis/forecast
参数：
- indicatorId: 指标ID
- forecastPeriod: 预测期间
- forecastType: 预测类型
- inputData: 输入数据（JSON字符串）
```

### 5. 获取预测结果
```
GET /yjzb/expense-ai-analysis/forecast-result
参数：
- indicatorId: 指标ID
- forecastPeriod: 预测期间
- forecastType: 预测类型
```

## 配置说明

### 1. Dify配置
在 `application.yml` 中配置：
```yaml
dify:
  api:
    key: your_dify_api_key
```

### 2. MLOps配置
在 `application.yml` 中配置：
```yaml
mlops:
  predict:
    url: http://your-mlops-server/predict
    jobid: your_job_id
    modelid: your_model_id
```

## 使用流程

1. **前端调用**：前端调用 `/execute` 接口执行AI分析
2. **缓存检查**：后端首先检查是否有缓存结果
3. **Dify调用**：如果没有缓存，调用Dify API进行分析
4. **结果存储**：将分析结果存储到数据库
5. **结果返回**：返回分析结果给前端

## 分析类型说明

- `overview`: 概览分析
- `structure`: 结构分析
- `trend`: 趋势分析
- `volatility`: 波动性分析
- `growth`: 增长分析
- `budget`: 预算分析
- `forecast`: 预测分析

## 注意事项

1. 确保数据库表已创建
2. 配置正确的Dify API Key
3. 配置正确的MLOps服务地址
4. 前端需要更新API调用方式

## 故障排除

1. **ScheduledExecutorService错误**：检查ExpenseAiAnalysisConfig配置
2. **Dify调用失败**：检查API Key配置和网络连接
3. **MLOps调用失败**：检查MLOps服务配置和网络连接
4. **数据库连接失败**：检查数据库配置和表结构
