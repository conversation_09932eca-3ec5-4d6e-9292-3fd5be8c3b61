/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import org.springblade.modules.yjzb.pojo.entity.IndicatorEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorVO;
import org.springblade.modules.yjzb.excel.IndicatorExcel;
import org.springblade.modules.yjzb.wrapper.IndicatorWrapper;
import org.springblade.modules.yjzb.service.IIndicatorService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 指标 控制器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/indicator")
@Tag(name = "指标", description = "指标接口")
public class IndicatorController extends BladeController {

    private final IIndicatorService indicatorService;

    /**
     * 指标 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入indicator")
    public R<IndicatorVO> detail(IndicatorEntity indicator) {
        IndicatorEntity detail = indicatorService.getOne(Condition.getQueryWrapper(indicator));
        return R.data(IndicatorWrapper.build().entityVO(detail));
    }

    /**
     * 指标 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入indicator")
    public R<IPage<IndicatorVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> indicator,
            Query query) {
        // 针对PostgreSQL数据库处理查询条件，避免bigint字段LIKE查询类型不匹配错误
        IndicatorWrapper.build().indicatorQuery(indicator);
        IPage<IndicatorEntity> pages = indicatorService.page(Condition.getPage(query),
                Condition.getQueryWrapper(indicator, IndicatorEntity.class));
        return R.data(IndicatorWrapper.build().pageVO(pages));
    }

    /**
     * 指标 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入indicator")
    public R<IPage<IndicatorVO>> page(IndicatorVO indicator, Query query) {
        IPage<IndicatorVO> pages = indicatorService.selectIndicatorPage(Condition.getPage(query), indicator);
        return R.data(pages);
    }

    /**
     * 指标 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入indicator")
    public R save(@Valid @RequestBody IndicatorEntity indicator) {
        return R.status(indicatorService.save(indicator));
    }

    /**
     * 指标 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入indicator")
    public R update(@Valid @RequestBody IndicatorEntity indicator) {
        return R.status(indicatorService.updateById(indicator));
    }

    /**
     * 指标 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入indicator")
    public R submit(@Valid @RequestBody IndicatorEntity indicator) {
        return R.status(indicatorService.saveOrUpdate(indicator));
    }

    /**
     * 指标 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(indicatorService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 导出数据
     */
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    @GetMapping("/export-indicator")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "导出数据", description = "传入indicator")
    public void exportIndicator(@Parameter(hidden = true) @RequestParam Map<String, Object> indicator,
            BladeUser bladeUser, HttpServletResponse response) {
        // 针对PostgreSQL数据库处理查询条件，避免bigint字段LIKE查询类型不匹配错误
        IndicatorWrapper.build().indicatorQuery(indicator);
        QueryWrapper<IndicatorEntity> queryWrapper = Condition.getQueryWrapper(indicator, IndicatorEntity.class);
        // if (!AuthUtil.isAdministrator()) {
        // queryWrapper.lambda().eq(Indicator::getTenantId, bladeUser.getTenantId());
        // }
        // queryWrapper.lambda().eq(IndicatorEntity::getIsDeleted,
        // BladeConstant.DB_NOT_DELETED);
        List<IndicatorExcel> list = indicatorService.exportIndicator(queryWrapper);
        ExcelUtil.export(response, "指标数据" + DateUtil.time(), "指标数据表", list, IndicatorExcel.class);
    }

    /**
     * 下载数据导入模板
     */
    @GetMapping("/download-template/{templateType}")
    @ApiOperationSupport(order = 10)
    @Operation(summary = "下载数据导入模板", description = "templateType: profit(利润表), balance(资产负债表), expense(三项费用), tax(税利指标明细表)")
    public ResponseEntity<Resource> downloadTemplate(@PathVariable String templateType) {
        String fileName;
        switch (templateType) {
            case "profit":
                fileName = "利润表数据模板.csv";
                break;
            case "balance":
                fileName = "资产负债表数据模板.csv";
                break;
            case "expense":
                fileName = "三项费用数据模板.csv";
                break;
            case "tax":
                fileName = "税利指标明细表数据模板.csv";
                break;
            default:
                return ResponseEntity.notFound().build();
        }

        try {
            Resource resource = new ClassPathResource("templates/" + fileName);
            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }

            String encodedFileName = java.net.URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName)
                    .body(resource);
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }

}
