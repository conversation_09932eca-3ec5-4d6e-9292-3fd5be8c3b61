<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.IndicatorAnnualBudgetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indicatorAnnualBudgetResultMap" type="org.springblade.modules.yjzb.pojo.entity.IndicatorAnnualBudgetEntity">
        <result column="id" property="id"/>
        <result column="indicator_id" property="indicatorId"/>
        <result column="indicator_name" property="indicatorName"/>
        <result column="year" property="year"/>
        <result column="initial_budget" property="initialBudget"/>
        <result column="initial_reported" property="initialReported"/>
        <result column="midyear_budget" property="midyearBudget"/>
        <result column="midyear_reported" property="midyearReported"/>
        <result column="current_used" property="currentUsed"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="selectIndicatorAnnualBudgetPage" resultMap="indicatorAnnualBudgetResultMap">
        select * from yjzb_indicator_annual_budget where is_deleted = 0
    </select>

    <select id="exportIndicatorAnnualBudget" resultType="org.springblade.modules.yjzb.excel.IndicatorAnnualBudgetExcel">
        SELECT * FROM yjzb_indicator_annual_budget ${ew.customSqlSegment}
    </select>

    <!-- 按指标与年份获取年度预算 -->
    <select id="findByIndicatorAndYear" resultMap="indicatorAnnualBudgetResultMap">
        SELECT *
        FROM yjzb_indicator_annual_budget
        WHERE is_deleted = 0
          AND indicator_id = #{indicatorId}
          AND year = #{year}
        LIMIT 1
    </select>

    <!-- 按指标类型与年份汇总年度预算：优先 midyear_budget，否则 initial_budget -->
    <select id="sumBudgetByTypeAndYear" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(
                       CASE
                           WHEN yab.midyear_budget IS NOT NULL AND yab.midyear_budget != 0 THEN yab.midyear_budget
                           WHEN yab.initial_budget IS NOT NULL THEN yab.initial_budget
                           ELSE 0
                       END
                   ),0) AS total_budget
        FROM yjzb_indicator_annual_budget yab
        JOIN yjzb_indicator i ON i.id = yab.indicator_id
        WHERE yab.is_deleted = 0
          AND yab.year = #{year}
          AND i.indicator_type_id = #{indicatorTypeId}
    </select>

</mapper>
