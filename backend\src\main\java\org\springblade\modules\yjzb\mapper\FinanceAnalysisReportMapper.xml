<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.FinanceAnalysisReportMapper">

    <!-- 财务分析报告列表结果映射 -->
    <resultMap id="financeAnalysisReportResultMap" type="org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisReportEntity">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="period" property="period"/>
        <result column="query_year" property="queryYear"/>
        <result column="compare_year" property="compareYear"/>
        <result column="start_month" property="startMonth"/>
        <result column="end_month" property="endMonth"/>
        <result column="report_status" property="reportStatus"/>
        <result column="file_path" property="filePath"/>
        <result column="file_name" property="fileName"/>
        <result column="file_size" property="fileSize"/>
        <result column="download_count" property="downloadCount"/>
        <result column="generate_time" property="generateTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="error_message" property="errorMessage"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 分页查询财务分析报告列表 -->
    <select id="selectReportPage" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisReportVO">
        SELECT 
            r.*,
            CASE
                WHEN r.report_status = 'generating' THEN '生成中'
                WHEN r.report_status = 'completed' THEN '已完成'
                WHEN r.report_status = 'failed' THEN '失败'
                ELSE r.report_status
            END as statusName,
            CASE 
                WHEN r.type = 'finance' THEN '财务分析'
                WHEN r.type = 'tax' THEN '税务分析'
                WHEN r.type = 'expense' THEN '费用分析'
                WHEN r.type = 'budget' THEN '预算分析'
                WHEN r.type = 'risk' THEN '风险分析'
                ELSE r.type
            END as typeName,
            CASE 
                WHEN r.file_size IS NULL THEN ''
                WHEN r.file_size &lt; 1024 THEN CONCAT(r.file_size, 'B')
                WHEN r.file_size &lt; 1048576 THEN CONCAT(ROUND(r.file_size/1024, 1), 'KB')
                WHEN r.file_size &lt; 1073741824 THEN CONCAT(ROUND(r.file_size/1048576, 1), 'MB')
                ELSE CONCAT(ROUND(r.file_size/1073741824, 1), 'GB')
            END as fileSizeFormatted,
            u.real_name as createUserName,
            CASE 
                WHEN r.complete_time IS NOT NULL AND r.generate_time IS NOT NULL 
                THEN EXTRACT(EPOCH FROM (r.complete_time - r.generate_time))::INT
                ELSE NULL
            END as generateDuration
        FROM yjzb_finance_analysis_report r
        LEFT JOIN blade_user u ON r.create_user = u.id
        <where>
            r.is_deleted = 0
            <if test="report.title != null and report.title != ''">
                AND r.title LIKE CONCAT('%', #{report.title}, '%')
            </if>
            <if test="report.type != null and report.type != ''">
                AND r.type = #{report.type}
            </if>
            <if test="report.reportStatus != null and report.reportStatus != ''">
                AND r.report_status = #{report.reportStatus}
            </if>
            <if test="report.queryYear != null">
                AND r.query_year = #{report.queryYear}
            </if>
            <if test="report.startMonth != null">
                AND r.start_month = #{report.startMonth}
            </if>
            <if test="report.endMonth != null">
                AND r.end_month = #{report.endMonth}
            </if>
        </where>
        ORDER BY r.generate_time DESC
    </select>

    <!-- 获取报告统计信息 -->
    <select id="getReportStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as total,
            COUNT(CASE WHEN report_status = 'completed' AND EXTRACT(YEAR FROM generate_time) = EXTRACT(YEAR FROM NOW()) THEN 1 END) as thisYear,
            COUNT(CASE WHEN report_status = 'generating' THEN 1 END) as generating,
            COUNT(CASE WHEN report_status = 'failed' THEN 1 END) as failed
        FROM yjzb_finance_analysis_report
        WHERE is_deleted = 0
    </select>

    <!-- 根据ID查询详情 -->
    <select id="selectReportDetail" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisReportVO">
        SELECT
            r.*,
            CASE
                WHEN r.report_status = 'generating' THEN '生成中'
                WHEN r.report_status = 'completed' THEN '已完成'
                WHEN r.report_status = 'failed' THEN '失败'
                ELSE r.report_status
            END as statusName,
            CASE
                WHEN r.type = 'finance' THEN '财务分析'
                WHEN r.type = 'tax' THEN '税务分析'
                WHEN r.type = 'expense' THEN '费用分析'
                WHEN r.type = 'budget' THEN '预算分析'
                WHEN r.type = 'risk' THEN '风险分析'
                ELSE r.type
            END as typeName,
            u.real_name as createUserName
        FROM yjzb_finance_analysis_report r
        LEFT JOIN blade_user u ON r.create_user = u.id
        WHERE r.id = #{id} AND r.is_deleted = 0
    </select>

    <!-- 更新下载次数 -->
    <update id="updateDownloadCount">
        UPDATE yjzb_finance_analysis_report 
        SET download_count = download_count + 1,
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据状态统计报告数量 -->
    <select id="countByStatus" resultType="int">
        SELECT COUNT(*)
        FROM yjzb_finance_analysis_report
        WHERE report_status = #{status} AND is_deleted = 0
    </select>

    <!-- 获取本年完成的报告数量 -->
    <select id="countCompletedByYear" resultType="int">
        SELECT COUNT(*)
        FROM yjzb_finance_analysis_report
        WHERE report_status = 'completed'
        AND EXTRACT(YEAR FROM generate_time) = #{year}
        AND is_deleted = 0
    </select>

</mapper>
