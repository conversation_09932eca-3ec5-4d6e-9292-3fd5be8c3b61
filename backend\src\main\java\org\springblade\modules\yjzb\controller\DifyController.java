/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.modules.yjzb.pojo.dto.DifyWorkflowRunRequest;
import org.springblade.modules.yjzb.service.IDifyService;
import org.springframework.web.bind.annotation.*;

/**
 * Dify 调试接口
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/yjzb/dify")
@Tag(name = "Dify 调试", description = "工作流执行与调试接口")
public class DifyController extends BladeController {

    private final IDifyService difyService;

    // 仅保留 streaming 启动与执行详情两个接口

    /**
     * 直接以 streaming 模式启动工作流，立即返回 Dify 返回的数据（通常包含 workflow_run_id / task_id）
     */
    @PostMapping("/workflow/run-streaming-once")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "启动Workflow(Streaming)", description = "直接调用 Dify streaming，立即返回包含 workflow_run_id 的响应体")
    public R<String> runStreamingOnce(@Valid @RequestBody DifyWorkflowRunRequest req) {
        String result = difyService.startWorkflowStreaming(req.getInputs(), req.getUser(),
                "app-hxnjbQRuY3npPCawk9ksGQ30");
        if (result == null) {
            return R.fail("启动失败");
        }
        return R.data(result);
    }

    /**
     * 查询 workflow 执行详情（透传到 Dify GET /v1/workflows/run/{workflow_run_id}）
     */
    @GetMapping("/workflow/run/detail/{workflowRunId}")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "查询workflow执行详情", description = "使用 Dify 的 GET /v1/workflows/run/{workflow_run_id}")
    public R<String> getWorkflowRunDetail(@PathVariable String workflowRunId) {
        String result = difyService.getWorkflowRunDetail(workflowRunId, "app-hxnjbQRuY3npPCawk9ksGQ30");
        if (result == null) {
            return R.fail("查询失败");
        }
        return R.data(result);
    }

}
