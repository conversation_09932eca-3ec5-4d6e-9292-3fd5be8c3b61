import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/yjzb/indicatorAnnualBudget/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/yjzb/indicatorAnnualBudget/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/yjzb/indicatorAnnualBudget/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/yjzb/indicatorAnnualBudget/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/yjzb/indicatorAnnualBudget/submit',
    method: 'post',
    data: row
  })
}

// 按指标与年份获取预算
export const getByIndicatorAndYear = (indicatorId, year) => {
  return request({
    url: '/yjzb/indicatorAnnualBudget/getByIndicatorAndYear',
    method: 'get',
    params: { indicatorId, year }
  })
}

// 按指标类型与年份汇总预算
export const sumByTypeAndYear = (indicatorTypeId, year) => {
  return request({
    url: '/yjzb/indicatorAnnualBudget/sumByTypeAndYear',
    method: 'get',
    params: { indicatorTypeId, year }
  })
}

