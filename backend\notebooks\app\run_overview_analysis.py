#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查询yjzb_indicator_types获取所有指标类型，并遍历执行调用restartOverviewWithPrompt方法
期间范围：2025-01至2025-07
overviewType：expense_overview、balance_overview、profit_overview
"""

import os
import psycopg2
import pandas as pd
import requests
import json
from datetime import datetime
from dotenv import load_dotenv
import time

# 加载环境变量
load_dotenv()

# 数据库连接配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'gzyc-nlp-2024.rwlb.rds.aliyuncs.com'),
    'port': os.getenv('DB_PORT', '1921'),
    'database': os.getenv('DB_NAME', 'yjyc'),
    'user': os.getenv('DB_USER', 'gzyc'),
    'password': os.getenv('DB_PASSWORD', 'gzyc1234')
}

# API配置
API_BASE_URL = os.getenv('API_BASE_URL', 'http://localhost:8088')


def connect_to_db():
    """
    连接到PostgreSQL数据库
    """
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            database=DB_CONFIG['database'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        return conn
    except Exception as e:
        print(f"数据库连接错误: {e}")
        return None


def get_all_indicator_types():
    """
    查询yjzb_indicator_types表获取所有指标类型
    """
    conn = connect_to_db()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        query = "SELECT id, type_name, type_code, description, data_type, unit FROM yjzb_indicator_types WHERE is_deleted = 0"
        cursor.execute(query)
        rows = cursor.fetchall()
        
        # 转换为DataFrame
        df = pd.DataFrame(rows, columns=['id', 'type_name', 'type_code', 'description', 'data_type', 'unit'])
        return df
    except Exception as e:
        print(f"查询指标类型错误: {e}")
        return None
    finally:
        conn.close()


def call_restart_overview_api(indicator_type_id, period, overview_type):
    """
    调用restartOverview接口
    """
    url = f"{API_BASE_URL}/yjzb/indicatorAiAnalysis/overview/restart"
    
    params = {
        'indicatorTypeId': indicator_type_id,
        'period': period,
        'overviewType': overview_type
    }
    
    try:
        response = requests.post(url, params=params)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                return result.get('data')
            else:
                print(f"API调用失败: {result.get('msg')}")
                return None
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"API调用异常: {e}")
        return None


def get_ai_analysis_status(ai_record_id):
    """
    查询AI分析记录的状态
    参考DifyServiceImpl.getWorkflowRunDetail方法实现
    """
    if not ai_record_id:
        return None
    
    # 使用DifyServiceImpl.getWorkflowRunDetail接口
    url = f"{API_BASE_URL}/yjzb/dify/workflow/run/{ai_record_id}"
    
    try:
        # 发送GET请求获取工作流执行详情
        response = requests.get(url)
        if response.status_code == 200:
            # 直接解析响应体，DifyServiceImpl.getWorkflowRunDetail返回的是原始响应体
            data = response.json()
            if data:
                # 根据Dify工作流状态映射到AI分析状态
                workflow_status = data.get('status')
                if workflow_status == 'succeeded' or workflow_status == 'completed':
                    return 'COMPLETED'
                elif workflow_status == 'failed':
                    return 'FAILED'
                elif workflow_status == 'error':
                    return 'ERROR'
                elif workflow_status == 'running' or workflow_status == 'pending':
                    return 'RUNNING'
                else:
                    # 对于未知状态，返回大写形式
                    return workflow_status.upper() if workflow_status else None
            return None
        else:
            print(f"查询AI分析状态失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"查询AI分析状态异常: {e}")
        return None


def main():
    # 获取所有指标类型
    indicator_types_df = get_all_indicator_types()
    
    if indicator_types_df is None:
        print("获取指标类型数据失败")
        return
    
    print(f"成功获取{len(indicator_types_df)}个指标类型")
    
    # 定义期间范围和总览类型
    periods = [f"2025-{month:02d}" for month in range(1, 8)]  # 2025-01至2025-07
    overview_types = ["expense_overview", "balance_overview", "profit_overview"]
    
    # 记录执行结果
    results = []
    
    # 遍历执行
    for _, indicator_type in indicator_types_df.iterrows():
        indicator_type_id = indicator_type['id']
        type_name = indicator_type['type_name']
        
        for period in periods:
            for overview_type in overview_types:
                print(f"执行: 指标类型={type_name}(ID={indicator_type_id}), 期间={period}, 总览类型={overview_type}")
                
                # 调用API
                ai_record_id = call_restart_overview_api(indicator_type_id, period, overview_type)
                
                # 初始化结果记录
                result = {
                    'indicator_type_id': indicator_type_id,
                    'type_name': type_name,
                    'period': period,
                    'overview_type': overview_type,
                    'ai_record_id': ai_record_id,
                    'status': 'SUCCESS' if ai_record_id else 'FAILED',
                    'ai_status': None,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # 轮询检查AI分析状态，直到完成或超时
                if ai_record_id:
                    print("开始轮询检查AI分析状态...")
                    max_retries = 30  # 最大重试次数
                    retry_interval = 10  # 重试间隔（秒）
                    completed = False
                    
                    for retry in range(max_retries):
                        status = get_ai_analysis_status(ai_record_id)
                        print(f"轮询次数: {retry+1}/{max_retries}, 状态: {status}")
                        
                        # 判断状态是否表示已完成
                        if status in ['COMPLETED', 'FAILED', 'ERROR']:
                            print(f"AI分析已完成，最终状态: {status}")
                            result['ai_status'] = status  # 更新结果中的AI状态
                            completed = True
                            break
                        elif status == 'RUNNING':
                            print(f"AI分析正在运行中...")
                        else:
                            print(f"AI分析状态未知: {status}")
                            if status:  # 如果返回了状态但不是预期的状态值
                                result['ai_status'] = status
                                completed = True
                                break
                        
                        # 等待一段时间后再次查询
                        time.sleep(retry_interval)
                    
                    if not completed:
                        print("AI分析超时，继续下一个任务")
                        result['ai_status'] = 'TIMEOUT'
                
                # 添加结果到列表
                results.append(result)
                
                print(f"结果: {'成功' if ai_record_id else '失败'}, AI记录ID: {ai_record_id}")
                print("-" * 80)
    
    # 转换结果为DataFrame并保存
    results_df = pd.DataFrame(results)
    results_df.to_csv('overview_analysis_results.csv', index=False, encoding='utf-8')
    print(f"执行完成，结果已保存到 overview_analysis_results.csv")


if __name__ == "__main__":
    main()