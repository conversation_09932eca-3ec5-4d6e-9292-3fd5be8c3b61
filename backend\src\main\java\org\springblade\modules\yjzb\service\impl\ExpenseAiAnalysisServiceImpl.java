package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.yjzb.entity.ExpenseAiAnalysis;
import org.springblade.modules.yjzb.mapper.ExpenseAiAnalysisMapper;
import org.springblade.modules.yjzb.service.ExpenseAiAnalysisService;
import org.springblade.modules.yjzb.service.IDifyService;
import org.springblade.modules.yjzb.service.IIndicatorDeepAnalysisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 办公费用AI解读分析服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExpenseAiAnalysisServiceImpl extends ServiceImpl<ExpenseAiAnalysisMapper, ExpenseAiAnalysis>
        implements ExpenseAiAnalysisService {

    private final IDifyService difyService;
    private final ObjectMapper objectMapper;
    private final IIndicatorDeepAnalysisService indicatorDeepAnalysisService;

    // 已去掉difyApiKey字段，不再使用，仅使用下面的agentkey

    @Value("${dify.api.agentkey.expenseanalysisagent:}")
    private String expenseAnalysisAgentKey;

    @Override
    public String executeAnalysis(Long indicatorId, String period, String analysisType, String inputParams,
            boolean force) {
        log.info("开始执行办公费用AI分析 - indicatorId: {}, period: {}, analysisType: {}", indicatorId, period, analysisType);

        // 检查是否有缓存结果（当未强制刷新时）
        if (!force && hasCachedResult(indicatorId, period, analysisType)) {
            log.info("发现缓存的分析结果，直接返回");
            ExpenseAiAnalysis cachedResult = getAnalysisResult(indicatorId, period, analysisType);
            return cachedResult.getAnswerContent();
        }

        // 创建分析记录
        ExpenseAiAnalysis analysis = new ExpenseAiAnalysis();
        analysis.setIndicatorId(indicatorId);
        analysis.setPeriod(period);
        analysis.setAnalysisType(analysisType);
        // 后端统一构造传入Agent的数据
        String dataTextForAgent;
        try {
            Map<String, Object> dataObj = buildDataForAnalysisType(indicatorId, period, analysisType);
            dataTextForAgent = objectMapper.writeValueAsString(dataObj);
        } catch (Exception e) {
            log.error("构造传入Agent的数据失败", e);
            dataTextForAgent = inputParams; // 兜底：仍然记录前端传入，避免丢失
        }
        analysis.setInputParams(dataTextForAgent);
        analysis.setExecuteTime(LocalDateTime.now());
        analysis.setExecuteStatus("RUNNING");

        // 保存到数据库
        save(analysis);

        try {
            // 调用Dify API进行AI分析（仅 data 与 prompt）
            String result = callDifyApi(analysisType, dataTextForAgent);

            // 更新分析结果
            analysis.setExecuteStatus("COMPLETED");
            analysis.setResult(result);
            analysis.setAnswerContent(result);
            analysis.setExecuteTime(LocalDateTime.now());

            updateById(analysis);

            log.info("AI分析完成 - analysisId: {}", analysis.getId());
            return result;

        } catch (Exception e) {
            log.error("AI分析失败", e);

            // 更新失败状态
            analysis.setExecuteStatus("FAILED");
            analysis.setResult("分析失败: " + e.getMessage());
            updateById(analysis);

            throw new RuntimeException("AI分析失败: " + e.getMessage());
        }
    }

    @Override
    public ExpenseAiAnalysis getAnalysisResult(Long indicatorId, String period, String analysisType) {
        LambdaQueryWrapper<ExpenseAiAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpenseAiAnalysis::getIndicatorId, indicatorId)
                .eq(ExpenseAiAnalysis::getPeriod, period)
                .eq(ExpenseAiAnalysis::getAnalysisType, analysisType)
                .eq(ExpenseAiAnalysis::getExecuteStatus, "COMPLETED")
                .orderByDesc(ExpenseAiAnalysis::getExecuteTime)
                .last("LIMIT 1");

        return getOne(queryWrapper);
    }

    @Override
    public boolean hasCachedResult(Long indicatorId, String period, String analysisType) {
        LambdaQueryWrapper<ExpenseAiAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpenseAiAnalysis::getIndicatorId, indicatorId)
                .eq(ExpenseAiAnalysis::getPeriod, period)
                .eq(ExpenseAiAnalysis::getAnalysisType, analysisType)
                .eq(ExpenseAiAnalysis::getExecuteStatus, "COMPLETED");

        return count(queryWrapper) > 0;
    }

    /**
     * 调用Dify API进行AI分析
     *
     * @param analysisType 分析类型
     * @param inputParams  输入参数
     * @return AI分析结果
     */
    private String callDifyApi(String analysisType, String inputParams) {
        try {
            log.info("调用Dify API(仅 data 与 prompt) - analysisType: {}", analysisType);

            // 构造仅包含 data 与 prompt 的 inputs
            // 上游已构造为合法JSON文本，这里直接传递
            String dataText = inputParams == null ? "" : inputParams;

            String prompt = buildPromptForAnalysisType(analysisType);
            Map<String, Object> inputs = new java.util.HashMap<>();
            inputs.put("data", dataText);
            inputs.put("prompt", prompt);

            // 获取API Key - 优先使用费用分析专用的agent key
            String apiKey = (expenseAnalysisAgentKey != null && !expenseAnalysisAgentKey.isBlank())
                    ? expenseAnalysisAgentKey
                    : null;

            // 启动Dify工作流 - 参数顺序：inputs, user, apiKey
            String workflowRunId = difyService.startWorkflowStreaming(inputs, "expense_analysis", apiKey);
            if (workflowRunId == null) {
                log.error("启动Dify工作流失败，analysisType={}", analysisType);
                throw new RuntimeException("启动Dify工作流失败");
            }

            log.info("启动Dify工作流成功 - analysisType: {}, workflowRunId: {}", analysisType, workflowRunId);

            // 轮询获取结果
            return pollDifyResult(workflowRunId, apiKey);

        } catch (Exception e) {
            log.error("调用Dify API失败", e);
            throw new RuntimeException("调用Dify API失败: " + e.getMessage());
        }
    }

    /**
     * 根据分析类型生成提示词
     */
    private String buildPromptForAnalysisType(String analysisType) {
        String common = "你是一名资深财务分析师。系统会通过 inputs 传入一个 JSON 字符串 data（包含 indicatorId、period 及各章节对应的数据结构），" +
                "请严格基于 data 做数据驱动的分析解读；不得臆造数据。所有结论必须以‘当前月份’为主体，历史仅作参照基线，禁止逐条点评历史月份本身的风险。\n" +
                "请先在<think>…</think>中简要推理，再输出 Markdown 正文，正文必须包含以下结构（全文不少于300字，优先围绕当前月份给出判断）：\n" +
                "\n" +
                "## 本月详细结论解读\n" +
                "- 围绕‘现状-原因-影响-建议’的逻辑，针对当前月份给出判断；必须引用与当月相关的具体数字和对比（如当前值、同比/环比、历史均值/标准差、分位、Z分数、控制界限、预算偏差等）。\n" +
                "- 如涉及金额，请统一以‘¥’符号展示；百分比保留1位小数。\n" +
                "\n" +
                "## 风险与异常\n" +
                "- 判断本月是否异常/有风险，并给出评级（低/中/高），同时写明量化判据（是否越过控制界限、|Z|阈值、增速偏离历史均值等）。历史月份仅可用于对比，不要单独点评其风险。\n" +
                "\n" +
                "## 建议措施\n" +
                "- 给出1~3条可执行的措施（每条尽量量化触发阈值或跟踪指标）。\n" +
                "\n" +
                "> 输出规则：\n" +
                "> - 仅使用 data 中可验证的信息，不要输出‘无法确定的数据来源’。\n" +
                "> - 若 data 缺失或为空，也必须基于已存在字段给出解释与建议，禁止返回空内容或仅一句‘无数据’。\n" +
                "> - 数字后加单位（万元/元、%）；金额默认按‘元’展示。\n";

        String focus;
        switch (analysisType) {
            case "overview":
                focus = "章节侧重点：给出总体费用概览，突出本月费用、预算执行率、Z分数、历史分位/排名等；用最少的篇幅呈现最重要的现象与结论。";
                break;
            case "structure":
                focus = "章节侧重点：分析费用结构（各类别占比/金额TopN），识别集中度、结构变化对总费用的影响，并提出结构优化方向。";
                break;
            case "trend":
                focus = "章节侧重点：从时间序列角度解读趋势方向、拐点、季节性；必要时结合均值/标准差/分位进行佐证，并用具体月份举例。";
                break;
            case "volatility":
                focus = "章节侧重点：以当月为主体评估波动性，结合历史均值/标准差/控制界限判断‘当月是否异常’。\n" +
                        "数据字段指引：使用 data.volatility.indicators（currentValue、currentDeviationRate、currentZScore、historicalMean 等）与 data.volatility.controlLimits（upper/lower），必要时参考 monthlyData 中 isCurrentMonth=true 的点。\n"
                        +
                        "异常判定建议：当月值越过控制界限或 |当月Z分数| ≥ 2 可判为异常；输出‘本月风险评级：低/中/高’并给出量化判据。严禁逐条点评历史月份，仅将历史用于对比。";
                break;
            case "growth":
                focus = "章节侧重点：以当月为主体解析增速（MoM/YoY），判断当月增速相对历史均值/标准差是否异常，并解释驱动因素。\n" +
                        "数据字段指引：使用 data.growth.indicators（currentMomGrowth、currentYoyGrowth、historicalMomMean、historicalMomStdDev、currentMomZScore 等）以及 growthLimits。\n"
                        +
                        "输出要求：明确‘当月增长方向与幅度’‘是否异常及依据’‘本月风险评级：低/中/高’‘下一步跟踪阈值（如 |Z|≥2、偏离>20% 等）’。历史月份仅作参照，不要列举历史月份的风险清单。";
                break;
            case "budget":
                focus = "章节侧重点：聚焦预算执行差异（超支/结余）、关键超差月份与幅度、结构性超差来源，并提出控制策略。";
                break;
            case "forecast":
                focus = "章节侧重点：在不臆造模型细节的前提下，基于历史数据与已给出的预测结果进行通俗解读，说明区间不确定性与经营建议。";
                break;
            default:
                focus = "章节侧重点：围绕本章节数据最能说明的问题进行重点解读与决策建议。";
        }

        return common + "\n\n" + focus;
    }

    // 已移除未使用的工作流ID映射方法，Agent只依赖 data 与 prompt

    /**
     * 根据章节组装传入 Agent 的 data 对象
     */
    private Map<String, Object> buildDataForAnalysisType(Long indicatorId, String period, String analysisType) {
        Map<String, Object> data = new java.util.LinkedHashMap<>();
        data.put("indicatorId", indicatorId);
        data.put("period", period);

        try {
            switch (analysisType) {
                case "overview":
                    // 总览可复用结构、趋势、预算等的关键片段
                    data.put("structure",
                            indicatorDeepAnalysisService.generateCategoryStructureChartData(indicatorId, period));
                    data.put("trend",
                            indicatorDeepAnalysisService.generateTrendComparisonChartData(indicatorId, period));
                    data.put("budget",
                            indicatorDeepAnalysisService.generateBudgetAnalysisChartData(indicatorId, period));
                    break;
                case "structure":
                    data.put("structure",
                            indicatorDeepAnalysisService.generateCategoryStructureChartData(indicatorId, period));
                    break;
                case "trend":
                    data.put("trend",
                            indicatorDeepAnalysisService.generateTrendComparisonChartData(indicatorId, period));
                    break;
                case "volatility":
                    data.put("volatility",
                            indicatorDeepAnalysisService.generateVolatilityAnalysisChartData(indicatorId, period));
                    break;
                case "growth":
                    data.put("growth",
                            indicatorDeepAnalysisService.generateGrowthAnalysisChartData(indicatorId, period));
                    break;
                case "budget":
                    data.put("budget",
                            indicatorDeepAnalysisService.generateBudgetAnalysisChartData(indicatorId, period));
                    break;
                case "forecast":
                    data.put("forecast",
                            indicatorDeepAnalysisService.generateForecastAnalysisChartData(indicatorId, period));
                    break;
                default:
                    // 默认提供结构+趋势的核心信息
                    data.put("structure",
                            indicatorDeepAnalysisService.generateCategoryStructureChartData(indicatorId, period));
                    data.put("trend",
                            indicatorDeepAnalysisService.generateTrendComparisonChartData(indicatorId, period));
            }
        } catch (Exception e) {
            log.error("构建章节数据失败: indicatorId={}, period={}, type={}", indicatorId, period, analysisType, e);
        }

        return data;
    }

    /**
     * 轮询Dify结果
     */
    private String pollDifyResult(String workflowRunId, String apiKey) {
        int maxAttempts = 20; // 最多轮询30次
        int attempt = 0;

        while (attempt < maxAttempts) {
            try {
                Thread.sleep(10000); // 等待2秒

                String detail = difyService.getWorkflowRunDetail(workflowRunId, apiKey);
                if (detail == null || detail.isBlank()) {
                    log.warn("未获取到工作流详情: workflowRunId={}", workflowRunId);
                    attempt++;
                    continue;
                }

                // 提取状态
                String status = extractStatus(detail);
                String lower = status == null ? null : status.toLowerCase();

                log.info("轮询结果: workflowRunId={}, status={}", workflowRunId, lower);

                if ("succeeded".equals(lower) || "completed".equals(lower)) {
                    // 提取输出文本
                    String outputText = extractOutputText(detail);
                    return outputText != null ? outputText : detail;
                } else if ("failed".equals(lower) || "error".equals(lower)) {
                    throw new RuntimeException("Dify工作流执行失败: " + detail);
                }

                attempt++;

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("轮询被中断");
            } catch (Exception e) {
                log.error("轮询Dify结果异常", e);
                attempt++;
            }
        }

        throw new RuntimeException("轮询超时，未获取到结果");
    }

    /**
     * 提取工作流状态字段
     */
    private String extractStatus(String detailJson) {
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(detailJson);
            if (node == null)
                return null;

            // 直接在根查找
            if (node.has("status")) {
                return node.get("status").asText();
            }

            // 在 data 节点查找
            if (node.has("data")) {
                com.fasterxml.jackson.databind.JsonNode data = node.get("data");
                if (data.isObject() && data.has("status")) {
                    return data.get("status").asText();
                }
                if (data.isTextual()) {
                    String dataText = data.asText();
                    com.fasterxml.jackson.databind.JsonNode dataObj = objectMapper.readTree(dataText);
                    if (dataObj != null && dataObj.has("status")) {
                        return dataObj.get("status").asText();
                    }
                }
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    /**
     * 提取 outputs.text 内容
     */
    private String extractOutputText(String detailJson) {
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(detailJson);
            if (node == null)
                return null;

            com.fasterxml.jackson.databind.JsonNode outputs = node.get("outputs");
            if (outputs == null)
                return null;

            if (outputs.isObject()) {
                com.fasterxml.jackson.databind.JsonNode text = outputs.get("text");
                return text != null ? text.asText() : outputs.toString();
            }

            if (outputs.isTextual()) {
                String outputsText = outputs.asText();
                com.fasterxml.jackson.databind.JsonNode outObj = objectMapper.readTree(outputsText);
                if (outObj != null && outObj.has("text")) {
                    return outObj.get("text").asText();
                }
                return outputsText;
            }
        } catch (Exception ignore) {
        }
        return null;
    }
}
