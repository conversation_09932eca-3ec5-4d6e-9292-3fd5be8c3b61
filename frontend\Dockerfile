# 运行阶段
FROM nginx:1.25-alpine

LABEL maintainer="<EMAIL>"
LABEL description="AI指标管控平台前端服务"
LABEL version="1.0.0"

# 安装必要的工具
RUN apk add --no-cache curl tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 创建应用目录
RUN mkdir -p /app/dist

# 复制构建的前端文件
COPY dist/ /app/dist

# 复制 nginx 配置文件
COPY nginx.conf /etc/nginx/nginx.conf

# 设置权限
RUN chown -R nginx:nginx /app/dist && \
    chmod -R 755 /app/dist

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
