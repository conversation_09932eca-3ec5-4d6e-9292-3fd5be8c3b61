/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.pojo.entity.IndicatorEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorVO;

import java.util.Map;
import java.util.Objects;

/**
 * Indicator包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public class IndicatorWrapper extends BaseEntityWrapper<IndicatorEntity, IndicatorVO> {

    public static IndicatorWrapper build() {
        return new IndicatorWrapper();
    }

    @Override
    public IndicatorVO entityVO(IndicatorEntity indicator) {
        return Objects.requireNonNull(BeanUtil.copyProperties(indicator, IndicatorVO.class));
    }

    /**
     * 查询条件处理 - 针对PostgreSQL数据库的特殊处理
     * 解决bigint字段LIKE查询类型不匹配的问题
     */
    public void indicatorQuery(Map<String, Object> indicator) {
        // 针对PostgreSQL数据库bigint类型字段查询需要强转的处理

        // 处理id字段 - 数据库字段为bigint类型
        String idKey = "id";
        if (Func.isNotEmpty(indicator.get(idKey))) {
            // 设置"="查询，避免bigint字段使用LIKE查询导致的类型不匹配错误
            indicator.put(idKey.concat("_equal"), Func.toLong(indicator.get(idKey)));
            // 删除默认的LIKE查询参数，防止PostgreSQL报错
            indicator.remove(idKey);
        }

        // 处理indicator_type_id字段 - 数据库字段为bigint类型
        String indicatorTypeIdKey = "indicatorTypeId";
        if (Func.isNotEmpty(indicator.get(indicatorTypeIdKey))) {
            indicator.put(indicatorTypeIdKey.concat("_equal"), Func.toLong(indicator.get(indicatorTypeIdKey)));
            indicator.remove(indicatorTypeIdKey);
        }

        // 处理create_user字段 - 数据库字段为bigint类型
        String createUserKey = "createUser";
        if (Func.isNotEmpty(indicator.get(createUserKey))) {
            indicator.put(createUserKey.concat("_equal"), Func.toLong(indicator.get(createUserKey)));
            indicator.remove(createUserKey);
        }

        // 处理create_dept字段 - 数据库字段为bigint类型
        String createDeptKey = "createDept";
        if (Func.isNotEmpty(indicator.get(createDeptKey))) {
            indicator.put(createDeptKey.concat("_equal"), Func.toLong(indicator.get(createDeptKey)));
            indicator.remove(createDeptKey);
        }

        // 处理update_user字段 - 数据库字段为bigint类型
        String updateUserKey = "updateUser";
        if (Func.isNotEmpty(indicator.get(updateUserKey))) {
            indicator.put(updateUserKey.concat("_equal"), Func.toLong(indicator.get(updateUserKey)));
            indicator.remove(updateUserKey);
        }

        // 处理status字段 - 数据库字段为int类型
        String statusKey = "status";
        if (Func.isNotEmpty(indicator.get(statusKey))) {
            indicator.put(statusKey.concat("_equal"), Func.toInt(indicator.get(statusKey)));
            indicator.remove(statusKey);
        }

        // 处理is_deleted字段 - 数据库字段为int类型
        String isDeletedKey = "isDeleted";
        if (Func.isNotEmpty(indicator.get(isDeletedKey))) {
            indicator.put(isDeletedKey.concat("_equal"), Func.toInt(indicator.get(isDeletedKey)));
            indicator.remove(isDeletedKey);
        }
    }

}