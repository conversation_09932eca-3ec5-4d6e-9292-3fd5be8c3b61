<template>
  <basic-container>
    <div class="tech-bg">
    <div class="header">
      <div class="title">指标看板</div>
      <div class="filters">
        <el-date-picker v-model="state.month" type="month" format="YYYY-MM" value-format="YYYY-MM" placeholder="选择月份" size="small" @change="onMonthChange" />
        <el-button type="primary" size="small" @click="refresh">刷新</el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <el-col :span="8">
        <el-card shadow="hover" class="tech-card metric-card gradient-blue">
          <div class="metric-top">
            <div class="metric-title-wrap">
              <i class="indicator-dot"></i>
              <div class="metric-name">两项费用率</div>
            </div>
            <el-progress :percentage="clampPercent(kpis.expenseRate.value)" type="circle" :width="64" :stroke-width="6" :color="progressColors.blue" />
          </div>
          <div class="metric-value accent">{{ formatPercent(kpis.expenseRate.value) }}</div>
          <div class="formula">公式：<span>(管理费用 + 销售费用) / 主营业务收入 × 100%</span></div>
          <el-table :data="tables.expenseRate" border size="small" class="factor-table">
            <el-table-column prop="name" label="计算因子" width="160" />
            <el-table-column prop="value" label="数值" />
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card shadow="hover" class="tech-card metric-card gradient-purple">
          <div class="metric-top">
            <div class="metric-title-wrap">
              <i class="indicator-dot"></i>
              <div class="metric-name">资产负债率</div>
            </div>
            <el-progress :percentage="clampPercent(kpis.debtAssetRatio.value)" type="circle" :width="64" :stroke-width="6" :color="progressColors.purple" />
          </div>
          <div class="metric-value accent">{{ formatPercent(kpis.debtAssetRatio.value) }}</div>
          <div class="formula">公式：<span>负债合计 / (流动资产合计 + 非流动资产合计) × 100%</span></div>
          <el-table :data="tables.debtAssetRatio" border size="small" class="factor-table">
            <el-table-column prop="name" label="计算因子" width="200" />
            <el-table-column prop="value" label="数值" />
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card shadow="hover" class="tech-card metric-card gradient-green">
          <div class="metric-top">
            <div class="metric-title-wrap">
              <i class="indicator-dot"></i>
              <div class="metric-name">当年利润增长率</div>
            </div>
            <el-progress :percentage="clampPercent(Math.abs(kpis.profitGrowthRate.value))" type="circle" :width="64" :stroke-width="6" :color="kpis.profitGrowthRate.value >= 0 ? progressColors.green : progressColors.red" />
          </div>
          <div class="metric-value" :class="kpis.profitGrowthRate.value >= 0 ? 'accent-positive' : 'accent-negative'">{{ formatPercent(kpis.profitGrowthRate.value) }}</div>
          <div class="formula">公式：<span>(本年 利润总额 − 上年 利润总额) / 上年 利润总额 × 100%</span></div>
          <el-table :data="tables.profitGrowthRate" border size="small" class="factor-table">
            <el-table-column prop="name" label="计算因子" width="200" />
            <el-table-column prop="value" label="数值" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card shadow="hover" class="tech-card metric-card gradient-orange">
          <div class="metric-top">
            <div class="metric-title-wrap">
              <i class="indicator-dot"></i>
              <div class="metric-name">国有资产保值增值率</div>
            </div>
            <el-progress :percentage="clampPercent(kpis.stateAssetPreservationRate.value)" type="circle" :width="64" :stroke-width="6" :color="progressColors.orange" />
          </div>
          <div class="metric-value accent">{{ formatPercent(kpis.stateAssetPreservationRate.value) }}</div>
          <div class="formula">公式：<span>(年初 所有者权益 + 当年 净利润) / 年初 所有者权益 × 100%</span></div>
          <el-table :data="tables.stateAssetPreservationRate" border size="small" class="factor-table">
            <el-table-column prop="name" label="计算因子" width="220" />
            <el-table-column prop="value" label="数值" />
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card shadow="hover" class="tech-card metric-card gradient-pink">
          <div class="metric-top">
            <div class="metric-title-wrap">
              <i class="indicator-dot"></i>
              <div class="metric-name">卷烟存货周转率</div>
            </div>
            <el-progress :percentage="clampPercent(kpis.cigaretteInventoryTurnover.valuePercent)" type="circle" :width="64" :stroke-width="6" :color="progressColors.blue" />
          </div>
          <div class="metric-value accent">{{ formatPercent(kpis.cigaretteInventoryTurnover.valuePercent) }}</div>
          <div class="formula">公式：<span>卷烟主营业务成本 / 平均卷烟库存成本</span>；<span class="muted">平均 = (期末余额 + 期初余额) / 2</span></div>
          <el-table :data="tables.cigaretteInventoryTurnover" border size="small" class="factor-table">
            <el-table-column prop="name" label="计算因子" width="220" />
            <el-table-column prop="value" label="数值" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    </div>
  </basic-container>
</template>

<script>
import { getMetrics } from '@/api/yjzb/dashboard'
export default {
  name: 'HomeDashboard',
  data() {
    return {
      state: {
        month: ''
      },
      progressColors: {
        blue: '#409eff',
        purple: '#6f42c1',
        green: '#34c759',
        orange: '#ff8f1f',
        pink: '#ff4ecb',
        red: '#ff4d4f'
      },
      factors: {
        // 两项费用率
        expenseRate: {
          managementExpense: 0,
          sellingExpense: 0,
          mainBusinessRevenue: 0
        },
        // 资产负债率
        debtAssetRatio: {
          totalLiabilities: 0,
          currentAssets: 0,
          nonCurrentAssets: 0
        },
        // 当年利润增长率
        profitGrowthRate: {
          currentYearProfitTotal: 0,
          lastYearProfitTotal: 0
        },
        // 国有资产保值增值率
        stateAssetPreservationRate: {
          openingEquity: 0,
          currentYearNetProfit: 0
        },
        // 卷烟存货周转率
        cigaretteInventoryTurnover: {
          cigaretteCOGS: 0,
          endingCigaretteInventory: 0,
          beginningCigaretteInventory: 0
        }
      }
    }
  },
  computed: {
    kpis() {
      const exp = this.factors.expenseRate
      const debt = this.factors.debtAssetRatio
      const growth = this.factors.profitGrowthRate
      const preserve = this.factors.stateAssetPreservationRate
      const cig = this.factors.cigaretteInventoryTurnover

      const expenseRateValue = (exp.mainBusinessRevenue || 0) === 0
        ? 0
        : ((exp.managementExpense + exp.sellingExpense) / exp.mainBusinessRevenue) * 100

      const assetSum = debt.currentAssets + debt.nonCurrentAssets
      const debtAssetRatioValue = (assetSum || 0) === 0
        ? 0
        : (debt.totalLiabilities / assetSum) * 100

      const profitGrowthRateValue = (growth.lastYearProfitTotal || 0) === 0
        ? 0
        : ((growth.currentYearProfitTotal - growth.lastYearProfitTotal) / growth.lastYearProfitTotal) * 100

      const stateAssetPreservationRateValue = (preserve.openingEquity || 0) === 0
        ? 0
        : ((preserve.openingEquity + preserve.currentYearNetProfit) / preserve.openingEquity) * 100

      const avgCigInv = (cig.endingCigaretteInventory + cig.beginningCigaretteInventory) / 2
      const cigaretteInventoryTurnoverValue = (avgCigInv || 0) === 0
        ? 0
        : cig.cigaretteCOGS / avgCigInv
      const cigaretteInventoryTurnoverPercent = cigaretteInventoryTurnoverValue * 100

      return {
        expenseRate: { value: expenseRateValue },
        debtAssetRatio: { value: debtAssetRatioValue },
        profitGrowthRate: { value: profitGrowthRateValue },
        stateAssetPreservationRate: { value: stateAssetPreservationRateValue },
        cigaretteInventoryTurnover: { value: cigaretteInventoryTurnoverValue, valuePercent: cigaretteInventoryTurnoverPercent, avgCigInv }
      }
    },
    tables() {
      return {
        expenseRate: [
          { name: '管理费用', value: this.formatCurrency(this.factors.expenseRate.managementExpense) },
          { name: '销售费用', value: this.formatCurrency(this.factors.expenseRate.sellingExpense) },
          { name: '主营业务收入', value: this.formatCurrency(this.factors.expenseRate.mainBusinessRevenue) },
          { name: '两项费用合计', value: this.formatCurrency(this.factors.expenseRate.managementExpense + this.factors.expenseRate.sellingExpense) }
        ],
        debtAssetRatio: [
          { name: '负债合计', value: this.formatCurrency(this.factors.debtAssetRatio.totalLiabilities) },
          { name: '流动资产合计', value: this.formatCurrency(this.factors.debtAssetRatio.currentAssets) },
          { name: '非流动资产合计', value: this.formatCurrency(this.factors.debtAssetRatio.nonCurrentAssets) },
          { name: '资产合计(计算)', value: this.formatCurrency(this.factors.debtAssetRatio.currentAssets + this.factors.debtAssetRatio.nonCurrentAssets) }
        ],
        profitGrowthRate: [
          { name: '本年 利润总额', value: this.formatCurrency(this.factors.profitGrowthRate.currentYearProfitTotal) },
          { name: '上年 利润总额', value: this.formatCurrency(this.factors.profitGrowthRate.lastYearProfitTotal) },
          { name: '增量', value: this.formatCurrency(this.factors.profitGrowthRate.currentYearProfitTotal - this.factors.profitGrowthRate.lastYearProfitTotal) }
        ],
        stateAssetPreservationRate: [
          { name: '年初 所有者权益', value: this.formatCurrency(this.factors.stateAssetPreservationRate.openingEquity) },
          { name: '当年 净利润', value: this.formatCurrency(this.factors.stateAssetPreservationRate.currentYearNetProfit) },
          { name: '期末 权益(计算)', value: this.formatCurrency(this.factors.stateAssetPreservationRate.openingEquity + this.factors.stateAssetPreservationRate.currentYearNetProfit) }
        ],
        cigaretteInventoryTurnover: [
          { name: '卷烟 主营业务成本', value: this.formatCurrency(this.factors.cigaretteInventoryTurnover.cigaretteCOGS) },
          { name: '期初 卷烟存货余额', value: this.formatCurrency(this.factors.cigaretteInventoryTurnover.beginningCigaretteInventory) },
          { name: '期末 卷烟存货余额', value: this.formatCurrency(this.factors.cigaretteInventoryTurnover.endingCigaretteInventory) },
          { name: '平均卷烟库存成本(计算)', value: this.formatCurrency(this.kpis.cigaretteInventoryTurnover.avgCigInv) }
        ]
      }
    }
  },
  mounted() {
    this.state.month = this.getCurrentMonth()
    this.loadMetrics()
  },
  methods: {
    async refresh() {
      this.state.month = this.getCurrentMonth()
      await this.loadMetrics()
    },
    onMonthChange() {
      this.loadMetrics()
    },
    async loadMetrics() {
      try {
        const res = await getMetrics(this.state.month)
        const payload = res.data && res.data.data ? res.data.data : null
        if (!payload) {
          this.$message.error('未获取到首页指标数据')
          return
        }
        const toNum = (v) => (v === null || v === undefined ? 0 : Number(v))
        // 映射后端因子到前端 factors
        this.factors.expenseRate = {
          managementExpense: toNum(payload.expenseRate?.factors?.['管理费用']),
          sellingExpense: toNum(payload.expenseRate?.factors?.['销售费用']),
          mainBusinessRevenue: toNum(payload.expenseRate?.factors?.['主营业务收入'])
        }
        this.factors.debtAssetRatio = {
          totalLiabilities: toNum(payload.debtAssetRatio?.factors?.['负债合计']),
          currentAssets: toNum(payload.debtAssetRatio?.factors?.['流动资产合计']),
          nonCurrentAssets: toNum(payload.debtAssetRatio?.factors?.['非流动资产合计'])
        }
        this.factors.profitGrowthRate = {
          currentYearProfitTotal: toNum(payload.profitGrowthRate?.factors?.['本年 利润总额(累计)']),
          lastYearProfitTotal: toNum(payload.profitGrowthRate?.factors?.['上年 利润总额(累计)'])
        }
        this.factors.stateAssetPreservationRate = {
          openingEquity: toNum(payload.stateAssetPreservationRate?.factors?.['年初 所有者权益']),
          currentYearNetProfit: toNum(payload.stateAssetPreservationRate?.factors?.['当年 净利润(累计)'])
        }
        this.factors.cigaretteInventoryTurnover = {
          cigaretteCOGS: toNum(payload.cigaretteInventoryTurnover?.factors?.['卷烟 主营业务成本']),
          beginningCigaretteInventory: toNum(payload.cigaretteInventoryTurnover?.factors?.['期初 卷烟存货余额']),
          endingCigaretteInventory: toNum(payload.cigaretteInventoryTurnover?.factors?.['期末 卷烟存货余额'])
        }
        this.$message.success('首页指标数据已更新')
      } catch (e) {
        this.$message.error('首页指标数据获取失败')
      }
    },
    clampPercent(value) {
      if (value === null || value === undefined || isNaN(value)) return 0
      if (value < 0) return 0
      if (value > 100) return 100
      return Number(Number(value).toFixed(2))
    },
    formatNumber(value, digits = 2) {
      if (value === null || value === undefined || isNaN(value)) return '-'
      const fixed = Number(value).toFixed(digits)
      const parts = fixed.split('.')
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      return parts.join('.')
    },
    formatCurrency(value) {
      return '¥ ' + this.formatNumber(value, 2)
    },
    formatPercent(value) {
      return this.formatNumber(value, 2) + '%'
    },
    formatTimes(value) {
      return this.formatNumber(value, 2) + ' 次'
    },
    getCurrentMonth() {
      const now = new Date()
      const y = now.getFullYear()
      const m = `${now.getMonth() + 1}`.padStart(2, '0')
      return `${y}-${m}`
    }
  }
}
</script>

<style lang="scss" scoped>
.tech-bg {
  position: relative;
  padding: 10px;
  border-radius: 12px;
  background:
    radial-gradient(1200px 600px at 100% -20%, rgba(64, 158, 255, 0.10), transparent 60%),
    radial-gradient(1200px 600px at 0% 120%, rgba(255, 78, 203, 0.08), transparent 60%),
    linear-gradient(180deg, rgba(255,255,255,0.85), rgba(255,255,255,0.75));
  overflow: hidden;
}
.tech-bg::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image:
      linear-gradient(rgba(99, 102, 241, 0.08) 1px, transparent 1px),
      linear-gradient(90deg, rgba(99, 102, 241, 0.08) 1px, transparent 1px);
  background-size: 24px 24px, 24px 24px;
  mask-image: radial-gradient(ellipse at center, rgba(0,0,0,.6), transparent 70%);
  pointer-events: none;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.title {
  font-size: 20px;
  font-weight: 800;
  letter-spacing: .5px;
  background: linear-gradient(90deg, #111827, #2563eb);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.filters {
  display: flex;
  align-items: center;
  gap: 8px;
}
.tech-card {
  position: relative;
  border-radius: 14px;
  background: linear-gradient(180deg, rgba(255,255,255,0.92), rgba(255,255,255,0.82));
  border: 1px solid rgba(45, 55, 72, 0.08);
  box-shadow: 0 8px 20px rgba(31, 41, 55, 0.08);
  transition: transform .2s ease, box-shadow .2s ease;
  overflow: hidden;
}
.tech-card::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  border-radius: 14px;
  background: linear-gradient(135deg, rgba(64,158,255,.35), rgba(111,66,193,.35), rgba(255,78,203,.25));
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}
.tech-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 14px 28px rgba(31, 41, 55, 0.14);
}
.metric-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}
.metric-title-wrap {
  display: flex;
  align-items: center;
  gap: 8px;
}
.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #34d399, #10b981);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.15), 0 0 14px rgba(16,185,129,.45);
}
.metric-name {
  font-size: 14px;
  color: #4b5563;
  }
  .metric-value {
  font-size: 26px;
  font-weight: 800;
}
.accent { color: #2563eb; }
.accent-positive { color: #16a34a; }
.accent-negative { color: #ef4444; }
.chip {
  padding: 4px 10px;
  border-radius: 999px;
  background: linear-gradient(90deg, rgba(99,102,241,.12), rgba(99,102,241,.07));
  color: #6366f1;
  font-weight: 600;
}
.formula {
  background: linear-gradient(180deg, rgba(230, 244, 255, 0.8), rgba(225, 240, 255, 0.9));
  border: 1px solid rgba(64, 158, 255, 0.22);
  border-radius: 10px;
  padding: 10px 12px;
  color: #1e3a8a;
  margin: 10px 0;
  box-shadow: inset 0 0 0 1px rgba(255,255,255,0.35);
}
.formula span {
  font-family: Consolas, Monaco, 'Courier New', monospace;
}
.factor-table ::v-deep .el-table__header th {
  background: #fafafa;
}
.muted {
  color: #909399;
}

/* 渐变主题边角光泽（可选配色类） */
.gradient-blue { box-shadow: inset 0 0 0 1px rgba(64,158,255,.18), 0 10px 20px rgba(64,158,255,.08); }
.gradient-purple { box-shadow: inset 0 0 0 1px rgba(111,66,193,.18), 0 10px 20px rgba(111,66,193,.08); }
.gradient-green { box-shadow: inset 0 0 0 1px rgba(52,199,89,.18), 0 10px 20px rgba(52,199,89,.08); }
.gradient-orange { box-shadow: inset 0 0 0 1px rgba(255,143,31,.18), 0 10px 20px rgba(255,143,31,.08); }
.gradient-pink { box-shadow: inset 0 0 0 1px rgba(255,78,203,.18), 0 10px 20px rgba(255,78,203,.08); }
</style> 