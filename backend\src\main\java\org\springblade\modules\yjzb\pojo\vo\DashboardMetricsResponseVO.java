package org.springblade.modules.yjzb.pojo.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Map;

@Data
public class DashboardMetricsResponseVO {
    private String period;
    private Metric expenseRate; // 两项费用率(%)
    private Metric debtAssetRatio; // 资产负债率(%)
    private Metric profitGrowthRate; // 当年利润增长率(%)
    private Metric stateAssetPreservationRate; // 国有资产保值增值率(%)
    private Metric cigaretteInventoryTurnover; // 卷烟存货周转率(%)

    @Data
    public static class Metric {
        private BigDecimal value; // 指标值
        private Map<String, BigDecimal> factors; // 参与计算的因子
        private String formula; // 公式说明
        private String unit; // 单位(%, 元, 次等)
    }
}
