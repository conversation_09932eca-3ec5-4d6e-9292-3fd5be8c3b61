<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.IndicatorValuesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indicatorValuesResultMap" type="org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity">
        <result column="id" property="id"/>
        <result column="indicator_id" property="indicatorId"/>
        <result column="period" property="period"/>
        <result column="value" property="value"/>
        <result column="dimensions" property="dimensions"/>
        <result column="data_source" property="dataSource"/>
        <result column="calculation_status" property="calculationStatus"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectIndicatorValuesPage" resultMap="indicatorValuesResultMap">
        select * from yjzb_indicator_values where is_deleted = 0
    </select>


    <select id="exportIndicatorValues" resultType="org.springblade.modules.yjzb.excel.IndicatorValuesExcel">
        SELECT * FROM yjzb_indicator_values ${ew.customSqlSegment}
    </select>

    <!-- 根据指标类型分页查询指标数据 -->
    <select id="selectIndicatorValuesByType" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorValuesVO">
        SELECT 
            iv.id,
            iv.indicator_id as indicatorId,
            iv.period,
            iv.value,
            iv.dimensions,
            iv.data_source as dataSource,
            iv.calculation_status as calculationStatus,
            iv.create_user as createUser,
            iv.create_dept as createDept,
            iv.create_time as createTime,
            iv.update_user as updateUser,
            iv.update_time as updateTime,
            iv.status,
            iv.is_deleted as isDeleted,
            i.name as indicatorName,
            i.code as indicatorCode,
            i.unit as indicatorUnit,
            it.type_name as typeName,
            it.type_code as typeCode
        FROM yjzb_indicator_values iv
        LEFT JOIN yjzb_indicator i ON iv.indicator_id = i.id
        LEFT JOIN yjzb_indicator_types it ON i.indicator_type_id = it.id
        WHERE iv.is_deleted = 0
        AND i.is_deleted = 0
        AND it.is_deleted = 0
        AND it.id = #{indicatorTypeId}
        <if test="period != null and period != ''">
            AND iv.period = #{period}
        </if>
        <if test="dataSource != null and dataSource != ''">
            AND iv.data_source = #{dataSource}
        </if>
        <if test="indicatorNameLike != null and indicatorNameLike != ''">
            AND (
                LOWER(i.name) LIKE CONCAT('%', LOWER(#{indicatorNameLike}), '%')
                OR LOWER(i.code) LIKE CONCAT('%', LOWER(#{indicatorNameLike}), '%')
            )
        </if>
        <if test="filterEmpty != null and filterEmpty == true">
            AND iv.value IS NOT NULL AND iv.value != 0
        </if>
        ORDER BY iv.period DESC, iv.create_time DESC
    </select>

    <!-- 统计指定月份和指标类型的指标数值 -->
    <select id="getIndicatorStatistics" resultType="java.util.HashMap">
        SELECT 
            COUNT(iv.id) AS "totalCount",
            COALESCE(SUM(iv.value), 0) AS "totalValue",
            COALESCE(AVG(iv.value), 0) AS "avgValue",
            COALESCE(MAX(iv.value), 0) AS "maxValue",
            COALESCE(MIN(iv.value), 0) AS "minValue",
            COUNT(CASE WHEN iv.calculation_status = 'COMPLETED' THEN 1 END) AS "completedCount",
            COUNT(CASE WHEN iv.calculation_status = 'FAILED' THEN 1 END) AS "failedCount",
            COUNT(CASE WHEN iv.calculation_status = 'PENDING' THEN 1 END) AS "pendingCount",
            COUNT(CASE WHEN iv.calculation_status = 'PROCESSING' THEN 1 END) AS "processingCount",
            #{period} AS "period",
            #{indicatorTypeId} AS "indicatorTypeId"
        FROM yjzb_indicator_values iv
        LEFT JOIN yjzb_indicator i ON iv.indicator_id = i.id
        LEFT JOIN yjzb_indicator_types it ON i.indicator_type_id = it.id
        WHERE iv.is_deleted = 0
        AND i.is_deleted = 0
        AND it.is_deleted = 0
        AND iv.period = #{period}
        AND it.id = #{indicatorTypeId}
        <if test="filterEmpty != null and filterEmpty == true">
            AND iv.value IS NOT NULL AND iv.value != 0
        </if>
    </select>

</mapper>
