/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 指标 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class IndicatorExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键ID")
	private Long id;
	/**
	 * 指标类型名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("指标类型名称")
	private String name;
	/**
	 * 指标类型编码（唯一）
	 */
	@ColumnWidth(20)
	@ExcelProperty("指标类型编码（唯一）")
	private String code;
	/**
	 * 指标类型描述
	 */
	@ColumnWidth(20)
	@ExcelProperty("指标类型描述")
	private String description;
	/**
	 * 指标类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("指标类型")
	private Long indicatorTypeId;
	/**
	 * 数据类型（NUMERIC-数值型、PERCENTAGE-百分比、ENUM-枚举、COMPOSITE-复合型）
	 */
	@ColumnWidth(20)
	@ExcelProperty("数据类型（NUMERIC-数值型、PERCENTAGE-百分比、ENUM-枚举、COMPOSITE-复合型）")
	private String dataType;
	/**
	 * 计量单位
	 */
	@ColumnWidth(20)
	@ExcelProperty("计量单位")
	private String unit;
	/**
	 * 计算公式（用于复合指标）
	 */
	@ColumnWidth(20)
	@ExcelProperty("计算公式（用于复合指标）")
	private String calculationFormula;
	/**
	 * 数据源配置（JSON格式，包含数据源类型、连接信息等）
	 */
	@ColumnWidth(20)
	@ExcelProperty("数据源配置（JSON格式，包含数据源类型、连接信息等）")
	private String dataSourceConfig;
	/**
	 * 删除标记（0-未删除，1-已删除）
	 */
	@ColumnWidth(20)
	@ExcelProperty("删除标记（0-未删除，1-已删除）")
	private Integer isDeleted;

}
