/**
 * 办公费用深度分析工具类
 * 提供异常检测、趋势分析、预测等功能
 */

export class ExpenseAnalyzer {
  constructor() {
    this.rawData = [];
    this.featureData = [];
  }

  /**
   * 加载CSV数据
   * @param {string} rawCsvPath - 原始费用CSV文件路径
   * @param {string} featureCsvPath - 特征工程CSV文件路径
   */
  async loadData(rawCsvPath, featureCsvPath) {
    try {
      // 在实际项目中，这里应该使用fetch或axios加载CSV文件
      // 由于浏览器安全限制，这里使用模拟数据
      this.rawData = await this.loadMockRawData();
      this.featureData = await this.loadMockFeatureData();
      
      console.log('数据加载完成:', {
        rawDataCount: this.rawData.length,
        featureDataCount: this.featureData.length
      });
      
      return true;
    } catch (error) {
      console.error('数据加载失败:', error);
      return false;
    }
  }

  /**
   * 异常费用检测
   * 基于多种算法检测异常支出
   */
  detectAnomalies() {
    const anomalies = [];
    
    // 1. 基于统计学的异常检测（Z-Score方法）
    const statisticalAnomalies = this.detectStatisticalAnomalies();
    anomalies.push(...statisticalAnomalies);
    
    // 2. 基于增长率的异常检测
    const growthAnomalies = this.detectGrowthAnomalies();
    anomalies.push(...growthAnomalies);
    
    // 3. 基于预算偏差的异常检测
    const budgetAnomalies = this.detectBudgetAnomalies();
    anomalies.push(...budgetAnomalies);
    
    // 4. 基于时间序列的异常检测
    const timeSeriesAnomalies = this.detectTimeSeriesAnomalies();
    anomalies.push(...timeSeriesAnomalies);
    
    // 去重并按严重程度排序
    const uniqueAnomalies = this.deduplicateAnomalies(anomalies);
    return this.sortAnomaliesBySeverity(uniqueAnomalies);
  }

  /**
   * 统计学异常检测（Z-Score方法）
   */
  detectStatisticalAnomalies() {
    const anomalies = [];
    const threshold = 2.5; // Z-Score阈值
    
    // 按类别分组计算Z-Score
    const categoryGroups = this.groupByCategory(this.featureData);
    
    Object.entries(categoryGroups).forEach(([category, data]) => {
      const amounts = data.map(item => item.月度总金额);
      const mean = this.calculateMean(amounts);
      const stdDev = this.calculateStandardDeviation(amounts, mean);
      
      data.forEach(item => {
        const zScore = Math.abs((item.月度总金额 - mean) / stdDev);
        
        if (zScore > threshold) {
          anomalies.push({
            id: `stat_${item.年月}_${category}`,
            type: 'statistical',
            category: category,
            date: item.年月,
            amount: item.月度总金额,
            severity: this.calculateSeverity(zScore, 'statistical'),
            reason: `统计异常：Z-Score=${zScore.toFixed(2)}，偏离均值${zScore.toFixed(1)}个标准差`,
            details: {
              zScore: zScore,
              mean: mean,
              stdDev: stdDev,
              threshold: threshold
            }
          });
        }
      });
    });
    
    return anomalies;
  }

  /**
   * 增长率异常检测
   */
  detectGrowthAnomalies() {
    const anomalies = [];
    const momThreshold = 2.0; // 环比增长率阈值（200%）
    const yoyThreshold = 3.0; // 同比增长率阈值（300%）
    
    this.featureData.forEach(item => {
      const momGrowth = Math.abs(item.较上月增长率 || 0);
      const yoyGrowth = Math.abs(item.较去年同期增长率 || 0);
      
      if (momGrowth > momThreshold) {
        anomalies.push({
          id: `mom_${item.年月}_${item.二级分类}`,
          type: 'growth',
          category: item.二级分类,
          date: item.年月,
          amount: item.月度总金额,
          severity: this.calculateSeverity(momGrowth, 'growth'),
          reason: `环比增长异常：${(item.较上月增长率 * 100).toFixed(1)}%`,
          details: {
            growthRate: item.较上月增长率,
            threshold: momThreshold,
            type: 'month-over-month'
          }
        });
      }
      
      if (yoyGrowth > yoyThreshold) {
        anomalies.push({
          id: `yoy_${item.年月}_${item.二级分类}`,
          type: 'growth',
          category: item.二级分类,
          date: item.年月,
          amount: item.月度总金额,
          severity: this.calculateSeverity(yoyGrowth, 'growth'),
          reason: `同比增长异常：${(item.较去年同期增长率 * 100).toFixed(1)}%`,
          details: {
            growthRate: item.较去年同期增长率,
            threshold: yoyThreshold,
            type: 'year-over-year'
          }
        });
      }
    });
    
    return anomalies;
  }

  /**
   * 预算偏差异常检测
   */
  detectBudgetAnomalies() {
    const anomalies = [];
    const deviationThreshold = 0.3; // 预算偏差阈值（30%）
    
    this.featureData.forEach(item => {
      const deviation = Math.abs(item.预算偏差 || 0);
      
      if (deviation > deviationThreshold) {
        anomalies.push({
          id: `budget_${item.年月}_${item.二级分类}`,
          type: 'budget',
          category: item.二级分类,
          date: item.年月,
          amount: item.月度总金额,
          severity: this.calculateSeverity(deviation, 'budget'),
          reason: `预算偏差异常：${(item.预算偏差 * 100).toFixed(1)}%`,
          details: {
            deviation: item.预算偏差,
            budgetRate: item.预算完成率,
            threshold: deviationThreshold
          }
        });
      }
    });
    
    return anomalies;
  }

  /**
   * 时间序列异常检测
   * 基于移动平均和趋势分析
   */
  detectTimeSeriesAnomalies() {
    const anomalies = [];
    const windowSize = 3; // 移动窗口大小
    
    // 按类别分组进行时间序列分析
    const categoryGroups = this.groupByCategory(this.featureData);
    
    Object.entries(categoryGroups).forEach(([category, data]) => {
      // 按时间排序
      const sortedData = data.sort((a, b) => a.年月.localeCompare(b.年月));
      
      for (let i = windowSize; i < sortedData.length; i++) {
        const current = sortedData[i];
        const window = sortedData.slice(i - windowSize, i);
        const movingAvg = this.calculateMean(window.map(item => item.月度总金额));
        const deviation = Math.abs(current.月度总金额 - movingAvg) / movingAvg;
        
        if (deviation > 0.5) { // 偏离移动平均50%以上
          anomalies.push({
            id: `ts_${current.年月}_${category}`,
            type: 'timeseries',
            category: category,
            date: current.年月,
            amount: current.月度总金额,
            severity: this.calculateSeverity(deviation, 'timeseries'),
            reason: `时间序列异常：偏离${windowSize}期移动平均${(deviation * 100).toFixed(1)}%`,
            details: {
              movingAverage: movingAvg,
              deviation: deviation,
              windowSize: windowSize
            }
          });
        }
      }
    });
    
    return anomalies;
  }

  /**
   * 计算异常严重程度
   */
  calculateSeverity(value, type) {
    let severity = 'low';
    
    switch (type) {
      case 'statistical':
        if (value > 4) severity = 'critical';
        else if (value > 3) severity = 'high';
        else if (value > 2.5) severity = 'medium';
        break;
      case 'growth':
        if (value > 10) severity = 'critical';
        else if (value > 5) severity = 'high';
        else if (value > 2) severity = 'medium';
        break;
      case 'budget':
        if (value > 0.8) severity = 'critical';
        else if (value > 0.5) severity = 'high';
        else if (value > 0.3) severity = 'medium';
        break;
      case 'timeseries':
        if (value > 2) severity = 'critical';
        else if (value > 1) severity = 'high';
        else if (value > 0.5) severity = 'medium';
        break;
    }
    
    return severity;
  }

  /**
   * 异常去重
   */
  deduplicateAnomalies(anomalies) {
    const uniqueMap = new Map();
    
    anomalies.forEach(anomaly => {
      const key = `${anomaly.date}_${anomaly.category}`;
      if (!uniqueMap.has(key) || 
          this.getSeverityScore(anomaly.severity) > this.getSeverityScore(uniqueMap.get(key).severity)) {
        uniqueMap.set(key, anomaly);
      }
    });
    
    return Array.from(uniqueMap.values());
  }

  /**
   * 按严重程度排序异常
   */
  sortAnomaliesBySeverity(anomalies) {
    return anomalies.sort((a, b) => {
      const scoreA = this.getSeverityScore(a.severity);
      const scoreB = this.getSeverityScore(b.severity);
      return scoreB - scoreA; // 降序排列
    });
  }

  /**
   * 获取严重程度分数
   */
  getSeverityScore(severity) {
    const scores = {
      'critical': 4,
      'high': 3,
      'medium': 2,
      'low': 1
    };
    return scores[severity] || 0;
  }

  /**
   * 工具方法：按类别分组
   */
  groupByCategory(data) {
    return data.reduce((groups, item) => {
      const category = item.二级分类;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(item);
      return groups;
    }, {});
  }

  /**
   * 工具方法：计算平均值
   */
  calculateMean(values) {
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  }

  /**
   * 工具方法：计算标准差
   */
  calculateStandardDeviation(values, mean) {
    const variance = values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  /**
   * 加载模拟原始数据
   */
  async loadMockRawData() {
    // 这里返回模拟的原始CSV数据
    return [
      {
        年: 2024,
        月: 5,
        日: 15,
        部门: '本部',
        摘要: '办公用品采购',
        金额: 266486.96,
        类别: '其他杂项',
        费用类型: '管理费用'
      }
      // 更多数据...
    ];
  }

  /**
   * 预算执行分析
   * 分析各类别的预算完成情况、偏差和趋势
   */
  analyzeBudgetExecution() {
    const budgetAnalysis = {
      overallSummary: {},
      categoryAnalysis: [],
      monthlyTrends: [],
      riskAssessment: {}
    };

    // 1. 整体预算分析
    budgetAnalysis.overallSummary = this.calculateOverallBudgetSummary();

    // 2. 分类别预算分析
    budgetAnalysis.categoryAnalysis = this.calculateCategoryBudgetAnalysis();

    // 3. 月度预算趋势
    budgetAnalysis.monthlyTrends = this.calculateMonthlyBudgetTrends();

    // 4. 风险评估
    budgetAnalysis.riskAssessment = this.assessBudgetRisks();

    return budgetAnalysis;
  }

  /**
   * 计算整体预算摘要
   */
  calculateOverallBudgetSummary() {
    const latestData = this.getLatestDataByCategory();

    let totalBudget = 0;
    let totalActual = 0;
    let totalRemaining = 0;

    Object.values(latestData).forEach(item => {
      totalBudget += item.年度预算 || 0;
      totalActual += item.当年累计金额 || 0;
    });

    totalRemaining = totalBudget - totalActual;
    const overallRate = totalBudget > 0 ? (totalActual / totalBudget) * 100 : 0;

    return {
      totalBudget,
      totalActual,
      totalRemaining,
      overallRate: Math.round(overallRate * 100) / 100,
      status: this.getBudgetStatus(overallRate)
    };
  }

  /**
   * 计算分类别预算分析
   */
  calculateCategoryBudgetAnalysis() {
    const latestData = this.getLatestDataByCategory();
    const categoryAnalysis = [];

    Object.entries(latestData).forEach(([category, data]) => {
      const budget = data.年度预算 || 0;
      const actual = data.当年累计金额 || 0;
      const rate = budget > 0 ? (actual / budget) * 100 : 0;
      const deviation = actual - budget;
      const remaining = Math.max(0, budget - actual);

      // 计算月度平均支出
      const monthlyData = this.featureData.filter(item => item.二级分类 === category);
      const avgMonthlySpend = monthlyData.length > 0
        ? monthlyData.reduce((sum, item) => sum + (item.月度总金额 || 0), 0) / monthlyData.length
        : 0;

      // 预测剩余月份所需预算
      const currentMonth = new Date().getMonth() + 1;
      const remainingMonths = 12 - currentMonth;
      const projectedSpend = avgMonthlySpend * remainingMonths;

      categoryAnalysis.push({
        category,
        budget,
        actual,
        rate: Math.round(rate * 100) / 100,
        deviation,
        remaining,
        avgMonthlySpend,
        projectedSpend,
        projectedTotal: actual + projectedSpend,
        riskLevel: this.calculateBudgetRisk(rate, projectedSpend, remaining),
        trend: this.calculateBudgetTrend(category),
        recommendations: this.generateBudgetRecommendations(category, rate, remaining, projectedSpend)
      });
    });

    return categoryAnalysis.sort((a, b) => b.rate - a.rate);
  }

  /**
   * 计算月度预算趋势
   */
  calculateMonthlyBudgetTrends() {
    const monthlyTrends = [];
    const monthlyData = new Map();

    // 按月份汇总数据
    this.featureData.forEach(item => {
      const month = item.年月;
      if (!monthlyData.has(month)) {
        monthlyData.set(month, {
          month,
          totalSpend: 0,
          budgetRate: 0,
          categories: new Set()
        });
      }

      const monthData = monthlyData.get(month);
      monthData.totalSpend += item.月度总金额 || 0;
      monthData.budgetRate = item.预算完成率 || 0;
      monthData.categories.add(item.二级分类);
    });

    // 转换为数组并排序
    const sortedMonths = Array.from(monthlyData.values())
      .sort((a, b) => a.month.localeCompare(b.month));

    // 计算趋势指标
    sortedMonths.forEach((monthData, index) => {
      const prevMonth = index > 0 ? sortedMonths[index - 1] : null;
      const momGrowth = prevMonth
        ? ((monthData.totalSpend - prevMonth.totalSpend) / prevMonth.totalSpend) * 100
        : 0;

      monthlyTrends.push({
        ...monthData,
        categories: monthData.categories.size,
        momGrowth: Math.round(momGrowth * 100) / 100
      });
    });

    return monthlyTrends;
  }

  /**
   * 评估预算风险
   */
  assessBudgetRisks() {
    const categoryAnalysis = this.calculateCategoryBudgetAnalysis();
    const risks = {
      high: [],
      medium: [],
      low: [],
      summary: {}
    };

    categoryAnalysis.forEach(category => {
      const risk = {
        category: category.category,
        riskLevel: category.riskLevel,
        reason: this.getBudgetRiskReason(category),
        impact: this.calculateRiskImpact(category),
        mitigation: this.suggestRiskMitigation(category)
      };

      risks[category.riskLevel].push(risk);
    });

    risks.summary = {
      totalCategories: categoryAnalysis.length,
      highRiskCount: risks.high.length,
      mediumRiskCount: risks.medium.length,
      lowRiskCount: risks.low.length,
      overallRiskLevel: this.calculateOverallRiskLevel(risks)
    };

    return risks;
  }

  /**
   * 获取每个类别的最新数据
   */
  getLatestDataByCategory() {
    const latestData = {};

    this.featureData.forEach(item => {
      const category = item.二级分类;
      if (!latestData[category] || item.年月 > latestData[category].年月) {
        latestData[category] = item;
      }
    });

    return latestData;
  }

  /**
   * 获取预算状态
   */
  getBudgetStatus(rate) {
    if (rate >= 95) return 'critical';
    if (rate >= 80) return 'warning';
    if (rate >= 50) return 'normal';
    return 'good';
  }

  /**
   * 计算预算风险等级
   */
  calculateBudgetRisk(rate, projectedSpend, remaining) {
    if (rate >= 90 || projectedSpend > remaining * 1.2) return 'high';
    if (rate >= 70 || projectedSpend > remaining * 0.8) return 'medium';
    return 'low';
  }

  /**
   * 计算预算趋势
   */
  calculateBudgetTrend(category) {
    const categoryData = this.featureData
      .filter(item => item.二级分类 === category)
      .sort((a, b) => a.年月.localeCompare(b.年月));

    if (categoryData.length < 2) return 'stable';

    const recent = categoryData.slice(-3);
    const avgGrowth = recent.reduce((sum, item) => sum + (item.较上月增长率 || 0), 0) / recent.length;

    if (avgGrowth > 0.1) return 'increasing';
    if (avgGrowth < -0.1) return 'decreasing';
    return 'stable';
  }

  /**
   * 生成预算建议
   */
  generateBudgetRecommendations(category, rate, remaining, projectedSpend) {
    const recommendations = [];

    if (rate >= 90) {
      recommendations.push('预算即将用完，需要严格控制支出');
    }

    if (projectedSpend > remaining) {
      recommendations.push(`预计超支¥${this.formatNumber(projectedSpend - remaining)}，建议申请追加预算`);
    }

    if (rate < 50) {
      recommendations.push('预算执行进度偏慢，可考虑加快相关项目推进');
    }

    return recommendations.length > 0 ? recommendations : ['预算执行正常'];
  }

  /**
   * 获取预算风险原因
   */
  getBudgetRiskReason(category) {
    if (category.rate >= 90) {
      return '预算执行率过高，存在超支风险';
    }
    if (category.projectedSpend > category.remaining) {
      return '按当前支出趋势，年底将超出预算';
    }
    if (category.trend === 'increasing') {
      return '支出呈上升趋势，需要关注';
    }
    return '预算执行正常';
  }

  /**
   * 计算风险影响
   */
  calculateRiskImpact(category) {
    const potentialOverspend = Math.max(0, category.projectedTotal - category.budget);
    if (potentialOverspend > category.budget * 0.2) return 'high';
    if (potentialOverspend > category.budget * 0.1) return 'medium';
    return 'low';
  }

  /**
   * 建议风险缓解措施
   */
  suggestRiskMitigation(category) {
    const suggestions = [];

    if (category.riskLevel === 'high') {
      suggestions.push('立即冻结非必要支出');
      suggestions.push('重新评估剩余预算分配');
      suggestions.push('考虑申请预算调整');
    } else if (category.riskLevel === 'medium') {
      suggestions.push('加强支出审批流程');
      suggestions.push('定期监控预算执行情况');
    } else {
      suggestions.push('保持当前预算管控水平');
    }

    return suggestions;
  }

  /**
   * 计算整体风险等级
   */
  calculateOverallRiskLevel(risks) {
    if (risks.high.length > 3) return 'high';
    if (risks.high.length > 0 || risks.medium.length > 5) return 'medium';
    return 'low';
  }

  /**
   * 趋势分析
   * 分析历史趋势、季节性模式、同比环比变化
   */
  analyzeTrends() {
    const trendAnalysis = {
      historicalTrends: this.calculateHistoricalTrends(),
      seasonalPatterns: this.calculateSeasonalPatterns(),
      growthAnalysis: this.calculateGrowthAnalysis(),
      categoryTrends: this.calculateCategoryTrends(),
      insights: this.generateTrendInsights()
    };

    return trendAnalysis;
  }

  /**
   * 计算历史趋势
   */
  calculateHistoricalTrends() {
    const monthlyData = new Map();

    // 按月汇总数据
    this.featureData.forEach(item => {
      const month = item.年月;
      if (!monthlyData.has(month)) {
        monthlyData.set(month, {
          month,
          totalAmount: 0,
          categories: new Map(),
          transactionCount: 0
        });
      }

      const monthData = monthlyData.get(month);
      monthData.totalAmount += item.月度总金额 || 0;
      monthData.transactionCount += item.月度交易次数 || 0;

      if (!monthData.categories.has(item.二级分类)) {
        monthData.categories.set(item.二级分类, 0);
      }
      monthData.categories.set(item.二级分类,
        monthData.categories.get(item.二级分类) + (item.月度总金额 || 0));
    });

    // 转换为数组并排序
    const sortedData = Array.from(monthlyData.values())
      .sort((a, b) => a.month.localeCompare(b.month));

    // 计算移动平均和趋势
    const trends = sortedData.map((data, index) => {
      const movingAvg = this.calculateMovingAverage(sortedData, index, 3);
      const trend = index > 0 ? this.calculateTrendDirection(sortedData, index) : 'stable';

      return {
        ...data,
        categories: Object.fromEntries(data.categories),
        movingAverage: movingAvg,
        trend: trend
      };
    });

    return trends;
  }

  /**
   * 计算季节性模式
   */
  calculateSeasonalPatterns() {
    const seasonalData = {
      quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
      monthly: {},
      insights: []
    };

    // 按季度和月份统计
    this.featureData.forEach(item => {
      const quarter = `Q${item.季度}`;
      const month = item.月;

      seasonalData.quarterly[quarter] += item.月度总金额 || 0;

      if (!seasonalData.monthly[month]) {
        seasonalData.monthly[month] = 0;
      }
      seasonalData.monthly[month] += item.月度总金额 || 0;
    });

    // 计算季节性洞察
    const quarterValues = Object.values(seasonalData.quarterly);
    const maxQuarter = Object.keys(seasonalData.quarterly)[quarterValues.indexOf(Math.max(...quarterValues))];
    const minQuarter = Object.keys(seasonalData.quarterly)[quarterValues.indexOf(Math.min(...quarterValues))];

    seasonalData.insights.push({
      type: 'quarterly',
      message: `${maxQuarter}季度支出最高，${minQuarter}季度支出最低`,
      data: { maxQuarter, minQuarter }
    });

    // 月度模式分析
    const monthlyValues = Object.values(seasonalData.monthly);
    const avgMonthly = monthlyValues.reduce((sum, val) => sum + val, 0) / monthlyValues.length;
    const highMonths = Object.entries(seasonalData.monthly)
      .filter(([month, amount]) => amount > avgMonthly * 1.2)
      .map(([month]) => month);

    if (highMonths.length > 0) {
      seasonalData.insights.push({
        type: 'monthly',
        message: `${highMonths.join('、')}月份支出通常较高`,
        data: { highMonths }
      });
    }

    return seasonalData;
  }

  /**
   * 计算增长分析
   */
  calculateGrowthAnalysis() {
    const growthData = {
      monthOverMonth: [],
      yearOverYear: [],
      compoundGrowth: {},
      volatility: {}
    };

    // 按类别分析增长率
    const categoryGroups = this.groupByCategory(this.featureData);

    Object.entries(categoryGroups).forEach(([category, data]) => {
      const sortedData = data.sort((a, b) => a.年月.localeCompare(b.年月));

      const momGrowthRates = sortedData.map(item => item.较上月增长率 || 0);
      const yoyGrowthRates = sortedData.map(item => item.较去年同期增长率 || 0);

      growthData.monthOverMonth.push({
        category,
        rates: momGrowthRates,
        average: this.calculateMean(momGrowthRates),
        volatility: this.calculateStandardDeviation(momGrowthRates, this.calculateMean(momGrowthRates))
      });

      growthData.yearOverYear.push({
        category,
        rates: yoyGrowthRates,
        average: this.calculateMean(yoyGrowthRates),
        volatility: this.calculateStandardDeviation(yoyGrowthRates, this.calculateMean(yoyGrowthRates))
      });

      // 计算复合增长率
      if (sortedData.length >= 12) {
        const firstYear = sortedData[0].月度总金额;
        const lastYear = sortedData[sortedData.length - 1].月度总金额;
        const years = sortedData.length / 12;
        const cagr = Math.pow(lastYear / firstYear, 1 / years) - 1;

        growthData.compoundGrowth[category] = cagr;
      }
    });

    return growthData;
  }

  /**
   * 计算分类趋势
   */
  calculateCategoryTrends() {
    const categoryTrends = {};

    Object.entries(this.groupByCategory(this.featureData)).forEach(([category, data]) => {
      const sortedData = data.sort((a, b) => a.年月.localeCompare(b.年月));
      const amounts = sortedData.map(item => item.月度总金额 || 0);

      // 计算趋势线斜率
      const slope = this.calculateTrendSlope(amounts);
      const correlation = this.calculateTrendCorrelation(amounts);

      // 分析趋势特征
      const trendDirection = slope > 0.1 ? 'increasing' : slope < -0.1 ? 'decreasing' : 'stable';
      const trendStrength = Math.abs(correlation) > 0.7 ? 'strong' : Math.abs(correlation) > 0.4 ? 'moderate' : 'weak';

      categoryTrends[category] = {
        direction: trendDirection,
        strength: trendStrength,
        slope: slope,
        correlation: correlation,
        recentTrend: this.calculateRecentTrend(sortedData.slice(-6)), // 最近6个月
        forecast: this.simpleForecast(amounts, 3) // 预测未来3个月
      };
    });

    return categoryTrends;
  }

  /**
   * 生成趋势洞察
   */
  generateTrendInsights() {
    const insights = [];
    const categoryTrends = this.calculateCategoryTrends();
    const growthAnalysis = this.calculateGrowthAnalysis();

    // 分析增长最快的类别
    const fastestGrowing = growthAnalysis.yearOverYear
      .sort((a, b) => b.average - a.average)[0];

    if (fastestGrowing && fastestGrowing.average > 0.2) {
      insights.push({
        type: 'growth',
        level: 'info',
        title: '快速增长类别',
        message: `${fastestGrowing.category}同比增长最快，平均增长率${(fastestGrowing.average * 100).toFixed(1)}%`,
        recommendation: '建议关注该类别的支出控制和预算规划'
      });
    }

    // 分析波动最大的类别
    const mostVolatile = growthAnalysis.monthOverMonth
      .sort((a, b) => b.volatility - a.volatility)[0];

    if (mostVolatile && mostVolatile.volatility > 1.0) {
      insights.push({
        type: 'volatility',
        level: 'warning',
        title: '支出波动较大',
        message: `${mostVolatile.category}支出波动较大，标准差${mostVolatile.volatility.toFixed(2)}`,
        recommendation: '建议分析波动原因，制定更稳定的支出计划'
      });
    }

    // 分析趋势强烈的类别
    Object.entries(categoryTrends).forEach(([category, trend]) => {
      if (trend.strength === 'strong' && trend.direction !== 'stable') {
        insights.push({
          type: 'trend',
          level: trend.direction === 'increasing' ? 'warning' : 'success',
          title: `${category}趋势明显`,
          message: `${category}呈现${trend.direction === 'increasing' ? '上升' : '下降'}趋势，相关性${(trend.correlation * 100).toFixed(1)}%`,
          recommendation: trend.direction === 'increasing' ? '建议加强成本控制' : '支出控制效果良好'
        });
      }
    });

    return insights;
  }

  /**
   * 计算移动平均
   */
  calculateMovingAverage(data, index, window) {
    const start = Math.max(0, index - window + 1);
    const end = index + 1;
    const subset = data.slice(start, end);
    return subset.reduce((sum, item) => sum + item.totalAmount, 0) / subset.length;
  }

  /**
   * 计算趋势方向
   */
  calculateTrendDirection(data, index) {
    if (index < 2) return 'stable';

    const current = data[index].totalAmount;
    const previous = data[index - 1].totalAmount;
    const change = (current - previous) / previous;

    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  /**
   * 计算趋势斜率
   */
  calculateTrendSlope(values) {
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;

    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);

    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  /**
   * 计算趋势相关性
   */
  calculateTrendCorrelation(values) {
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;

    const meanX = this.calculateMean(x);
    const meanY = this.calculateMean(y);

    const numerator = x.reduce((sum, val, i) => sum + (val - meanX) * (y[i] - meanY), 0);
    const denomX = Math.sqrt(x.reduce((sum, val) => sum + Math.pow(val - meanX, 2), 0));
    const denomY = Math.sqrt(y.reduce((sum, val) => sum + Math.pow(val - meanY, 2), 0));

    return numerator / (denomX * denomY);
  }

  /**
   * 计算最近趋势
   */
  calculateRecentTrend(recentData) {
    if (recentData.length < 2) return 'insufficient_data';

    const amounts = recentData.map(item => item.月度总金额 || 0);
    const slope = this.calculateTrendSlope(amounts);

    if (slope > 0.05) return 'increasing';
    if (slope < -0.05) return 'decreasing';
    return 'stable';
  }

  /**
   * 简单预测
   */
  simpleForecast(values, periods) {
    if (values.length < 3) return [];

    const slope = this.calculateTrendSlope(values);
    const lastValue = values[values.length - 1];

    const forecast = [];
    for (let i = 1; i <= periods; i++) {
      forecast.push(lastValue + slope * i);
    }

    return forecast;
  }

  /**
   * 预测性分析
   * 基于历史数据进行费用预测和预算建议
   */
  generateForecast(periods = 6) {
    const forecastAnalysis = {
      totalForecast: this.calculateTotalForecast(periods),
      categoryForecasts: this.calculateCategoryForecasts(periods),
      budgetRecommendations: this.generateBudgetRecommendations(),
      riskAssessment: this.assessForecastRisks(periods),
      scenarios: this.generateScenarios(periods),
      insights: this.generateForecastInsights(periods)
    };

    return forecastAnalysis;
  }

  /**
   * 计算总体预测
   */
  calculateTotalForecast(periods) {
    const historicalData = this.getMonthlyTotals();
    const forecast = [];

    // 使用多种预测方法
    const linearForecast = this.linearTrendForecast(historicalData, periods);
    const seasonalForecast = this.seasonalForecast(historicalData, periods);
    const movingAvgForecast = this.movingAverageForecast(historicalData, periods);

    // 组合预测（加权平均）
    for (let i = 0; i < periods; i++) {
      const combinedValue = (
        linearForecast[i] * 0.4 +
        seasonalForecast[i] * 0.4 +
        movingAvgForecast[i] * 0.2
      );

      const confidence = this.calculateConfidence(i, periods);
      const currentDate = new Date();
      const forecastDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + i + 1);

      forecast.push({
        period: `${forecastDate.getFullYear()}-${String(forecastDate.getMonth() + 1).padStart(2, '0')}`,
        amount: Math.max(0, combinedValue),
        confidence: confidence,
        methods: {
          linear: linearForecast[i],
          seasonal: seasonalForecast[i],
          movingAverage: movingAvgForecast[i]
        }
      });
    }

    return forecast;
  }

  /**
   * 计算分类别预测
   */
  calculateCategoryForecasts(periods) {
    const categoryForecasts = {};
    const categoryGroups = this.groupByCategory(this.featureData);

    Object.entries(categoryGroups).forEach(([category, data]) => {
      const sortedData = data.sort((a, b) => a.年月.localeCompare(b.年月));
      const amounts = sortedData.map(item => item.月度总金额 || 0);

      if (amounts.length >= 3) {
        const forecast = this.simpleForecast(amounts, periods);
        const trend = this.calculateRecentTrend(sortedData.slice(-6));
        const volatility = this.calculateStandardDeviation(amounts, this.calculateMean(amounts));

        categoryForecasts[category] = {
          forecast: forecast,
          trend: trend,
          volatility: volatility,
          confidence: this.calculateCategoryConfidence(amounts, volatility),
          recommendations: this.generateCategoryRecommendations(category, trend, volatility)
        };
      }
    });

    return categoryForecasts;
  }

  /**
   * 生成预算建议
   */
  generateBudgetRecommendations() {
    const recommendations = [];
    const budgetAnalysis = this.analyzeBudgetExecution();
    const trendAnalysis = this.analyzeTrends();

    // 基于当前预算执行情况的建议
    budgetAnalysis.categoryAnalysis.forEach(category => {
      if (category.riskLevel === 'high') {
        recommendations.push({
          category: category.category,
          type: 'budget_control',
          priority: 'high',
          title: '预算控制建议',
          description: `${category.category}预算执行率已达${category.rate}%，建议立即控制支出`,
          suggestedBudget: category.budget * 1.1, // 建议增加10%预算
          actions: [
            '暂停非必要支出',
            '重新评估预算分配',
            '申请预算调整'
          ]
        });
      } else if (category.rate < 50) {
        recommendations.push({
          category: category.category,
          type: 'budget_utilization',
          priority: 'medium',
          title: '预算利用建议',
          description: `${category.category}预算执行率仅${category.rate}%，可考虑加快项目推进`,
          suggestedBudget: category.budget * 0.9, // 建议减少10%预算
          actions: [
            '加快相关项目进度',
            '评估预算合理性',
            '考虑预算重新分配'
          ]
        });
      }
    });

    // 基于趋势分析的建议
    Object.entries(trendAnalysis.categoryTrends).forEach(([category, trend]) => {
      if (trend.direction === 'increasing' && trend.strength === 'strong') {
        recommendations.push({
          category: category,
          type: 'trend_control',
          priority: 'medium',
          title: '趋势控制建议',
          description: `${category}支出呈强烈上升趋势，建议加强成本控制`,
          suggestedBudget: this.calculateTrendBasedBudget(category, trend),
          actions: [
            '分析支出增长原因',
            '制定成本控制措施',
            '设置支出预警机制'
          ]
        });
      }
    });

    return recommendations.sort((a, b) => {
      const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * 评估预测风险
   */
  assessForecastRisks(periods) {
    const risks = {
      overall: 'low',
      factors: [],
      mitigation: []
    };

    const totalForecast = this.calculateTotalForecast(periods);
    const budgetAnalysis = this.analyzeBudgetExecution();

    // 评估预测准确性风险
    const avgConfidence = totalForecast.reduce((sum, item) => sum + item.confidence, 0) / totalForecast.length;
    if (avgConfidence < 70) {
      risks.factors.push({
        type: 'accuracy',
        level: 'medium',
        description: '预测准确性较低，历史数据波动较大'
      });
    }

    // 评估预算超支风险
    const highRiskCategories = budgetAnalysis.categoryAnalysis.filter(cat => cat.riskLevel === 'high');
    if (highRiskCategories.length > 2) {
      risks.factors.push({
        type: 'budget_overrun',
        level: 'high',
        description: `${highRiskCategories.length}个类别存在预算超支风险`
      });
      risks.overall = 'high';
    }

    // 评估季节性风险
    const seasonalPatterns = this.calculateSeasonalPatterns();
    const currentQuarter = Math.ceil((new Date().getMonth() + 1) / 3);
    const nextQuarter = currentQuarter === 4 ? 1 : currentQuarter + 1;
    const nextQuarterKey = `Q${nextQuarter}`;

    if (seasonalPatterns.quarterly[nextQuarterKey] > seasonalPatterns.quarterly[`Q${currentQuarter}`] * 1.3) {
      risks.factors.push({
        type: 'seasonal',
        level: 'medium',
        description: `${nextQuarterKey}季度历史支出较高，需要额外关注`
      });
    }

    // 生成风险缓解建议
    risks.mitigation = this.generateRiskMitigation(risks.factors);

    return risks;
  }

  /**
   * 生成情景分析
   */
  generateScenarios(periods) {
    const baselineForecast = this.calculateTotalForecast(periods);

    const scenarios = {
      optimistic: baselineForecast.map(item => ({
        ...item,
        amount: item.amount * 0.85, // 乐观情况：减少15%
        description: '成本控制措施有效，支出低于预期'
      })),
      baseline: baselineForecast,
      pessimistic: baselineForecast.map(item => ({
        ...item,
        amount: item.amount * 1.2, // 悲观情况：增加20%
        description: '支出增长超出预期，需要额外预算'
      }))
    };

    // 计算各情景的总金额
    scenarios.summary = {
      optimistic: scenarios.optimistic.reduce((sum, item) => sum + item.amount, 0),
      baseline: scenarios.baseline.reduce((sum, item) => sum + item.amount, 0),
      pessimistic: scenarios.pessimistic.reduce((sum, item) => sum + item.amount, 0)
    };

    return scenarios;
  }

  /**
   * 生成预测洞察
   */
  generateForecastInsights(periods) {
    const insights = [];
    const totalForecast = this.calculateTotalForecast(periods);
    const categoryForecasts = this.calculateCategoryForecasts(periods);

    // 总体趋势洞察
    const totalTrend = this.calculateTrendDirection(totalForecast.map(item => ({ totalAmount: item.amount })), totalForecast.length - 1);
    insights.push({
      type: 'overall_trend',
      level: totalTrend === 'increasing' ? 'warning' : 'info',
      title: '总体预测趋势',
      message: `未来${periods}个月支出预计呈${totalTrend === 'increasing' ? '上升' : totalTrend === 'decreasing' ? '下降' : '稳定'}趋势`,
      data: { trend: totalTrend, periods: periods }
    });

    // 高风险类别洞察
    const highRiskCategories = Object.entries(categoryForecasts)
      .filter(([_, forecast]) => forecast.confidence < 60)
      .map(([category]) => category);

    if (highRiskCategories.length > 0) {
      insights.push({
        type: 'prediction_risk',
        level: 'warning',
        title: '预测不确定性',
        message: `${highRiskCategories.join('、')}等类别预测不确定性较高`,
        data: { categories: highRiskCategories }
      });
    }

    // 预算建议洞察
    const budgetRecommendations = this.generateBudgetRecommendations();
    const highPriorityRecs = budgetRecommendations.filter(rec => rec.priority === 'high');

    if (highPriorityRecs.length > 0) {
      insights.push({
        type: 'budget_action',
        level: 'danger',
        title: '紧急预算行动',
        message: `${highPriorityRecs.length}个类别需要立即采取预算控制措施`,
        data: { recommendations: highPriorityRecs }
      });
    }

    return insights;
  }

  /**
   * 线性趋势预测
   */
  linearTrendForecast(data, periods) {
    const amounts = data.map(item => item.amount);
    const slope = this.calculateTrendSlope(amounts);
    const lastValue = amounts[amounts.length - 1];

    const forecast = [];
    for (let i = 1; i <= periods; i++) {
      forecast.push(lastValue + slope * i);
    }

    return forecast;
  }

  /**
   * 季节性预测
   */
  seasonalForecast(data, periods) {
    const seasonalIndices = this.calculateSeasonalIndices(data);
    const trendData = this.deseasonalize(data, seasonalIndices);
    const trendForecast = this.linearTrendForecast(trendData, periods);

    const forecast = [];
    const currentMonth = new Date().getMonth();

    for (let i = 0; i < periods; i++) {
      const forecastMonth = (currentMonth + i + 1) % 12;
      const seasonalIndex = seasonalIndices[forecastMonth] || 1;
      forecast.push(trendForecast[i] * seasonalIndex);
    }

    return forecast;
  }

  /**
   * 移动平均预测
   */
  movingAverageForecast(data, periods, window = 6) {
    const amounts = data.map(item => item.amount);
    const recentAvg = amounts.slice(-window).reduce((sum, val) => sum + val, 0) / window;

    return Array(periods).fill(recentAvg);
  }

  /**
   * 计算预测置信度
   */
  calculateConfidence(period, totalPeriods) {
    // 置信度随预测期数递减
    const baseConfidence = 85;
    const decayRate = 5;
    return Math.max(40, baseConfidence - period * decayRate);
  }

  /**
   * 计算类别预测置信度
   */
  calculateCategoryConfidence(amounts, volatility) {
    const baseConfidence = 80;
    const volatilityPenalty = Math.min(30, volatility * 10);
    return Math.max(40, baseConfidence - volatilityPenalty);
  }

  /**
   * 生成类别建议
   */
  generateCategoryRecommendations(category, trend, volatility) {
    const recommendations = [];

    if (trend === 'increasing') {
      recommendations.push('关注支出增长趋势，制定控制措施');
    }

    if (volatility > 0.3) {
      recommendations.push('支出波动较大，建议制定更稳定的支出计划');
    }

    if (trend === 'stable' && volatility < 0.2) {
      recommendations.push('支出模式稳定，可维持当前管理方式');
    }

    return recommendations.length > 0 ? recommendations : ['继续监控支出情况'];
  }

  /**
   * 获取月度总计数据
   */
  getMonthlyTotals() {
    const monthlyData = new Map();

    this.featureData.forEach(item => {
      const month = item.年月;
      if (!monthlyData.has(month)) {
        monthlyData.set(month, { month, amount: 0 });
      }
      monthlyData.get(month).amount += item.月度总金额 || 0;
    });

    return Array.from(monthlyData.values()).sort((a, b) => a.month.localeCompare(b.month));
  }

  /**
   * 计算季节性指数
   */
  calculateSeasonalIndices(data) {
    const monthlyTotals = Array(12).fill(0);
    const monthlyCounts = Array(12).fill(0);

    data.forEach(item => {
      const month = new Date(item.month + '-01').getMonth();
      monthlyTotals[month] += item.amount;
      monthlyCounts[month]++;
    });

    const monthlyAverages = monthlyTotals.map((total, i) =>
      monthlyCounts[i] > 0 ? total / monthlyCounts[i] : 0
    );

    const overallAverage = monthlyAverages.reduce((sum, avg) => sum + avg, 0) / 12;

    return monthlyAverages.map(avg => overallAverage > 0 ? avg / overallAverage : 1);
  }

  /**
   * 去季节性
   */
  deseasonalize(data, seasonalIndices) {
    return data.map(item => {
      const month = new Date(item.month + '-01').getMonth();
      const seasonalIndex = seasonalIndices[month] || 1;
      return {
        ...item,
        amount: item.amount / seasonalIndex
      };
    });
  }

  /**
   * 计算基于趋势的预算
   */
  calculateTrendBasedBudget(category, trend) {
    const categoryData = this.featureData.filter(item => item.二级分类 === category);
    const latestData = categoryData[categoryData.length - 1];

    if (!latestData) return 0;

    const currentBudget = latestData.年度预算 || 0;
    const trendMultiplier = trend.direction === 'increasing' ? 1.15 :
                           trend.direction === 'decreasing' ? 0.95 : 1.0;

    return currentBudget * trendMultiplier;
  }

  /**
   * 生成风险缓解建议
   */
  generateRiskMitigation(riskFactors) {
    const mitigation = [];

    riskFactors.forEach(risk => {
      switch (risk.type) {
        case 'accuracy':
          mitigation.push('增加数据收集频率，提高预测准确性');
          break;
        case 'budget_overrun':
          mitigation.push('实施严格的预算控制措施');
          mitigation.push('建立预算预警机制');
          break;
        case 'seasonal':
          mitigation.push('提前准备季节性支出预算');
          mitigation.push('制定季节性成本控制策略');
          break;
      }
    });

    return [...new Set(mitigation)]; // 去重
  }

  /**
   * 格式化数字
   */
  formatNumber(value) {
    return parseFloat(value).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  /**
   * 加载模拟特征工程数据
   */
  async loadMockFeatureData() {
    // 这里返回模拟的特征工程数据
    return [
      {
        年月: '2024-05',
        二级分类: '其他杂项',
        月度总金额: 266486.96,
        月度平均金额: 38069.57,
        月度交易次数: 7,
        年: 2024,
        月: 5,
        季度: 2,
        较上月增长率: 6.47,
        较去年同期增长率: 8.87,
        年度预算: 788594.05,
        当年累计金额: 504084.77,
        预算完成率: 0.639,
        预算偏差: 0.222
      },
      {
        年月: '2024-04',
        二级分类: '办公用品费',
        月度总金额: 45230.80,
        月度平均金额: 15076.93,
        月度交易次数: 3,
        年: 2024,
        月: 4,
        季度: 2,
        较上月增长率: 0.12,
        较去年同期增长率: 0.15,
        年度预算: 377215.93,
        当年累计金额: 180450.25,
        预算完成率: 0.478,
        预算偏差: -0.042
      }
      // 更多数据...
    ];
  }
}

// 导出单例实例
export const expenseAnalyzer = new ExpenseAnalyzer();
