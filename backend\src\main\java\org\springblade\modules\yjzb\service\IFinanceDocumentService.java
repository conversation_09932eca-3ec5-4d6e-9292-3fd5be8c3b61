/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.yjzb.pojo.entity.FinanceDocumentEntity;
import org.springblade.modules.yjzb.pojo.dto.FinanceDocumentDTO;
import org.springblade.modules.yjzb.pojo.vo.FinanceDocumentVO;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

/**
 * 知识库文件 服务类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
public interface IFinanceDocumentService extends IService<FinanceDocumentEntity> {

    /**
     * 自定义分页查询
     *
     * @param page            分页对象
     * @param financeDocument 查询条件
     * @return 分页结果
     */
    IPage<FinanceDocumentVO> selectFinanceDocumentPage(IPage<FinanceDocumentVO> page,
            FinanceDocumentVO financeDocument);

    /**
     * 根据ID查询详情
     *
     * @param id 主键ID
     * @return 详情信息
     */
    FinanceDocumentVO getFinanceDocumentById(Long id);

    /**
     * 根据分类ID查询文档列表
     *
     * @param categoryId 分类ID
     * @return 文档列表
     */
    List<FinanceDocumentVO> getDocumentsByCategory(Long categoryId);

    /**
     * 根据知识库ID查询文档列表
     *
     * @param knowledgeId 知识库ID
     * @return 文档列表
     */
    List<FinanceDocumentVO> getDocumentsByKnowledge(Long knowledgeId);

    /**
     * 搜索文档
     *
     * @param keyword     关键词
     * @param knowledgeId 知识库ID
     * @param categoryId  分类ID
     * @param tags        标签列表
     * @return 文档列表
     */
    List<FinanceDocumentVO> searchDocuments(String keyword, Long knowledgeId, Long categoryId, List<String> tags);

    /**
     * 上传文档
     *
     * @param financeDocumentDTO 文档信息
     * @return 是否成功
     */
    boolean uploadDocument(FinanceDocumentDTO financeDocumentDTO);

    /**
     * 更新文档
     *
     * @param financeDocumentDTO 文档信息
     * @return 是否成功
     */
    boolean updateDocument(FinanceDocumentDTO financeDocumentDTO);

    /**
     * 删除文档
     *
     * @param ids 主键ID列表
     * @return 是否成功
     */
    boolean deleteDocument(String ids);

    /**
     * 下载文档
     *
     * @param id 文档ID
     * @return 文件路径
     */
    String downloadDocument(Long id);

    /**
     * 获取文档下载URL
     *
     * @param id 文档ID
     * @return 下载URL
     */
    String getDocumentDownloadUrl(Long id);

    /**
     * 下载文档数据
     *
     * @param id 文档ID
     * @return 文件数据字节数组
     */
    byte[] downloadDocumentData(Long id);

    /**
     * 增加浏览次数
     *
     * @param id 文档ID
     */
    void incrementViewCount(Long id);

    /**
     * 增加下载次数
     *
     * @param id 文档ID
     */
    void incrementDownloadCount(Long id);

    /**
     * 同步文档到Dify
     *
     * @param id 文档ID
     * @return 是否成功
     */
    boolean syncToDify(Long id);
}
