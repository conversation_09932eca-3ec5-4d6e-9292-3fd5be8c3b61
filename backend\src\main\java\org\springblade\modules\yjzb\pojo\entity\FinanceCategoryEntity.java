/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serial;

/**
 * 知识分类 实体类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Data
@TableName("yjzb_finance_category")
@Schema(description = "知识分类对象")
@EqualsAndHashCode(callSuper = true)
public class FinanceCategoryEntity extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 所属知识库ID
     */
    @Schema(description = "所属知识库ID")
    private Long knowledgeId;

    /**
     * 上级分类ID，0表示顶级分类
     */
    @Schema(description = "上级分类ID，0表示顶级分类")
    private Long parentId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String categoryName;

    /**
     * 分类编码
     */
    @Schema(description = "分类编码")
    private String categoryCode;

    /**
     * 分类标签（JSON数组格式）
     */
    @Schema(description = "分类标签（JSON数组格式）")
    private String categoryTags;

    /**
     * 层级深度
     */
    @Schema(description = "层级深度")
    private Integer levelNum;

    /**
     * 完整路径
     */
    @Schema(description = "完整路径")
    private String fullPath;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
