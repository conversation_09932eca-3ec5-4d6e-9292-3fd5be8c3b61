<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.FinanceCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="financeCategoryResultMap" type="org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="knowledge_id" property="knowledgeId"/>
        <result column="parent_id" property="parentId"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_code" property="categoryCode"/>
        <result column="category_tags" property="categoryTags"/>
        <result column="level_num" property="levelNum"/>
        <result column="full_path" property="fullPath"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, tenant_id, knowledge_id, parent_id, category_name, category_code, category_tags,
        level_num, full_path, sort, status, remark, is_deleted, create_user, create_dept, 
        create_time, update_user, update_time
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectFinanceCategoryPage" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceCategoryVO">
        SELECT 
            fc.id,
            fc.tenant_id,
            fc.knowledge_id,
            fc.parent_id,
            fc.category_name,
            fc.category_code,
            fc.category_tags,
            fc.level_num,
            fc.full_path,
            fc.sort,
            fc.status,
            fc.remark,
            fc.create_user,
            fc.create_dept,
            fc.create_time,
            fc.update_user,
            fc.update_time,
            fk.name AS knowledgeName,
            pc.category_name AS parentName,
            cu.real_name AS createUserName,
            uu.real_name AS updateUserName,
            (SELECT COUNT(*) FROM yjzb_finance_document fd WHERE fd.category_id = fc.id AND fd.is_deleted = 0) AS documentCount,
            CASE WHEN EXISTS(SELECT 1 FROM yjzb_finance_category child WHERE child.parent_id = fc.id AND child.is_deleted = 0) THEN true ELSE false END AS hasChildren
        FROM yjzb_finance_category fc
        LEFT JOIN yjzb_finance_knowledge fk ON fc.knowledge_id = fk.id
        LEFT JOIN yjzb_finance_category pc ON fc.parent_id = pc.id
        LEFT JOIN blade_user cu ON fc.create_user = cu.id
        LEFT JOIN blade_user uu ON fc.update_user = uu.id
        WHERE fc.is_deleted = 0
        <if test="financeCategory.knowledgeId != null">
            AND fc.knowledge_id = #{financeCategory.knowledgeId}
        </if>
        <if test="financeCategory.parentId != null">
            AND fc.parent_id = #{financeCategory.parentId}
        </if>
        <if test="financeCategory.categoryName != null and financeCategory.categoryName != ''">
            AND fc.category_name LIKE CONCAT('%', #{financeCategory.categoryName}, '%')
        </if>
        <if test="financeCategory.status != null">
            AND fc.status = #{financeCategory.status}
        </if>
        ORDER BY fc.sort ASC, fc.create_time DESC
    </select>

    <!-- 根据ID查询详情 -->
    <select id="selectFinanceCategoryById" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceCategoryVO">
        SELECT 
            fc.id,
            fc.tenant_id,
            fc.knowledge_id,
            fc.parent_id,
            fc.category_name,
            fc.category_code,
            fc.category_tags,
            fc.level_num,
            fc.full_path,
            fc.sort,
            fc.status,
            fc.remark,
            fc.create_user,
            fc.create_dept,
            fc.create_time,
            fc.update_user,
            fc.update_time,
            fk.name AS knowledgeName,
            pc.category_name AS parentName,
            cu.real_name AS createUserName,
            uu.real_name AS updateUserName,
            (SELECT COUNT(*) FROM yjzb_finance_document fd WHERE fd.category_id = fc.id AND fd.is_deleted = 0) AS documentCount,
            CASE WHEN EXISTS(SELECT 1 FROM yjzb_finance_category child WHERE child.parent_id = fc.id AND child.is_deleted = 0) THEN true ELSE false END AS hasChildren
        FROM yjzb_finance_category fc
        LEFT JOIN yjzb_finance_knowledge fk ON fc.knowledge_id = fk.id
        LEFT JOIN yjzb_finance_category pc ON fc.parent_id = pc.id
        LEFT JOIN blade_user cu ON fc.create_user = cu.id
        LEFT JOIN blade_user uu ON fc.update_user = uu.id
        WHERE fc.id = #{id} AND fc.is_deleted = 0
    </select>

    <!-- 查询树形结构 -->
    <select id="selectCategoryTree" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceCategoryVO">
        SELECT 
            fc.id,
            fc.knowledge_id,
            fc.parent_id,
            fc.category_name,
            fc.category_code,
            fc.category_tags,
            fc.level_num,
            fc.full_path,
            fc.sort,
            fc.status,
            (SELECT COUNT(*) FROM yjzb_finance_document fd WHERE fd.category_id = fc.id AND fd.is_deleted = 0) AS documentCount,
            CASE WHEN EXISTS(SELECT 1 FROM yjzb_finance_category child WHERE child.parent_id = fc.id AND child.is_deleted = 0) THEN true ELSE false END AS hasChildren
        FROM yjzb_finance_category fc
        WHERE fc.knowledge_id = #{knowledgeId} AND fc.is_deleted = 0 AND fc.status = 1
        ORDER BY fc.sort ASC, fc.create_time ASC
    </select>

    <!-- 查询子分类列表 -->
    <select id="selectChildrenByParentId" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceCategoryVO">
        SELECT 
            fc.id,
            fc.knowledge_id,
            fc.parent_id,
            fc.category_name,
            fc.category_code,
            fc.category_tags,
            fc.level_num,
            fc.full_path,
            fc.sort,
            fc.status,
            (SELECT COUNT(*) FROM yjzb_finance_document fd WHERE fd.category_id = fc.id AND fd.is_deleted = 0) AS documentCount,
            CASE WHEN EXISTS(SELECT 1 FROM yjzb_finance_category child WHERE child.parent_id = fc.id AND child.is_deleted = 0) THEN true ELSE false END AS hasChildren
        FROM yjzb_finance_category fc
        WHERE fc.parent_id = #{parentId} AND fc.is_deleted = 0 AND fc.status = 1
        ORDER BY fc.sort ASC, fc.create_time ASC
    </select>

    <!-- 统计分类下的文档数量 -->
    <select id="countDocumentsByCategory" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM yjzb_finance_document fd
        WHERE fd.category_id = #{categoryId} AND fd.is_deleted = 0
    </select>

</mapper>
