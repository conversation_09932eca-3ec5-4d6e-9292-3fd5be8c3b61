/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.service.impl;

import org.springblade.modules.yjzb.pojo.entity.IndicatorTypesEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorTypesVO;
import org.springblade.modules.yjzb.excel.IndicatorTypesExcel;
import org.springblade.modules.yjzb.mapper.IndicatorTypesMapper;
import org.springblade.modules.yjzb.service.IIndicatorTypesService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 指标类型 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Service
public class IndicatorTypesServiceImpl extends BaseServiceImpl<IndicatorTypesMapper, IndicatorTypesEntity> implements IIndicatorTypesService {

	@Override
	public IPage<IndicatorTypesVO> selectIndicatorTypesPage(IPage<IndicatorTypesVO> page, IndicatorTypesVO indicatorTypes) {
		return page.setRecords(baseMapper.selectIndicatorTypesPage(page, indicatorTypes));
	}


	@Override
	public List<IndicatorTypesExcel> exportIndicatorTypes(Wrapper<IndicatorTypesEntity> queryWrapper) {
		List<IndicatorTypesExcel> indicatorTypesList = baseMapper.exportIndicatorTypes(queryWrapper);
		//indicatorTypesList.forEach(indicatorTypes -> {
		//	indicatorTypes.setTypeName(DictCache.getValue(DictEnum.YES_NO, IndicatorTypes.getType()));
		//});
		return indicatorTypesList;
	}

}
