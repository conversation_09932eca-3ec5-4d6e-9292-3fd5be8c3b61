/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.yjzb.pojo.entity.FinanceKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceKnowledgeVO;

/**
 * 财务知识库包装类，返回视图层所需的字段
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
public class FinanceKnowledgeWrapper extends BaseEntityWrapper<FinanceKnowledgeEntity, FinanceKnowledgeVO> {

    public static FinanceKnowledgeWrapper build() {
        return new FinanceKnowledgeWrapper();
    }

    @Override
    public FinanceKnowledgeVO entityVO(FinanceKnowledgeEntity financeKnowledge) {
        FinanceKnowledgeVO financeKnowledgeVO = BeanUtil.copy(financeKnowledge, FinanceKnowledgeVO.class);
        return financeKnowledgeVO;
    }
}
