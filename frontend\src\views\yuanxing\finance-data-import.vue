<template>
  <!-- 财务税费管控 - 数据导入页面 -->
  <basic-container>
    <!-- 页面标题 -->
    <div class="page-header">
      <h3>数据导入管理</h3>
      <p class="page-desc">支持从Excel文件导入财务数据</p>
    </div>

    <!-- 导入操作区域 -->
    <el-row :gutter="20">
      <!-- 左侧：导入入口 -->
      <el-col :span="12">
        <div class="import-panel">
          <div class="panel-header">
            <h4>数据导入</h4>
          </div>

          <!-- 导入方式选择 -->
          <el-tabs v-model="activeImportTab" @tab-click="handleTabClick"  style="margin-left: 10px;">
            <!-- Excel文件导入 -->
            <el-tab-pane label="Excel导入" name="excel">
              <div class="import-content">
                <div
                        class="custom-upload-area"
                        @click="showImportDialog"
                        @drop="handleDrop"
                        @dragover="handleDragOver"
                        @dragenter="handleDragEnter"
                        @dragleave="handleDragLeave"
                >
                  <el-icon class="el-icon--upload">
                    <document />
                  </el-icon>
                  <div class="el-upload__text">
                    将Excel文件拖到此处，或<em>点击导入</em>
                  </div>
                  <div class="el-upload__tip">
                    支持.xlsx、.xls格式，单个文件不超过100MB<br/>
                    <strong>重要：</strong>导入文件的行列结构必须与所选模板完全一致
                  </div>
                </div>

                <!-- 模板下载 -->
                <div class="template-section">
                  <p>模板下载：</p>
                  <el-button size="small" type="text" @click="downloadTemplateQuick('expense')">
                    <i class="el-icon-download"></i> 三项费用数据模板
                  </el-button>
                  <el-button size="small" type="text" @click="downloadTemplateQuick('profit')">
                    <i class="el-icon-download"></i> 利润表数据模板
                  </el-button>
                  <el-button size="small" type="text" @click="downloadTemplateQuick('balance')">
                    <i class="el-icon-download"></i> 资产负债表数据模板
                  </el-button>
                  <el-button size="small" type="text" @click="downloadTemplateQuick('tax')">
                    <i class="el-icon-download"></i> 税利指标明细表数据模板
                  </el-button>
                </div>

                <!-- 数据映射配置 -->
                <div class="mapping-section" v-if="uploadedFiles.length">
                  <h5>数据映射配置</h5>
                  <el-table :data="columnMapping" border size="small">
                    <el-table-column prop="excelColumn" label="Excel列名" width="150" />
                    <el-table-column prop="systemField" label="系统字段" width="150">
                      <template #default="{ row }">
                        <el-select v-model="row.systemField" size="small" placeholder="选择字段">
                          <el-option v-for="field in systemFields" :key="field.value" :label="field.label" :value="field.value" />
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column prop="required" label="必填" width="80">
                      <template #default="{ row }">
                        <el-tag :type="row.required ? 'danger' : 'info'" size="small">
                          {{ row.required ? '必填' : '可选' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>

            <!-- 数据库导入 -->
            <!-- <el-tab-pane label="数据库导入" name="database">
              <div class="import-content">
                <el-form :model="dbImportForm" label-width="100px">
                  <el-form-item label="数据源">
                    <el-select v-model="dbImportForm.dataSource" placeholder="选择数据源">
                      <el-option label="财务主库(MySQL)" value="finance_main" />
                      <el-option label="ERP系统(Oracle)" value="erp_oracle" />
                      <el-option label="税务系统(PostgreSQL)" value="tax_pg" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="数据表">
                    <el-select v-model="dbImportForm.tableName" placeholder="选择数据表">
                      <el-option label="费用明细表" value="expense_detail" />
                      <el-option label="税务申报表" value="tax_report" />
                      <el-option label="收入明细表" value="income_detail" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="导入条件">
                    <el-input
                      v-model="dbImportForm.condition"
                      type="textarea"
                      :rows="3"
                      placeholder="输入WHERE条件，例如：date >= '2024-01-01'"
                    />
                  </el-form-item>
                  <el-form-item label="预览数据">
                    <el-button size="small" @click="previewDbData">预览</el-button>
                    <el-button size="small" type="primary" @click="startDbImport">开始导入</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane> -->

            <!-- 财务系统API导入 -->
            <!-- <el-tab-pane label="系统API导入" name="api">
              <div class="import-content">
                <el-form :model="apiImportForm" label-width="100px">
                  <el-form-item label="API接口">
                    <el-select v-model="apiImportForm.apiEndpoint" placeholder="选择API接口">
                      <el-option label="用友U8 财务接口" value="yonyou_u8" />
                      <el-option label="金蝶KIS 费用接口" value="kingdee_kis" />
                      <el-option label="SAP ERP 接口" value="sap_erp" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="认证信息">
                    <el-input v-model="apiImportForm.apiKey" placeholder="API Key" />
                  </el-form-item>
                  <el-form-item label="同步周期">
                    <el-radio-group v-model="apiImportForm.syncFrequency">
                      <el-radio label="manual">手动同步</el-radio>
                      <el-radio label="daily">每日同步</el-radio>
                      <el-radio label="weekly">每周同步</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="testApiConnection">测试连接</el-button>
                    <el-button type="success" @click="startApiImport">开始同步</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane> -->
          </el-tabs>


        </div>
      </el-col>

      <!-- 右侧：实时状态和校验结果 -->
      <el-col :span="12">
        <!-- 导入进度 -->
        <div class="progress-panel" v-if="importProgress.show">
          <!--          <div class="panel-header">-->
          <!--            <h4>导入进度</h4>-->
          <!--          </div>-->
          <div class="progress-content">
            <!--            <el-progress :percentage="importProgress.percentage" :status="importProgress.status" />-->
            <!--            <p class="progress-text">{{ importProgress.text }}</p>-->
            <div class="progress-stats">
              <span>总计：{{ importProgress.total }}</span>
              <span style="color: rgba(0,215,0,1)">成功：{{ importProgress.success }}</span>
              <span style="color: rgba(255,0,0,1)">失败：{{ importProgress.failed }}</span>
            </div>
          </div>
        </div>

        <!-- 校验结果 -->
        <div class="validation-panel">
          <div class="panel-header">
            <h4>数据校验结果</h4>
            <el-badge :value="validationErrors.length" class="validation-badge">
              <i class="el-icon-warning" v-if="validationErrors.length"></i>
            </el-badge>
          </div>
          <div class="validation-content">
            <el-empty v-if="!validationErrors.length" description="暂无校验问题" :image-size="80" />
            <div v-else>
              <el-alert
                      v-for="error in validationErrors"
                      :key="error.id"
                      :title="error.message"
                      :type="error.type"
                      :description="error.detail"
                      show-icon
                      :closable="false"
                      style="margin-bottom: 10px;"
              />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 导入历史记录 -->
    <div class="history-panel">
      <div class="panel-header">
        <h4>导入历史记录</h4>
        <div class="panel-actions">
          <el-input
                  v-model="historySearch"
                  placeholder="搜索历史记录"
                  size="small"
                  prefix-icon="el-icon-search"
                  style="width: 200px; margin-right: 10px;"
                  @input="handleSearchChange"
                  clearable
          />
          <el-button size="small" @click="refreshHistory">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>

      <el-table :data="filteredHistory" stripe border v-loading="historyLoading">
        <el-table-column prop="createTime" label="导入时间" width="160" />
        <el-table-column prop="importType" label="导入类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getImportTypeTag(row.importType)" size="small">
              {{ row.importTypeName || getImportTypeName(row.importType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fileName" label="文件/来源" />
        <el-table-column prop="totalCount" label="总记录数" width="100" align="center" />
        <el-table-column prop="successCount" label="成功数" width="100" align="center" />
        <el-table-column prop="failCount" label="失败数" width="100" align="center" />
        <el-table-column prop="importStatus" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.importStatus)" size="small">
              {{ row.importStatusName || getImportStatusName(row.importStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button size="mini" type="text" @click="viewDetail(row)">详情</el-button>
            <!--            <el-button size="mini" type="text" @click="downloadLog(row)" v-if="row.logFile">日志</el-button>-->
            <!--            <el-button size="mini" type="text" @click="retryImport(row)" v-if="row.importStatus === 'FAILED'">重试</el-button>-->
          </template>
        </el-table-column>
      </el-table>

      <div style="margin-top: 15px; text-align: right;">
        <el-pagination
                background
                layout="total, sizes, prev, pager, next"
                :total="historyTotal"
                :page-size="pageSize"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 数据导入对话框 -->
    <el-dialog
            v-model="importDialogVisible"
            title="数据导入"
            width="600px"
            :close-on-click-modal="false"
    >
      <el-form :model="importForm" :rules="importRules" ref="importFormRef" label-width="120px">

        <el-form-item label="导入文件" prop="file">
          <el-upload
                  ref="importUploadRef"
                  action="#"
                  :auto-upload="false"
                  :on-change="handleImportFileChange"
                  :before-upload="beforeImportUpload"
                  accept=".xlsx,.xls"
                  :limit="1"
                  :file-list="importFileList"
                  drag
                  style="width: 100%"
          >
            <el-icon class="el-icon--upload">
              <document />
            </el-icon>
            <div class="el-upload__text">
              将Excel文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持.xlsx、.xls格式，单个文件不超过100MB<br/>
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="数据期间" prop="period">
          <el-date-picker
                  v-model="importForm.period"
                  type="month"
                  placeholder="选择数据期间"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="选择模板" prop="templateType">
          <el-select v-model="importForm.templateType" placeholder="请选择导入模板" style="width: 100%">
            <el-option label="利润表数据模板" value="profit" />
            <el-option label="资产负债表数据模板" value="balance" />
            <el-option label="三项费用数据模板" value="expense" />
            <el-option label="税利指标明细表数据模板" value="tax" />
          </el-select>
        </el-form-item>

      </el-form>

      <!-- 导入进度 -->
      <div v-if="importProgress.show" style="margin-top: 20px;">
        <el-progress :percentage="importProgress.percentage" :status="importProgress.status" />
        <p style="margin: 10px 0; text-align: center; color: #909399;">{{ importProgress.text }}</p>
        <div style="display: flex; justify-content: space-between; font-size: 14px; color: #606266;">
          <span>总计：{{ importProgress.total }}</span>
          <span>成功：{{ importProgress.success }}</span>
          <span>失败：{{ importProgress.failed }}</span>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button
                  type="primary"
                  @click="confirmImport"
                  :loading="importing"
                  :disabled="!importForm.file"
          >
            开始导入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入详情对话框 -->
    <el-dialog
            v-model="detailDialogVisible"
            title="导入详情"
            width="800px"
            :close-on-click-modal="false"
    >
      <div v-if="currentImportDetail" class="import-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4>基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名">{{ currentImportDetail.fileName }}</el-descriptions-item>
            <el-descriptions-item label="文件大小">{{ currentImportDetail.fileSizeFormatted || formatFileSize(currentImportDetail.fileSize) }}</el-descriptions-item>
            <el-descriptions-item label="导入类型">{{ currentImportDetail.importTypeName || getImportTypeName(currentImportDetail.importType) }}</el-descriptions-item>
            <el-descriptions-item label="模板类型">{{ currentImportDetail.templateTypeName || getTemplateTypeName(currentImportDetail.templateType) }}</el-descriptions-item>
            <el-descriptions-item label="数据期间">{{ currentImportDetail.period }}</el-descriptions-item>
            <el-descriptions-item label="导入时间">{{ currentImportDetail.createTime }}</el-descriptions-item>
            <el-descriptions-item label="导入耗时">{{ currentImportDetail.importDurationFormatted || formatDuration(currentImportDetail.importDuration) }}</el-descriptions-item>
            <el-descriptions-item label="导入状态">
              <el-tag :type="getStatusTag(currentImportDetail.importStatus)" size="small">
                {{ currentImportDetail.importStatusName || getImportStatusName(currentImportDetail.importStatus) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 统计信息 -->
        <div class="detail-section">
          <h4>统计信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-card">
                <div class="stat-number">{{ currentImportDetail.totalCount || 0 }}</div>
                <div class="stat-label">总记录数</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-card success">
                <div class="stat-number">{{ currentImportDetail.successCount || 0 }}</div>
                <div class="stat-label">成功导入</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-card error">
                <div class="stat-number">{{ currentImportDetail.failCount || 0 }}</div>
                <div class="stat-label">失败记录</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 导入结果详情 -->
        <div class="detail-section" v-if="currentImportDetail.importResult">
          <h4>导入结果详情</h4>
          <div class="result-content">
            <el-tabs v-model="activeDetailTab">
              <!-- JSON原始数据 -->
              <el-tab-pane label="原始数据" name="raw">
                <div class="json-viewer">
                  <pre>{{ formatJsonString(currentImportDetail.importResult) }}</pre>
                </div>
              </el-tab-pane>

              <!-- 格式化显示 -->
              <el-tab-pane label="格式化显示" name="formatted">
                <div class="formatted-result">
                  <div v-if="parsedImportResult">
                    <!-- 成功信息 -->
                    <div v-if="parsedImportResult.success" class="result-item success">
                      <el-icon><SuccessFilled /></el-icon>
                      <span>导入成功</span>
                    </div>
                    <div v-else class="result-item error">
                      <el-icon><CircleCloseFilled /></el-icon>
                      <span>导入失败</span>
                    </div>

                    <!-- 统计信息 -->
                    <div class="result-stats">
                      <div class="stat-item">
                        <span class="label">总记录数：</span>
                        <span class="value">{{ parsedImportResult.totalCount || 0 }}</span>
                      </div>
                      <div class="stat-item">
                        <span class="label">成功数：</span>
                        <span class="value success">{{ parsedImportResult.successCount || 0 }}</span>
                      </div>
                      <div class="stat-item">
                        <span class="label">失败数：</span>
                        <span class="value error">{{ parsedImportResult.failCount || 0 }}</span>
                      </div>
                    </div>

                    <!-- 错误信息 -->
                    <div v-if="parsedImportResult.errors && parsedImportResult.errors.length" class="result-errors">
                      <h5>错误信息</h5>
                      <el-alert
                              v-for="(error, index) in parsedImportResult.errors"
                              :key="index"
                              :title="error"
                              type="error"
                              :closable="false"
                              style="margin-bottom: 8px;"
                      />
                    </div>

                    <!-- 未找到的指标 -->
                    <div v-if="parsedImportResult.notFoundIndicators && parsedImportResult.notFoundIndicators.length" class="result-not-found">
                      <h5>未找到的指标</h5>
                      <el-alert
                              v-for="(indicator, index) in parsedImportResult.notFoundIndicators"
                              :key="index"
                              :title="indicator"
                              type="warning"
                              :closable="false"
                              style="margin-bottom: 8px;"
                      />
                    </div>

                    <!-- 其他信息 -->
                    <div v-if="hasOtherInfo" class="result-other">
                      <h5>其他信息</h5>
                      <div v-for="(value, key) in otherInfo" :key="key" class="other-item">
                        <span class="label">{{ key }}：</span>
                        <span class="value">{{ formatValue(value) }}</span>
                      </div>
                    </div>
                  </div>
                  <div v-else class="no-data">
                    <el-empty description="无法解析导入结果数据" :image-size="80" />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <!-- 错误信息 -->
        <div class="detail-section" v-if="currentImportDetail.errorMessage">
          <h4>错误信息</h4>
          <el-alert
                  :title="currentImportDetail.errorMessage"
                  type="error"
                  :closable="false"
                  show-icon
          />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import { Document, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { importExcelData } from '@/api/yjzb/indicatorValues'
import { downloadTemplate as downloadTemplateApi } from '@/api/yjzb/indicator'
import { searchImportHistory } from '@/api/yjzb/indicatorImportHistory'

export default {
  name: 'FinanceDataImport',
  components: {
    Document,
    SuccessFilled,
    CircleCloseFilled
  },
  data() {
    return {
      activeImportTab: 'excel',
      importing: false,
      historySearch: '',
      pageSize: 10,
      currentPage: 1,
      historyTotal: 0,
      historyLoading: false,
      searchTimer: null,

      // 文件上传
      uploadedFiles: [],

      // 数据映射
      columnMapping: [],
      systemFields: [
        { label: '日期', value: 'date' },
        { label: '部门', value: 'department' },
        { label: '费用类型', value: 'expenseType' },
        { label: '金额', value: 'amount' },
        { label: '说明', value: 'description' },
        { label: '申请人', value: 'applicant' }
      ],

      // 数据库导入表单
      dbImportForm: {
        dataSource: '',
        tableName: '',
        condition: ''
      },

      // API导入表单
      apiImportForm: {
        apiEndpoint: '',
        apiKey: '',
        syncFrequency: 'manual'
      },

      // 导入对话框
      importDialogVisible: false,
      importForm: {
        period: '',
        templateType: '',
        file: null
      },
      importFileList: [],
      importRules: {
        period: [
          { required: true, message: '请选择数据期间', trigger: 'change' }
        ],
        templateType: [
          { required: true, message: '请选择导入模板', trigger: 'change' }
        ],
        file: [
          { required: true, message: '请选择导入文件', trigger: 'change' }
        ]
      },

      // 导入进度
      importProgress: {
        show: false,
        percentage: 0,
        status: 'active',
        text: '',
        total: 0,
        success: 0,
        failed: 0
      },

      // 校验错误
      validationErrors: [
        
      ],

      // 导入历史
      importHistory: [],

      // 详情对话框
      detailDialogVisible: false,
      currentImportDetail: null,
      activeDetailTab: 'formatted'
    }
  },
  computed: {
    filteredHistory() {
      // 现在直接返回从API获取的数据，搜索在后端处理
      return this.importHistory
    },

    // 解析导入结果JSON
    parsedImportResult() {
      if (!this.currentImportDetail || !this.currentImportDetail.importResult) {
        return null
      }
      try {
        return JSON.parse(this.currentImportDetail.importResult)
      } catch (error) {
        console.error('解析导入结果失败:', error)
        return null
      }
    },

    // 检查是否有其他信息
    hasOtherInfo() {
      if (!this.parsedImportResult) return false
      const knownKeys = ['success', 'totalCount', 'successCount', 'failCount', 'errors', 'notFoundIndicators']
      return Object.keys(this.parsedImportResult).some(key => !knownKeys.includes(key))
    },

    // 获取其他信息
    otherInfo() {
      if (!this.parsedImportResult) return {}
      const knownKeys = ['success', 'totalCount', 'successCount', 'failCount', 'errors', 'notFoundIndicators']
      const other = {}
      Object.keys(this.parsedImportResult).forEach(key => {
        if (!knownKeys.includes(key)) {
          other[key] = this.parsedImportResult[key]
        }
      })
      return other
    }
  },
  methods: {
    // 标签页切换
    handleTabClick(tab) {
      console.log('切换到标签页:', tab.name)
    },

    // 拖拽处理方法
    handleDragOver(e) {
      e.preventDefault()
      e.stopPropagation()
    },

    handleDragEnter(e) {
      e.preventDefault()
      e.stopPropagation()
      e.target.closest('.custom-upload-area').classList.add('is-dragover')
    },

    handleDragLeave(e) {
      e.preventDefault()
      e.stopPropagation()
      e.target.closest('.custom-upload-area').classList.remove('is-dragover')
    },

    handleDrop(e) {
      e.preventDefault()
      e.stopPropagation()
      e.target.closest('.custom-upload-area').classList.remove('is-dragover')

      const files = e.dataTransfer.files
      if (files.length > 0) {
        const file = files[0]
        // 验证文件类型
        if (this.validateFileType(file)) {
          this.showImportDialogWithFile(file)
        }
      }
    },

    // 验证文件类型
    validateFileType(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel' ||
                     file.name.toLowerCase().endsWith('.xlsx') ||
                     file.name.toLowerCase().endsWith('.xls')
      const isLt100M = file.size / 1024 / 1024 < 100

      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt100M) {
        this.$message.error('文件大小不能超过100MB!')
        return false
      }
      return true
    },

    // 显示导入对话框并传递文件
    showImportDialogWithFile(file) {
      this.showImportDialog()
      this.importForm.file = file
      this.importFileList = [{
        name: file.name,
        size: file.size,
        raw: file,
        status: 'ready'
      }]

      // 智能解析文件名，自动填写期间和选择模板
      this.parseFileNameAndAutoFill(file.name)

      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.importFormRef) {
          this.$refs.importFormRef.validateField('file')
        }
      })
    },

    // 解析文件名并自动填写期间和模板
    parseFileNameAndAutoFill(fileName) {
      let autoFillMessages = []

      // 1. 解析数据期间
      const period = this.extractPeriodFromFileName(fileName)
      if (period) {
        this.importForm.period = period
        autoFillMessages.push(`数据期间: ${period}`)
      }

      // 2. 智能选择模板
      const templateType = this.selectTemplateByFileName(fileName)
      if (templateType) {
        this.importForm.templateType = templateType
        const templateNames = {
          profit: '利润表数据模板',
          balance: '资产负债表数据模板',
          expense: '三项费用数据模板',
          tax: '税利指标明细表数据模板'
        }
        autoFillMessages.push(`模板类型: ${templateNames[templateType]}`)
      }

      // 显示自动识别结果
      if (autoFillMessages.length > 0) {
        this.$message.success(`智能识别成功！已自动填写：${autoFillMessages.join('，')}`)
      } else {
        // 如果没有识别到任何信息，给出提示
        this.$message.info('未能自动识别文件信息，请手动选择数据期间和模板类型')
      }
    },

    // 从文件名中提取数据期间，支持多种日期格式
    extractPeriodFromFileName(fileName) {
      const patterns = [
        // YYYYMM格式 (如: 202505)
        { regex: /(\d{4})(\d{2})/, yearIndex: 1, monthIndex: 2 },
        // YYYY-MM格式 (如: 2025-05)
        { regex: /(\d{4})-(\d{1,2})/, yearIndex: 1, monthIndex: 2 },
        // YYYY年MM月格式 (如: 2025年05月, 2025年5月)
        { regex: /(\d{4})年(\d{1,2})月/, yearIndex: 1, monthIndex: 2 },
        // MM月YYYY年格式 (如: 05月2025年, 5月2025年)
        { regex: /(\d{1,2})月(\d{4})年/, yearIndex: 2, monthIndex: 1 },
        // YYYY.MM格式 (如: 2025.05)
        { regex: /(\d{4})\.(\d{1,2})/, yearIndex: 1, monthIndex: 2 },
        // YYYY_MM格式 (如: 2025_05)
        { regex: /(\d{4})_(\d{1,2})/, yearIndex: 1, monthIndex: 2 },
        // YYYY/MM格式 (如: 2025/05)
        { regex: /(\d{4})\/(\d{1,2})/, yearIndex: 1, monthIndex: 2 },
        // MM/YYYY格式 (如: 05/2025)
        { regex: /(\d{1,2})\/(\d{4})/, yearIndex: 2, monthIndex: 1 },
        // YYYY年M月格式 (如: 2025年5月)
        { regex: /(\d{4})年(\d{1,2})月/, yearIndex: 1, monthIndex: 2 },
        // 中文月份格式 (如: 2025年五月, 2025年十二月)
        { regex: /(\d{4})年(一|二|三|四|五|六|七|八|九|十|十一|十二)月/, yearIndex: 1, monthIndex: 2, isChineseMonth: true }
      ]

      for (const pattern of patterns) {
        const match = fileName.match(pattern.regex)
        if (match) {
          let year = match[pattern.yearIndex]
          let month = match[pattern.monthIndex]

          // 处理中文月份
          if (pattern.isChineseMonth) {
            const chineseMonths = {
              '一': '01', '二': '02', '三': '03', '四': '04', '五': '05', '六': '06',
              '七': '07', '八': '08', '九': '09', '十': '10', '十一': '11', '十二': '12'
            }
            month = chineseMonths[month] || month
          } else {
            month = month.padStart(2, '0')
          }

          // 验证年月的合理性
          const yearNum = parseInt(year)
          const monthNum = parseInt(month)
          if (yearNum >= 2020 && yearNum <= 2030 && monthNum >= 1 && monthNum <= 12) {
            return `${year}-${month}`
          }
        }
      }

      return null
    },

    // 根据文件名选择最相似的模板
    selectTemplateByFileName(fileName) {
      const templateConfig = {
        'profit': {
          keywords: ['利润', '利润表', '损益', '损益表', 'profit', 'income', 'pl'],
          exactMatches: ['利润表', '损益表'],
          weight: 1.0
        },
        'balance': {
          keywords: ['资产', '负债', '资产负债', '资产负债表', 'balance', 'sheet', 'bs'],
          exactMatches: ['资产负债表', '资产负债'],
          weight: 1.0
        },
        'expense': {
          keywords: ['费用', '三项费用', '销售费用', '管理费用', '财务费用', 'expense', 'cost', '三费'],
          exactMatches: ['三项费用', '三费'],
          weight: 1.0
        },
        'tax': {
          keywords: ['税', '税利', '税务', '税利', '指标明细', 'tax', 'revenue', '明细表'],
          exactMatches: ['税利指标', '税利指标', '指标明细表'],
          weight: 1.0
        }
      }

      let maxScore = 0
      let bestTemplate = null
      const lowerFileName = fileName.toLowerCase()
      const cleanFileName = fileName.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '') // 移除特殊字符

      for (const [templateType, config] of Object.entries(templateConfig)) {
        let score = 0

        // 1. 精确匹配检查（最高优先级）
        for (const exactMatch of config.exactMatches) {
          if (lowerFileName.includes(exactMatch.toLowerCase())) {
            score += 50 // 精确匹配给予最高分
            break
          }
        }

        // 2. 关键词匹配
        for (const keyword of config.keywords) {
          const lowerKeyword = keyword.toLowerCase()
          if (lowerFileName.includes(lowerKeyword)) {
            // 基础分数：关键词长度 * 2
            let keywordScore = keyword.length * 2

            // 完整词匹配加分
            const regex = new RegExp(`\\b${lowerKeyword}\\b`, 'i')
            if (regex.test(lowerFileName)) {
              keywordScore += 10
            }

            // 关键词在文件名开头或结尾加分
            if (lowerFileName.startsWith(lowerKeyword) || lowerFileName.endsWith(lowerKeyword)) {
              keywordScore += 5
            }

            score += keywordScore
          }
        }

        // 3. 字符相似度匹配（模糊匹配）
        const templateNames = {
          profit: '利润表',
          balance: '资产负债表',
          expense: '三项费用',
          tax: '税利指标明细表'
        }

        const similarity = this.calculateStringSimilarity(cleanFileName, templateNames[templateType])
        if (similarity > 0.3) { // 相似度阈值
          score += similarity * 20
        }

        // 4. 应用权重
        score *= config.weight

        if (score > maxScore) {
          maxScore = score
          bestTemplate = templateType
        }
      }

      // 只有当相似度足够高时才自动选择
      return maxScore >= 8 ? bestTemplate : null
    },

    // 计算字符串相似度（简单的编辑距离算法）
    calculateStringSimilarity(str1, str2) {
      const len1 = str1.length
      const len2 = str2.length

      if (len1 === 0) return len2 === 0 ? 1 : 0
      if (len2 === 0) return 0

      const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0))

      for (let i = 0; i <= len1; i++) matrix[i][0] = i
      for (let j = 0; j <= len2; j++) matrix[0][j] = j

      for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
          const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
          matrix[i][j] = Math.min(
            matrix[i - 1][j] + 1,      // 删除
            matrix[i][j - 1] + 1,      // 插入
            matrix[i - 1][j - 1] + cost // 替换
          )
        }
      }

      const maxLen = Math.max(len1, len2)
      return (maxLen - matrix[len1][len2]) / maxLen
    },



    // 模板下载
    downloadTemplate(type) {
      this.$message.success(`正在下载${type}数据模板...`)
    },

    // 开始导入
    startImport() {
      this.importing = true
      this.importProgress.show = true
      this.importProgress.percentage = 0
      this.importProgress.status = 'active'
      this.importProgress.text = '正在解析文件...'
      this.importProgress.total = 156
      this.importProgress.success = 0
      this.importProgress.failed = 0

      // 模拟导入进度
      const timer = setInterval(() => {
        this.importProgress.percentage += 10
        this.importProgress.success = Math.floor(this.importProgress.total * this.importProgress.percentage / 100)

        if (this.importProgress.percentage <= 30) {
          this.importProgress.text = '正在校验数据格式...'
        } else if (this.importProgress.percentage <= 70) {
          this.importProgress.text = '正在导入数据...'
        } else if (this.importProgress.percentage <= 90) {
          this.importProgress.text = '正在处理关联关系...'
        } else {
          this.importProgress.text = '导入完成'
          this.importProgress.status = 'success'
          this.importProgress.failed = 2
          this.importProgress.success = 154
          this.importing = false
          clearInterval(timer)
          this.$message.success('数据导入完成！')
        }
      }, 500)
    },

    // 清空文件
    clearFiles() {
      this.uploadedFiles = []
      this.columnMapping = []
      this.$refs.excelUpload.clearFiles()
    },

    // 数据库相关
    previewDbData() {
      this.$message.info('正在预览数据库数据...')
    },

    startDbImport() {
      this.$message.success('开始数据库导入...')
    },

    // API相关
    testApiConnection() {
      this.$message.success('API连接测试成功！')
    },

    startApiImport() {
      this.$message.success('开始API数据同步...')
    },

    // 历史记录相关
    async refreshHistory() {
      await this.loadImportHistory()
    },

    // 加载导入历史记录
    async loadImportHistory() {
      this.historyLoading = true
      try {
        const response = await searchImportHistory(this.currentPage, this.pageSize, {
          searchKeyword: this.historySearch
        })

        if (response.data.success) {
          this.importHistory = response.data.data.records || []
          this.historyTotal = response.data.data.total || 0
        } else {
          this.$message.error('加载历史记录失败')
        }
      } catch (error) {
        console.error('加载历史记录失败:', error)
        this.$message.error('加载历史记录失败：' + error.message)
      } finally {
        this.historyLoading = false
      }
    },

    async viewDetail(row) {
      try {
        // 调用API获取详细信息
        const response = await this.getImportDetail(row.id)
        if (response.data.success) {
          this.currentImportDetail = response.data.data
          this.detailDialogVisible = true
        } else {
          this.$message.error('获取导入详情失败')
        }
      } catch (error) {
        console.error('获取导入详情失败:', error)
        this.$message.error('获取导入详情失败：' + error.message)
      }
    },

    // 获取导入详情
    async getImportDetail(id) {
      const { getDetail } = await import('@/api/yjzb/indicatorImportHistory')
      return await getDetail(id)
    },

    downloadLog(row) {
      this.$message.success(`正在下载日志文件: ${row.logFile}`)
    },

    retryImport(row) {
      this.$message.info(`重试导入: ${row.fileName}`)
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadImportHistory()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadImportHistory()
    },

    // 工具方法
    getImportTypeTag(type) {
      const typeMap = {
        excel: 'primary',
        database: 'success',
        api: 'warning'
      }
      return typeMap[type] || 'info'
    },

    getImportTypeName(type) {
      const typeMap = {
        excel: 'Excel导入',
        database: '数据库导入',
        api: 'API导入'
      }
      return typeMap[type] || type
    },

    getStatusTag(status) {
      const statusMap = {
        SUCCESS: 'success',
        PARTIAL: 'warning',
        FAILED: 'danger'
      }
      return statusMap[status] || 'info'
    },

    getImportStatusName(status) {
      const statusMap = {
        SUCCESS: '成功',
        PARTIAL: '部分成功',
        FAILED: '失败'
      }
      return statusMap[status] || status
    },

    // 快速下载模板（不需要选择模板类型）
    async downloadTemplateQuick(templateType) {
      const templateNames = {
        profit: '利润表数据模板',
        balance: '资产负债表数据模板',
        expense: '三项费用数据模板',
        tax: '税利指标明细表数据模板'
      }

      try {
        const response = await downloadTemplateApi(templateType)

        // 创建下载链接
        const blob = new Blob([response.data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = templateNames[templateType] + '.csv'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success(`正在下载${templateNames[templateType]}...`)
      } catch (error) {
        this.$message.error('下载模板失败：' + error.message)
      }
    },

    // 导入对话框相关方法
    showImportDialog() {
      this.importDialogVisible = true
      // 重置表单，但保留已选择的文件
      this.importForm.period = ''
      this.importForm.templateType = ''
      if (!this.importForm.file) {
        this.importForm.file = null
        this.importFileList = []
      }
      this.importProgress.show = false
    },

    handleImportFileChange(file, fileList) {
      this.importFileList = fileList
      this.importForm.file = file.raw

      // 智能解析文件名，自动填写期间和选择模板
      if (file && file.name) {
        this.parseFileNameAndAutoFill(file.name)
      }

      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.importFormRef) {
          this.$refs.importFormRef.validateField('file')
        }
      })
    },

    beforeImportUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel'
      const isLt100M = file.size / 1024 / 1024 < 100

      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt100M) {
        this.$message.error('文件大小不能超过100MB!')
        return false
      }
      return true
    },

    async downloadTemplate() {
      if (!this.importForm.templateType) {
        this.$message.warning('请先选择模板类型')
        return
      }

      const templateNames = {
        profit: '利润表数据模板',
        balance: '资产负债表数据模板',
        expense: '三项费用数据模板',
        tax: '税利指标明细表数据模板'
      }

      try {
        const response = await downloadTemplateApi(this.importForm.templateType)

        // 创建下载链接
        const blob = new Blob([response.data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = templateNames[this.importForm.templateType] + '.csv'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success(`正在下载${templateNames[this.importForm.templateType]}...`)
      } catch (error) {
        this.$message.error('下载模板失败：' + error.message)
      }
    },

    confirmImport() {
      this.$refs.importFormRef.validate((valid) => {
        if (valid) {
          this.startDataImport()
        }
      })
    },

    async startDataImport() {
      this.importing = true
      this.importProgress.show = true
      this.importProgress.percentage = 0
      this.importProgress.status = 'active'
      this.importProgress.text = '正在上传文件...'
      this.importProgress.total = 0
      this.importProgress.success = 0
      this.importProgress.failed = 0

      try {
        const formData = new FormData()
        formData.append('file', this.importForm.file)
        formData.append('templateType', this.importForm.templateType)
        formData.append('period', this.importForm.period)

        this.importProgress.percentage = 30
        this.importProgress.text = '正在解析数据...'

        const response = await importExcelData(formData)

        this.importProgress.percentage = 100

        if (response.data.success) {
          this.importProgress.status = 'success'
          this.importProgress.text = '导入完成'
          this.importProgress.total = response.data.data.totalCount || 0
          this.importProgress.success = response.data.data.successCount || 0
          this.importProgress.failed = response.data.data.failCount || 0

          if(response.data.data.notFoundIndicators) {
            this.validationErrors = response.data.data.notFoundIndicators.map((indicator, index) => ({
              id: index,
              type: 'error',
              message: '指标未找到',
              detail: indicator.indexOf(':') !== -1 ? indicator.slice(indicator.indexOf(':') + 1) : indicator
            }));
          }

          this.$message.success('数据导入成功！')

          // 刷新历史记录
          this.loadImportHistory()

          // 3秒后关闭对话框
          setTimeout(() => {
            this.importDialogVisible = false
          }, 3000)
        } else {
          this.importProgress.status = 'exception'
          this.importProgress.text = '导入失败'
          this.$message.error('导入失败：' + (response.data.errors || []).join(', '))
        }
      } catch (error) {
        this.importProgress.status = 'exception'
        this.importProgress.text = '导入异常'
        this.$message.error('导入异常：' + error.message)
      } finally {
        this.importing = false
      }
    },

    // 搜索处理（防抖）
    handleSearchChange() {
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1
        this.loadImportHistory()
      }, 500)
    },

    // 格式化JSON字符串
    formatJsonString(jsonStr) {
      try {
        const obj = JSON.parse(jsonStr)
        return JSON.stringify(obj, null, 2)
      } catch (error) {
        return jsonStr
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 格式化耗时
    formatDuration(milliseconds) {
      if (!milliseconds) return '0ms'
      if (milliseconds < 1000) {
        return milliseconds + 'ms'
      } else if (milliseconds < 60000) {
        return (milliseconds / 1000).toFixed(1) + 's'
      } else {
        return (milliseconds / 60000).toFixed(1) + 'min'
      }
    },

    // 获取模板类型名称
    getTemplateTypeName(templateType) {
      const templateNames = {
        profit: '利润表数据模板',
        balance: '资产负债表数据模板',
        expense: '三项费用数据模板',
        tax: '税利指标明细表数据模板'
      }
      return templateNames[templateType] || templateType
    },

    // 格式化值
    formatValue(value) {
      if (value === null || value === undefined) {
        return 'null'
      }
      if (typeof value === 'object') {
        return JSON.stringify(value, null, 2)
      }
      return String(value)
    }
  },

  mounted() {
    // 组件挂载时加载历史记录
    this.loadImportHistory()
  },

  beforeUnmount() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 20px;
  
  h3 {
    margin: 0 0 5px 0;
    color: #303133;
  }
  
  .page-desc {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.import-panel,
.progress-panel,
.validation-panel,
.history-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  margin-bottom: 20px;
  
  .panel-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    
    h4 {
      margin: 0;
      color: #303133;
      font-size: 16px;
    }
    
    .validation-badge {
      .el-icon-warning {
        color: #e6a23c;
        font-size: 18px;
      }
    }
  }
  
  .import-content,
  .progress-content,
  .validation-content {
    padding: 20px;
  }
}

.template-section {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  
  p {
    margin: 0 0 10px 0;
    font-weight: bold;
    color: #606266;
  }
  
  .el-button {
    margin-right: 15px;
  }
}

.mapping-section {
  margin-top: 20px;
  
  h5 {
    margin: 0 0 15px 0;
    color: #303133;
  }
}

.import-actions {
  margin-top: 20px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.progress-stats {
  margin-top: 10px;
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  color: #606266;
}

.progress-text {
  margin: 10px 0;
  color: #909399;
  text-align: center;
}

.validation-content {
  max-height: 253px;
  overflow-y: auto;
}

.history-panel {
  .panel-actions {
    display: flex;
    align-items: center;
  }
}

.el-table {
  margin-top: 15px;
}

.custom-upload-area {
  width: 100%;
  height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  transition: border-color 0.3s;
}

.custom-upload-area:hover {
  border-color: #409eff;
}

.custom-upload-area.is-dragover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.06);
}

.custom-upload-area .el-icon--upload {
  font-size: 67px;
  color: #c0c4cc;
  margin-bottom: 16px;
  line-height: 50px;
}

.custom-upload-area .el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.custom-upload-area .el-upload__text em {
  color: #409eff;
  font-style: normal;
}

.custom-upload-area .el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 7px;
  text-align: center;
}

/* 导入详情对话框样式 */
.import-detail-content {
  .detail-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;
    }

    h5 {
      margin: 16px 0 8px 0;
      color: #606266;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .stat-card {
    text-align: center;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    background: #fafafa;

    .stat-number {
      font-size: 28px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 8px;
    }

    .stat-label {
      font-size: 14px;
      color: #909399;
    }

    &.success {
      border-color: #67c23a;
      background: rgba(103, 194, 58, 0.1);

      .stat-number {
        color: #67c23a;
      }
    }

    &.error {
      border-color: #f56c6c;
      background: rgba(245, 108, 108, 0.1);

      .stat-number {
        color: #f56c6c;
      }
    }
  }

  .json-viewer {
    background: #f5f7fa;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;

    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      line-height: 1.5;
      color: #303133;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }

  .formatted-result {
    .result-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;

      .el-icon {
        margin-right: 8px;
        font-size: 18px;
      }

      &.success {
        color: #67c23a;
      }

      &.error {
        color: #f56c6c;
      }
    }

    .result-stats {
      display: flex;
      gap: 24px;
      margin-bottom: 20px;
      padding: 16px;
      background: #f5f7fa;
      border-radius: 6px;

      .stat-item {
        .label {
          color: #606266;
          font-weight: 500;
        }

        .value {
          font-weight: 600;
          margin-left: 4px;

          &.success {
            color: #67c23a;
          }

          &.error {
            color: #f56c6c;
          }
        }
      }
    }

    .result-errors,
    .result-not-found,
    .result-other {
      margin-bottom: 20px;
    }

    .other-item {
      margin-bottom: 8px;
      padding: 8px 12px;
      background: #f9f9f9;
      border-radius: 4px;

      .label {
        color: #606266;
        font-weight: 500;
      }

      .value {
        color: #303133;
        margin-left: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
      }
    }

    .no-data {
      text-align: center;
      padding: 40px 0;
    }
  }
}
</style>