/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import java.io.Serial;

/**
 * 指标年度预算 实体类
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@TableName("yjzb_indicator_annual_budget")
@Schema(description = "IndicatorAnnualBudget对象")
@EqualsAndHashCode(callSuper = true)
public class IndicatorAnnualBudgetEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 指标ID（关联指标表）
     */
    @Schema(description = "指标ID（关联指标表）")
    private Long indicatorId;
    /**
     * 指标名称（为历史保留冗余）
     */
    @Schema(description = "指标名称（为历史保留冗余）")
    private String indicatorName;
    /**
     * 年份（YYYY）
     */
    @Schema(description = "年份（YYYY）")
    private Integer year;
    /**
     * 年初预算数
     */
    @Schema(description = "年初预算数")
    private BigDecimal initialBudget;
    /**
     * 年初报备数
     */
    @Schema(description = "年初报备数")
    private BigDecimal initialReported;
    /**
     * 中期调整预算数
     */
    @Schema(description = "中期调整预算数")
    private BigDecimal midyearBudget;
    /**
     * 中期调整报备数
     */
    @Schema(description = "中期调整报备数")
    private BigDecimal midyearReported;
    /**
     * 当前使用数
     */
    @Schema(description = "当前使用数")
    private BigDecimal currentUsed;

}
