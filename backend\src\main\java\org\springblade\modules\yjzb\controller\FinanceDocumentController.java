/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import jakarta.validation.Valid;

import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.FinanceDocumentEntity;
import org.springblade.modules.yjzb.pojo.dto.FinanceDocumentDTO;
import org.springblade.modules.yjzb.pojo.vo.FinanceDocumentVO;
import org.springblade.modules.yjzb.service.IFinanceDocumentService;
import org.springblade.core.boot.ctrl.BladeController;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 知识库文件 控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/finance-document")
@Tag(name = "知识库文件", description = "知识库文件接口")
public class FinanceDocumentController extends BladeController {

    private final IFinanceDocumentService financeDocumentService;

    /**
     * 知识库文件 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入financeDocument")
    public R<FinanceDocumentVO> detail(FinanceDocumentEntity financeDocument) {
        FinanceDocumentEntity detail = financeDocumentService.getOne(Condition.getQueryWrapper(financeDocument));
        return R.data(financeDocumentService.getFinanceDocumentById(detail.getId()));
    }

    /**
     * 知识库文件 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入financeDocument")
    public R<IPage<FinanceDocumentVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> financeDocument, Query query) {
        IPage<FinanceDocumentVO> pages = financeDocumentService.selectFinanceDocumentPage(Condition.getPage(query), Condition.getQueryWrapper(financeDocument, FinanceDocumentVO.class).getEntity());
        return R.data(pages);
    }

    /**
     * 知识库文件 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入financeDocument")
    public R<IPage<FinanceDocumentVO>> page(FinanceDocumentVO financeDocument, Query query) {
        IPage<FinanceDocumentVO> pages = financeDocumentService.selectFinanceDocumentPage(Condition.getPage(query), financeDocument);
        return R.data(pages);
    }

    /**
     * 根据分类查询文档列表
     */
    @GetMapping("/by-category")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "根据分类查询", description = "传入分类ID")
    public R<List<FinanceDocumentVO>> getByCategory(@RequestParam Long categoryId) {
        List<FinanceDocumentVO> documents = financeDocumentService.getDocumentsByCategory(categoryId);
        return R.data(documents);
    }

    /**
     * 根据知识库查询文档列表
     */
    @GetMapping("/by-knowledge")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "根据知识库查询", description = "传入知识库ID")
    public R<List<FinanceDocumentVO>> getByKnowledge(@RequestParam Long knowledgeId) {
        List<FinanceDocumentVO> documents = financeDocumentService.getDocumentsByKnowledge(knowledgeId);
        return R.data(documents);
    }

    /**
     * 搜索文档
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "搜索文档", description = "传入搜索条件")
    public R<List<FinanceDocumentVO>> search(
        @RequestParam(required = false) String keyword,
        @RequestParam(required = false) Long knowledgeId,
        @RequestParam(required = false) Long categoryId,
        @RequestParam(required = false) List<String> tags) {
        List<FinanceDocumentVO> documents = financeDocumentService.searchDocuments(keyword, knowledgeId, categoryId, tags);
        return R.data(documents);
    }

    /**
     * 上传文档
     */
    @PostMapping("/upload")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "上传文档", description = "上传文件和文档信息")
    @PreAuth("hasRole('administrator')")
    public R upload(
        @RequestParam("file") MultipartFile file,
        @RequestParam Long knowledgeId,
        @RequestParam(required = false) Long categoryId,
        @RequestParam(required = false) String fileName,
        @RequestParam(required = false) String fileDescription,
        @RequestParam(required = false) List<String> tags,
        @RequestParam(defaultValue = "true") Boolean syncDify) {
        
        FinanceDocumentDTO documentDTO = new FinanceDocumentDTO();
        documentDTO.setFile(file);
        documentDTO.setKnowledgeId(knowledgeId);
        documentDTO.setCategoryId(categoryId);
        documentDTO.setFileName(Func.isBlank(fileName) ? file.getOriginalFilename() : fileName);
        documentDTO.setFileDescription(fileDescription);
        documentDTO.setTagList(tags);
        documentDTO.setSyncDify(syncDify);
        
        return R.status(financeDocumentService.uploadDocument(documentDTO));
    }

    /**
     * 知识库文件 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "修改", description = "传入financeDocument")
    @PreAuth("hasRole('administrator')")
    public R update(@Valid @RequestBody FinanceDocumentDTO financeDocument) {
        return R.status(financeDocumentService.updateDocument(financeDocument));
    }

    /**
     * 知识库文件 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "逻辑删除", description = "传入ids")
    @PreAuth("hasRole('administrator')")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(financeDocumentService.deleteDocument(ids));
    }

    /**
     * 下载文档 - 使用ResponseEntity方式
     */
    @GetMapping("/download/{id}")
    @ApiOperationSupport(order = 11)
    @Operation(summary = "下载文档", description = "传入文档ID，使用ResponseEntity返回")
    public ResponseEntity<byte[]> download(@PathVariable Long id) {
        try {
            byte[] fileData = financeDocumentService.downloadDocumentData(id);
            if (fileData != null && fileData.length > 0) {
                // 获取文档信息用于设置响应头
                var document = financeDocumentService.getById(id);
                if (document != null) {
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                    headers.setContentLength(fileData.length);
                    headers.setContentDispositionFormData("attachment", encodeFileName(document.getFileName()));
                    headers.setCacheControl("no-cache, no-store, must-revalidate");
                    headers.setPragma("no-cache");
                    headers.setExpires(0);

                    return ResponseEntity.ok()
                            .headers(headers)
                            .body(fileData);
                } else {
                    return ResponseEntity.notFound().build();
                }
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("下载文档失败，ID: {}", id, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 编码文件名以支持中文
     */
    private String encodeFileName(String fileName) {
        try {
            return java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        } catch (Exception e) {
            return fileName;
        }
    }

    /**
     * 增加浏览次数
     */
    @PostMapping("/view/{id}")
    @ApiOperationSupport(order = 11)
    @Operation(summary = "增加浏览次数", description = "传入文档ID")
    public R view(@PathVariable Long id) {
        financeDocumentService.incrementViewCount(id);
        return R.success("浏览次数已增加");
    }

    /**
     * 同步文档到Dify
     */
    @PostMapping("/sync-dify/{id}")
    @ApiOperationSupport(order = 12)
    @Operation(summary = "同步到Dify", description = "传入文档ID")
    @PreAuth("hasRole('administrator')")
    public R syncToDify(@PathVariable Long id) {
        return R.status(financeDocumentService.syncToDify(id));
    }
}
