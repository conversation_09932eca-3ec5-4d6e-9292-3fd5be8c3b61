<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.IndicatorTypesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indicatorTypesResultMap" type="org.springblade.modules.yjzb.pojo.entity.IndicatorTypesEntity">
        <result column="id" property="id"/>
        <result column="type_name" property="typeName"/>
        <result column="type_code" property="typeCode"/>
        <result column="description" property="description"/>
        <result column="data_type" property="dataType"/>
        <result column="unit" property="unit"/>
        <result column="calculation_formula" property="calculationFormula"/>
        <result column="data_source_config" property="dataSourceConfig"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectIndicatorTypesPage" resultMap="indicatorTypesResultMap">
        select * from yjzb_indicator_types where is_deleted = 0
    </select>


    <select id="exportIndicatorTypes" resultType="org.springblade.modules.yjzb.excel.IndicatorTypesExcel">
        SELECT * FROM yjzb_indicator_types ${ew.customSqlSegment}
    </select>

</mapper>
