package org.springblade.modules.yjzb.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ColumnWidth(20)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class IndicatorForecastExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty("主键ID")
    private Long id;
    @ExcelProperty("指标ID")
    private Long indicatorId;
    @ExcelProperty("指标名称")
    private String indicatorName;
    @ExcelProperty("年份")
    private Integer year;
    @ExcelProperty("2月预测值")
    private BigDecimal forecastM02;
    @ExcelProperty("3月预测值")
    private BigDecimal forecastM03;
    @ExcelProperty("4月预测值")
    private BigDecimal forecastM04;
    @ExcelProperty("5月预测值")
    private BigDecimal forecastM05;
    @ExcelProperty("6月预测值")
    private BigDecimal forecastM06;
    @ExcelProperty("7月预测值")
    private BigDecimal forecastM07;
    @ExcelProperty("8月预测值")
    private BigDecimal forecastM08;
    @ExcelProperty("9月预测值")
    private BigDecimal forecastM09;
    @ExcelProperty("10月预测值")
    private BigDecimal forecastM10;
    @ExcelProperty("11月预测值")
    private BigDecimal forecastM11;
    @ExcelProperty("12月预测值")
    private BigDecimal forecastM12;
}
