/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import java.io.Serial;

/**
 * 指标导入历史记录 实体类
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Data
@TableName("yjzb_indicator_import_history")
@Schema(description = "IndicatorImportHistory对象")
@EqualsAndHashCode(callSuper = true)
public class IndicatorImportHistoryEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 导入类型（excel-Excel导入、database-数据库导入、api-API导入）
     */
    @Schema(description = "导入类型（excel-Excel导入、database-数据库导入、api-API导入）")
    private String importType;

    /**
     * 模板类型（profit-利润表、balance-资产负债表、expense-三项费用、tax-税利指标明细表）
     */
    @Schema(description = "模板类型（profit-利润表、balance-资产负债表、expense-三项费用、tax-税利指标明细表）")
    private String templateType;

    /**
     * 导入文件名
     */
    @Schema(description = "导入文件名")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    /**
     * 数据期间（格式：YYYY-MM）
     */
    @Schema(description = "数据期间（格式：YYYY-MM）")
    private String period;

    /**
     * 总记录数
     */
    @Schema(description = "总记录数")
    private Integer totalCount;

    /**
     * 成功导入数
     */
    @Schema(description = "成功导入数")
    private Integer successCount;

    /**
     * 失败记录数
     */
    @Schema(description = "失败记录数")
    private Integer failCount;

    /**
     * 导入状态（SUCCESS-成功、PARTIAL-部分成功、FAILED-失败）
     */
    @Schema(description = "导入状态（SUCCESS-成功、PARTIAL-部分成功、FAILED-失败）")
    private String importStatus;

    /**
     * 导入结果详情（JSON格式，包含错误信息、未找到指标等）
     */
    @Schema(description = "导入结果详情（JSON格式，包含错误信息、未找到指标等）")
    private String importResult;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 导入耗时（毫秒）
     */
    @Schema(description = "导入耗时（毫秒）")
    private Long importDuration;

}
