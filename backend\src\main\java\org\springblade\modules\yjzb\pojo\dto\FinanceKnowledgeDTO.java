/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.modules.yjzb.pojo.entity.FinanceKnowledgeEntity;

/**
 * 财务知识库 数据传输对象
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "财务知识库数据传输对象")
public class FinanceKnowledgeDTO extends FinanceKnowledgeEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 是否同步创建Dify知识库
     */
    @Schema(description = "是否同步创建Dify知识库")
    private Boolean syncDify;

    /**
     * Dify知识库配置
     */
    @Schema(description = "Dify知识库配置")
    private String difyConfig;
}
