import request from '@/axios';

// 费用结构占比（表1）
export const getCategoryStructure = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicator-deep-analysis/category-structure',
    method: 'get',
    params: { indicatorId, period }
  });
}

// 费用趋势对比（表2）
export const getTrendComparison = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicator-deep-analysis/trend-comparison',
    method: 'get',
    params: { indicatorId, period }
  });
}

// 费用波动性分析（表3）
export const getVolatilityAnalysis = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicator-deep-analysis/volatility-analysis',
    method: 'get',
    params: { indicatorId, period }
  });
}

// 费用增长率分析（表4）
export const getGrowthAnalysis = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicator-deep-analysis/growth-analysis',
    method: 'get',
    params: { indicatorId, period }
  });
}

// 费用预算分析（表5）
export const getBudgetAnalysis = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicator-deep-analysis/budget-analysis',
    method: 'get',
    params: { indicatorId, period }
  });
}

// 费用异常检测（表6）
export const getForecastAnalysis = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicator-deep-analysis/forecast-analysis',
    method: 'get',
    params: { indicatorId, period }
  });
}

