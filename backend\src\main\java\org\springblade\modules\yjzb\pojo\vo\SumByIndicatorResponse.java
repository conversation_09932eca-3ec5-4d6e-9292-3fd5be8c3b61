/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "按指标与期间区间合计的返回体")
public class SumByIndicatorResponse {

    @Schema(description = "指标ID", format = "int64")
    private Long indicatorId;

    @Schema(description = "指标名称，若未找到则为null", nullable = true)
    private String indicatorName;

    @Schema(description = "起始期间(YYYY-MM)", pattern = "^\\d{4}-\\d{2}$")
    private String startPeriod;

    @Schema(description = "结束期间(YYYY-MM)", pattern = "^\\d{4}-\\d{2}$")
    private String endPeriod;

    @Schema(description = "合计值")
    private BigDecimal total;
}
