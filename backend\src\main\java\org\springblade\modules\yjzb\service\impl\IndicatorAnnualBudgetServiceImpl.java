/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.service.impl;

import org.springblade.modules.yjzb.pojo.entity.IndicatorAnnualBudgetEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorAnnualBudgetVO;
import org.springblade.modules.yjzb.excel.IndicatorAnnualBudgetExcel;
import org.springblade.modules.yjzb.mapper.IndicatorAnnualBudgetMapper;
import org.springblade.modules.yjzb.service.IIndicatorAnnualBudgetService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.math.BigDecimal;
import java.util.List;

/**
 * 指标年度预算 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Service
public class IndicatorAnnualBudgetServiceImpl
        extends BaseServiceImpl<IndicatorAnnualBudgetMapper, IndicatorAnnualBudgetEntity>
        implements IIndicatorAnnualBudgetService {

    @Override
    public IPage<IndicatorAnnualBudgetVO> selectIndicatorAnnualBudgetPage(IPage<IndicatorAnnualBudgetVO> page,
            IndicatorAnnualBudgetVO indicatorAnnualBudget) {
        return page.setRecords(baseMapper.selectIndicatorAnnualBudgetPage(page, indicatorAnnualBudget));
    }

    @Override
    public List<IndicatorAnnualBudgetExcel> exportIndicatorAnnualBudget(
            Wrapper<IndicatorAnnualBudgetEntity> queryWrapper) {
        List<IndicatorAnnualBudgetExcel> indicatorAnnualBudgetList = baseMapper
                .exportIndicatorAnnualBudget(queryWrapper);
        // indicatorAnnualBudgetList.forEach(indicatorAnnualBudget -> {
        // indicatorAnnualBudget.setTypeName(DictCache.getValue(DictEnum.YES_NO,
        // IndicatorAnnualBudget.getType()));
        // });
        return indicatorAnnualBudgetList;
    }

    @Override
    public IndicatorAnnualBudgetEntity findByIndicatorAndYear(Long indicatorId, Integer year) {
        return baseMapper.findByIndicatorAndYear(indicatorId, year);
    }

    @Override
    public BigDecimal sumBudgetByTypeAndYear(Long indicatorTypeId, Integer year) {
        return baseMapper.sumBudgetByTypeAndYear(indicatorTypeId, year);
    }

}
