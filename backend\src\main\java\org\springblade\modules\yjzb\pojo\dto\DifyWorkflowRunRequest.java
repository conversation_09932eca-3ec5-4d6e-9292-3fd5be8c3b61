/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
public class DifyWorkflowRunRequest {

    @Schema(description = "输入参数，可为空")
    private Map<String, Object> inputs;

    @Schema(description = "响应模式：blocking 或 streaming，默认 blocking")
    private String responseMode;

    @Schema(description = "用户标识，可用于追踪")
    private String user;

    @Schema(description = "最大等待时长(毫秒)，用于阻塞模式；默认300000=5分钟")
    private Long timeoutMs = 300_000L;
}
