<template>
  <!-- 财务税费管控 - 可视化报表页面 -->
  <basic-container>
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <h3>可视化报表</h3>
      <!-- header-actions按钮已移除 -->
    </div>

    <!-- 报表分类和筛选 -->
    <div class="filter-panel">
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="view-controls">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索报表名称"
              prefix-icon="el-icon-search"
              clearable
            />
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 报表列表 -->
    <div class="report-grid">
      <el-row :gutter="20">
        <el-col 
          :span="8" 
          v-for="report in filteredReports" 
          :key="report.id"
          style="margin-bottom: 20px;"
        >
          <el-card class="report-card" @click="viewReport(report)">
            <!-- 报表缩略图 -->
            <div class="report-thumbnail">
              <div class="chart-preview">
                <!-- 用ECharts模拟图替换原有内容 -->
                <div :id="'echarts-thumb-' + report.id" style="width:100%;height:80px;"></div>
              </div>
              <div class="report-actions">
                <el-button size="small" type="link" @click.stop="editReport(report)">
                  <el-icon><edit /></el-icon>
                </el-button>
                <el-button size="small" type="link" @click.stop="copyReport(report)">
                  <el-icon><copy-document /></el-icon>
                </el-button>
                <el-button size="small" type="link" @click.stop="deleteReport(report)">
                  <el-icon><delete /></el-icon>
                </el-button>
              </div>
            </div>
            
            <!-- 报表信息 -->
            <div class="report-info">
              <h4 class="report-title">{{ report.title }}</h4>
              <div class="report-meta">
                <el-tag :type="getReportTypeTag(report.type)" size="small">
                  {{ report.typeName }}
                </el-tag>
                <!-- 状态标签已移除 -->
              </div>
              <p class="report-description">{{ report.description }}</p>
              <!-- 去除report-stats区域 -->
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 报表预览/编辑对话框 -->
    <el-dialog
      v-model="reportDialogVisible"
      :title="currentReport?.title || '报表详情'"
      width="90%"
      top="5vh"
      destroy-on-close
    >
      <div class="report-designer" v-if="currentReport">
        <!-- 工具栏 -->
        <div class="designer-toolbar">
          <div class="toolbar-left">
            <el-button-group>
              <!-- 预览按钮已移除 -->
            </el-button-group>
          </div>
          <div class="toolbar-right">
            <el-button @click="exportReport('image')">
              <el-icon><picture /></el-icon> 导出图片
            </el-button>
            <el-button @click="exportReport('pdf')">
              <el-icon><document /></el-icon> 导出PDF
            </el-button>
            <el-button type="primary" @click="saveReport" v-if="designMode !== 'view'" style="display:none">
              <el-icon><check /></el-icon> 保存
            </el-button>
          </div>
        </div>

        <!-- 设计器内容 -->
        <div class="designer-content">
          <!-- 预览模式 -->
          <div v-if="designMode === 'view'" class="view-mode">
            <!-- 多维度筛选器 -->
            <div class="dimension-filters">
              <el-form inline>
                <el-form-item label="时间范围:">
                  <el-date-picker
                    v-model="reportFilters.dateRange"
                    type="monthrange"
                    size="small"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                    start-placeholder="开始月份"
                    end-placeholder="结束月份"
                  />
                </el-form-item>
                <!-- 只保留月份范围，移除部门和项目等其他筛选项 -->
                <el-form-item>
                  <el-button type="primary" size="small" @click="refreshReportData">
                    <el-icon><refresh /></el-icon> 刷新数据
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 图表展示区域 -->
            <div class="chart-display">
              <el-row :gutter="20">
                <el-col :span="24">
                  <div class="chart-container">
                    <div class="chart-header">
                      <h4>{{ currentReport.title }}</h4>
                    </div>
                    
                    <!-- 只显示一个ECharts柱状图 -->
                    <div class="chart-area">
                      <div id="reportBarChart" style="width: 100%; height: 400px;"></div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 编辑模式 -->
          <div v-if="designMode === 'edit'" class="edit-mode">
            <el-row :gutter="20">
              <!-- 左侧：组件库 -->
              <el-col :span="6">
                <div class="component-library">
                  <h5>图表组件</h5>
                  <div class="component-list">
                    <div 
                      v-for="component in chartComponents" 
                      :key="component.type"
                      class="component-item"
                      @click="addComponent(component)"
                    >
                      <el-icon>
                        <component :is="component.icon" />
                      </el-icon>
                      <span>{{ component.name }}</span>
                    </div>
                  </div>
                </div>
              </el-col>
              
              <!-- 中间：设计画布 -->
              <el-col :span="12">
                <div class="design-canvas">
                  <div class="canvas-header">
                    <h5>设计画布</h5>
                    <div class="canvas-tools">
                      <el-button size="small">
                        <el-icon><upload /></el-icon> 导入模板
                      </el-button>
                      <el-button size="small">
                        <el-icon><download /></el-icon> 保存模板
                      </el-button>
                    </div>
                  </div>
                  <div class="canvas-area">
                    <div class="canvas-placeholder">
                      <el-icon><plus /></el-icon>
                      <p>拖拽左侧组件到此处开始设计</p>
                    </div>
                  </div>
                </div>
              </el-col>
              
              <!-- 右侧：属性配置 -->
              <el-col :span="6">
                <div class="property-panel">
                  <h5>属性配置</h5>
                  <el-form label-width="80px" size="small">
                    <el-form-item label="图表标题">
                      <el-input v-model="chartConfig.title" />
                    </el-form-item>
                    <el-form-item label="图表类型">
                      <el-select v-model="chartConfig.type">
                        <el-option label="柱状图" value="bar" />
                        <el-option label="折线图" value="line" />
                        <el-option label="饼图" value="pie" />
                        <el-option label="散点图" value="scatter" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="数据源">
                      <el-select v-model="chartConfig.dataSource">
                        <el-option label="财务数据" value="finance" />
                        <el-option label="费用数据" value="expense" />
                        <el-option label="税务数据" value="tax" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="颜色主题">
                      <el-select v-model="chartConfig.theme">
                        <el-option label="默认主题" value="default" />
                        <el-option label="蓝色主题" value="blue" />
                        <el-option label="绿色主题" value="green" />
                      </el-select>
                    </el-form-item>
                  </el-form>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 配置模式 -->
          <div v-if="designMode === 'config'" class="config-mode">
            <el-tabs v-model="configTab" type="card">
              <!-- 基本配置 -->
              <el-tab-pane label="基本配置" name="basic">
                <el-form :model="reportConfig" label-width="120px">
                  <el-form-item label="报表名称">
                    <el-input v-model="reportConfig.title" />
                  </el-form-item>
                  <el-form-item label="报表描述">
                    <el-input v-model="reportConfig.description" type="textarea" :rows="3" />
                  </el-form-item>
                  <el-form-item label="报表类型">
                    <el-select v-model="reportConfig.type">
                      <el-option label="财务报表" value="finance" />
                      <el-option label="税务报表" value="tax" />
                      <el-option label="费用报表" value="expense" />
                      <el-option label="指标报表" value="indicator" />
                    </el-select>
                  </el-form-item>
                  <!-- 发布状态已移除 -->
                </el-form>
              </el-tab-pane>
              
              <!-- 数据配置 -->
              <el-tab-pane label="数据配置" name="data">
                <el-form label-width="120px">
                  <el-form-item label="数据源类型">
                    <el-radio-group v-model="dataConfig.sourceType">
                      <el-radio label="database">数据库</el-radio>
                      <el-radio label="api">API接口</el-radio>
                      <el-radio label="file">文件导入</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="数据表/接口">
                    <el-select v-model="dataConfig.source">
                      <el-option label="财务明细表" value="finance_detail" />
                      <el-option label="费用统计表" value="expense_stat" />
                      <el-option label="税务申报表" value="tax_report" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="更新频率">
                    <el-select v-model="dataConfig.refreshRate">
                      <el-option label="实时" value="realtime" />
                      <el-option label="每小时" value="hourly" />
                      <el-option label="每日" value="daily" />
                      <el-option label="手动" value="manual" />
                    </el-select>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
              
              <!-- 权限配置 -->
              <el-tab-pane label="权限配置" name="permission">
                <el-form label-width="120px">
                  <el-form-item label="可见范围">
                    <el-checkbox-group v-model="permissionConfig.visibleTo">
                      <el-checkbox label="all">所有用户</el-checkbox>
                      <el-checkbox label="department">本部门</el-checkbox>
                      <el-checkbox label="role">指定角色</el-checkbox>
                      <el-checkbox label="user">指定用户</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="操作权限">
                    <el-checkbox-group v-model="permissionConfig.actions">
                      <el-checkbox label="view">查看</el-checkbox>
                      <el-checkbox label="edit">编辑</el-checkbox>
                      <el-checkbox label="export">导出</el-checkbox>
                      <el-checkbox label="share">分享</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import { Plus, Refresh, PieChart, Edit, CopyDocument, Delete, View, User, Clock, Setting, Picture, Document, Check, Upload, Download } from '@element-plus/icons-vue'

export default {
  name: 'FinanceVisualReport',
  components: {
    Plus, Refresh, PieChart, Edit, CopyDocument, Delete, View, User, Clock, Setting, Picture, Document, Check, Upload, Download
  },
  data() {
    return {
      searchKeyword: '',
      pageSize: 12,
      total: 0,
      reportDialogVisible: false,
      currentReport: null,
      designMode: 'view', // view, edit, config
      chartViewType: 'chart', // chart, table, both
      configTab: 'basic',
      
      // 筛选表单
      // filterForm: {
      //   reportType: '',
      //   dimension: '',
      //   status: ''
      // },
      
      // 报表筛选器
      reportFilters: {
        dateRange: [],
        department: [],
        project: ''
      },
      
      // 图表配置
      chartConfig: {
        title: '',
        type: 'bar',
        dataSource: '',
        theme: 'default'
      },
      
      // 报表配置
      reportConfig: {
        title: '',
        description: '',
        type: '',
        status: 'draft'
      },
      
      // 数据配置
      dataConfig: {
        sourceType: 'database',
        source: '',
        refreshRate: 'daily'
      },
      
      // 权限配置
      permissionConfig: {
        visibleTo: ['all'],
        actions: ['view']
      },
      
      // 图表组件库
      chartComponents: [
        { type: 'bar', name: '柱状图', icon: 'PieChart' },
        { type: 'line', name: '折线图', icon: 'PieChart' },
        { type: 'pie', name: '饼图', icon: 'PieChart' },
        { type: 'scatter', name: '散点图', icon: 'PieChart' },
        { type: 'radar', name: '雷达图', icon: 'PieChart' },
        { type: 'gauge', name: '仪表盘', icon: 'PieChart' }
      ],
      
      // 报表列表数据
      reportList: [
        {
          id: 1,
          title: '月度财务收支分析',
          description: '展示月度收入、支出及利润趋势分析',
          type: 'finance',
          typeName: '财务报表',
          chartType: 'bar',
          chartTypeName: '柱状图',
          author: '张财务',
          views: 256,
          updateTime: '2024-01-15',
          tableColumns: [
            { prop: 'month', label: '月份', width: 100 },
            { prop: 'income', label: '收入(万元)', width: 120 },
            { prop: 'expense', label: '支出(万元)', width: 120 },
            { prop: 'profit', label: '利润(万元)', width: 120 }
          ],
          tableData: [
            { month: '1月', income: 3256, expense: 2134, profit: 1122 },
            { month: '2月', income: 3458, expense: 2245, profit: 1213 },
            { month: '3月', income: 3789, expense: 2456, profit: 1333 }
          ]
        },
        {
          id: 2,
          title: '费用支出对比',
          description: '各费用支出对比分析',
          type: 'expense',
          typeName: '费用报表',
          chartType: 'pie',
          chartTypeName: '饼图',
          author: '李会计',
          views: 189,
          updateTime: '2024-01-14',
          tableColumns: [
            { prop: 'department', label: '部门', width: 150 },
            { prop: 'amount', label: '费用(万元)', width: 120 },
            { prop: 'ratio', label: '占比(%)', width: 100 }
          ],
          tableData: [
            { department: '技术研发部', amount: 125.6, ratio: 22.3 },
            { department: '市场营销部', amount: 98.2, ratio: 17.4 },
            { department: '生产制造部', amount: 87.5, ratio: 15.5 }
          ]
        },
        {
          id: 3,
          title: '税种缴纳情况统计',
          description: '各税种月度缴纳金额及趋势',
          type: 'tax',
          typeName: '税务报表',
          chartType: 'line',
          chartTypeName: '折线图',
          author: '王税务',
          views: 324,
          updateTime: '2024-01-13',
          tableColumns: [
            { prop: 'taxType', label: '税种', width: 120 },
            { prop: 'amount', label: '金额(万元)', width: 120 },
            { prop: 'rate', label: '税率(%)', width: 100 }
          ],
          tableData: [
            { taxType: '企业所得税', amount: 156.8, rate: 25 },
            { taxType: '增值税', amount: 234.5, rate: 13 },
            { taxType: '个人所得税', amount: 45.2, rate: 10 }
          ]
        },
        {
          id: 4,
          title: '关键财务指标仪表盘',
          description: '实时展示关键财务指标完成情况',
          type: 'indicator',
          typeName: '指标报表',
          chartType: 'gauge',
          chartTypeName: '仪表盘',
          author: '陈总监',
          views: 78,
          updateTime: '2024-01-12',
          tableColumns: [
            { prop: 'indicator', label: '指标名称', width: 150 },
            { prop: 'target', label: '目标值', width: 120 },
            { prop: 'actual', label: '实际值', width: 120 },
            { prop: 'completion', label: '完成率(%)', width: 120 }
          ],
          tableData: [
            { indicator: '营业收入', target: 10000, actual: 9856, completion: 98.6 },
            { indicator: '净利润', target: 1200, actual: 1156, completion: 96.3 },
            { indicator: '成本控制', target: 6500, actual: 6234, completion: 104.3 }
          ]
        },
        {
          id: 5,
          title: '项目盈亏分析',
          description: '各项目收支及盈亏状况分析',
          type: 'custom',
          typeName: '自定义报表',
          chartType: 'bar',
          chartTypeName: '柱状图',
          author: '赵经理',
          views: 145,
          updateTime: '2024-01-11',
          tableColumns: [
            { prop: 'project', label: '项目名称', width: 150 },
            { prop: 'revenue', label: '收入(万元)', width: 120 },
            { prop: 'cost', label: '成本(万元)', width: 120 },
            { prop: 'profit', label: '利润(万元)', width: 120 }
          ],
          tableData: [
            { project: '项目A', revenue: 1256, cost: 856, profit: 400 },
            { project: '项目B', revenue: 2134, cost: 1567, profit: 567 },
            { project: '项目C', revenue: 987, cost: 756, profit: 231 }
          ]
        },
        {
          id: 6,
          title: '预算执行情况追踪',
          description: '各部门预算执行进度及偏差分析',
          type: 'finance',
          typeName: '财务报表',
          chartType: 'radar',
          chartTypeName: '雷达图',
          author: '孙主管',
          views: 67,
          updateTime: '2024-01-10',
          tableColumns: [
            { prop: 'department', label: '部门', width: 150 },
            { prop: 'budget', label: '预算(万元)', width: 120 },
            { prop: 'actual', label: '实际(万元)', width: 120 },
            { prop: 'variance', label: '偏差(万元)', width: 120 }
          ],
          tableData: [
            { department: '技术研发部', budget: 1500, actual: 1456, variance: -44 },
            { department: '市场营销部', budget: 1200, actual: 1267, variance: 67 },
            { department: '生产制造部', budget: 2000, actual: 1987, variance: -13 }
          ]
        }
      ]
    }
  },
  computed: {
    filteredReports() {
      let result = this.reportList
      
      // 类型筛选
      // if (this.filterForm.reportType) {
      //   result = result.filter(item => item.type === this.filterForm.reportType)
      // }
      
      // 状态筛选
      // if (this.filterForm.status) {
      //   result = result.filter(item => item.status === this.filterForm.status)
      // }
      
      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        result = result.filter(item =>
          item.title.toLowerCase().includes(keyword) ||
          item.description.toLowerCase().includes(keyword)
        )
      }
      
      this.total = result.length
      return result
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.renderAllThumbCharts();
    });
  },
  updated() {
    this.$nextTick(() => {
      this.renderAllThumbCharts();
      this.renderReportBarChart();
    });
  },
  watch: {
    reportDialogVisible(val) {
      if (val) {
        this.renderReportBarChart();
      }
    }
  },
  methods: {
    // 创建报表
    createReport() {
      this.currentReport = {
        id: null,
        title: '新建报表',
        description: '',
        type: 'finance',
        status: 'draft',
        chartType: 'bar'
      }
      this.designMode = 'edit'
      this.reportDialogVisible = true
    },
    
    // 查看报表
    viewReport(report) {
      this.currentReport = report
      this.designMode = 'view'
      this.reportDialogVisible = true
      // 增加浏览次数
      report.views++
    },
    
    // 编辑报表
    editReport(report) {
      this.currentReport = { ...report }
      this.designMode = 'edit'
      this.reportDialogVisible = true
    },
    
    // 复制报表
    copyReport(report) {
      const newReport = {
        ...report,
        id: Date.now(),
        title: `${report.title} - 副本`,
        status: 'draft',
        statusName: '草稿',
        views: 0
      }
      this.reportList.unshift(newReport)
      this.$message.success('复制成功')
    },
    
    // 删除报表
    deleteReport(report) {
      this.$confirm('确认删除此报表？删除后无法恢复。', '确认删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.reportList.findIndex(item => item.id === report.id)
        if (index > -1) {
          this.reportList.splice(index, 1)
          this.$message.success('删除成功')
        }
      })
    },
    
    // 保存报表
    saveReport() {
      this.$message.success('报表保存成功')
      this.reportDialogVisible = false
    },
    
    // 导出报表
    exportReport(format) {
      this.$message.success(`正在导出${format.toUpperCase()}格式...`)
    },
    
    // 刷新报表数据
    refreshReportData() {
      this.$message.success('数据刷新成功')
    },
    
    // 添加组件
    addComponent(component) {
      this.$message.info(`添加${component.name}组件`)
    },
    
    // 筛选和刷新
    handleFilter() {
      // 仅保留名称搜索，无需其他筛选
    },
    
    resetFilter() {
      // 仅保留名称搜索，无需其他筛选
    },
    
    refreshReports() {
      this.$message.success('刷新成功')
    },
    
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
    },
    
    handleCurrentChange(val) {
      console.log('当前页:', val)
    },
    
    // 工具方法
    getReportTypeTag(type) {
      const typeMap = {
        finance: 'primary',
        tax: 'success',
        expense: 'warning',
        indicator: 'info',
        custom: 'danger'
      }
      return typeMap[type] || 'info'
    },
    
    // getStatusTag方法可删除

    renderAllThumbCharts() {
      if (!window.echarts) return;
      this.filteredReports.forEach(report => {
        const dom = document.getElementById('echarts-thumb-' + report.id);
        if (dom) {
          let option = {};
          switch (report.chartType) {
            case 'bar':
              option = {
                xAxis: { show: false, type: 'category', data: ['A', 'B', 'C'] },
                yAxis: { show: false },
                series: [{ type: 'bar', data: [5, 8, 3] }],
                grid: { left: 0, right: 0, top: 0, bottom: 0 }
              };
              break;
            case 'pie':
              option = {
                series: [{ type: 'pie', radius: ['50%', '80%'], data: [ { value: 40 }, { value: 30 }, { value: 30 } ], label: { show: false } }],
                grid: { left: 0, right: 0, top: 0, bottom: 0 }
              };
              break;
            case 'line':
              option = {
                xAxis: { show: false, type: 'category', data: ['A', 'B', 'C'] },
                yAxis: { show: false },
                series: [{ type: 'line', data: [2, 6, 4] }],
                grid: { left: 0, right: 0, top: 0, bottom: 0 }
              };
              break;
            case 'radar':
              option = {
                radar: { indicator: [ { name: 'A' }, { name: 'B' }, { name: 'C' } ], splitLine: { show: false }, splitArea: { show: false }, axisLine: { show: false } },
                series: [{ type: 'radar', data: [ { value: [3, 5, 2] } ] }]
              };
              break;
            case 'gauge':
              option = {
                series: [{ type: 'gauge', progress: { show: true }, detail: { show: false }, data: [{ value: 70 }] }]
              };
              break;
            default:
              option = {};
          }
          const chart = window.echarts.init(dom);
          chart.setOption(option);
        }
      });
    },

    renderReportBarChart() {
      if (!window.echarts || !this.reportDialogVisible) return;
      this.$nextTick(() => {
        const dom = document.getElementById('reportBarChart');
        if (dom) {
          // 销毁旧实例，防止重复渲染
          if (dom.__chart__) {
            dom.__chart__.dispose();
          }
          const chart = window.echarts.init(dom);
          dom.__chart__ = chart;
          const option = {
            title: { show: false },
            tooltip: {},
            xAxis: { type: 'category', data: ['一月', '二月', '三月', '四月', '五月', '六月'] },
            yAxis: { type: 'value' },
            series: [{ name: '收入', type: 'bar', data: [320, 452, 601, 734, 890, 1020], barWidth: '40%' }],
            grid: { left: 40, right: 20, top: 20, bottom: 40 }
          };
          chart.setOption(option);
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.filter-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  margin-bottom: 20px;
  
  .view-controls {
    text-align: right;
  }
}

.report-grid {
  margin-bottom: 20px;
  
  .report-card {
    cursor: pointer;
    transition: all 0.3s;
    height: 320px;
    
    &:hover {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
    
    .report-thumbnail {
      position: relative;
      height: 150px;
      background: #f5f7fa;
      border-radius: 4px;
      margin-bottom: 15px;
      
      .chart-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #909399;
        
        .el-icon {
          font-size: 48px;
          margin-bottom: 8px;
        }
        
        .chart-type {
          font-size: 14px;
        }
      }
      
      .report-actions {
        position: absolute;
        top: 10px;
        right: 10px;
        display: flex;
        gap: 5px;
        opacity: 0;
        transition: opacity 0.3s;
      }
      
      &:hover .report-actions {
        opacity: 1;
      }
    }
    
    .report-info {
      .report-title {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 16px;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .report-meta {
        display: flex;
        gap: 8px;
        margin-bottom: 10px;
      }
      
      .report-description {
        margin: 0 0 15px 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.4;
        height: 40px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .report-stats {
        display: flex;
        justify-content: space-between;
        
        .stat-item {
          color: #909399;
          font-size: 12px;
          display: flex;
          align-items: center;
          
          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }
  }
}

.pagination-wrapper {
  text-align: center;
}

.report-designer {
  .designer-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 20px;
    
    .toolbar-left,
    .toolbar-right {
      display: flex;
      gap: 10px;
    }
  }
  
  .designer-content {
    min-height: 600px;
    
    .view-mode {
      .dimension-filters {
        background: #f5f7fa;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
      }
      
      .chart-display {
        .chart-container {
          background: #fff;
          border: 1px solid #ebeef5;
          border-radius: 6px;
          padding: 20px;
          
          .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            
            h4 {
              margin: 0;
              color: #303133;
            }
          }
          
          .chart-area,
          .table-area {
            margin-top: 20px;
          }
        }
      }
    }
    
    .edit-mode {
      .component-library {
        background: #fff;
        border: 1px solid #ebeef5;
        border-radius: 6px;
        padding: 15px;
        height: 500px;
        
        h5 {
          margin: 0 0 15px 0;
          color: #303133;
        }
        
        .component-list {
          .component-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
            
            &:hover {
              background: #f5f7fa;
              border-color: #409eff;
            }
            
            .el-icon {
              margin-right: 10px;
              color: #409eff;
            }
          }
        }
      }
      
      .design-canvas {
        background: #fff;
        border: 1px solid #ebeef5;
        border-radius: 6px;
        padding: 15px;
        height: 500px;
        
        .canvas-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
          
          h5 {
            margin: 0;
            color: #303133;
          }
          
          .canvas-tools {
            display: flex;
            gap: 10px;
          }
        }
        
        .canvas-area {
          height: 400px;
          border: 2px dashed #ddd;
          border-radius: 6px;
          position: relative;
          
          .canvas-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #909399;
            
            .el-icon {
              font-size: 48px;
              margin-bottom: 10px;
            }
            
            p {
              margin: 0;
              font-size: 14px;
            }
          }
        }
      }
      
      .property-panel {
        background: #fff;
        border: 1px solid #ebeef5;
        border-radius: 6px;
        padding: 15px;
        height: 500px;
        
        h5 {
          margin: 0 0 15px 0;
          color: #303133;
        }
      }
    }
    
    .config-mode {
      background: #fff;
      border: 1px solid #ebeef5;
      border-radius: 6px;
      padding: 20px;
    }
  }
}
</style>