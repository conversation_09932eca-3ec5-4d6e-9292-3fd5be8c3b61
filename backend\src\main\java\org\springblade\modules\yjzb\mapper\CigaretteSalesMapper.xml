<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.CigaretteSalesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="cigaretteSalesResultMap" type="org.springblade.modules.yjzb.pojo.entity.CigaretteSalesEntity">
        <id column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="period" property="period"/>
        <result column="sales" property="sales"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 自定义分页 -->
    <select id="selectCigaretteSalesPage" resultMap="cigaretteSalesResultMap">
        select * from yjzb_cigarette_sales where is_deleted = 0
        <if test="cigaretteSales.year != null">
            and year = #{cigaretteSales.year}
        </if>
        <if test="cigaretteSales.month != null">
            and month = #{cigaretteSales.month}
        </if>
    </select>

</mapper>