#数据源配置
spring:
  ai: ##配置OPEN AI API。这里可以换ollama
    openai:
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      api-key: sk-1a05721b70aa400a9763b48be0447da0
      chat:
        options:
          model: qwen-plus-latest
      mcp:
        client:
          enabled: false




  data:
    redis:
      ##redis 单机环境配置
      host: 127.0.0.1
      port: 6379
      password: 
      database: 10
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    # MySql
#    url: *********************************************************************************************************************************************************************************************************************************************
#    username: root
#    password: root
    # PostgreSQL
    url: ***************************************************************
    username: gzyc
    password: gzyc1234
    # Oracle
    #url: *************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # SqlServer
    #url: ********************************************************
    #username: bladex_boot
    #password: bladex_boot
    # DaMeng
    #url: jdbc:dm://127.0.0.1:5236/BLADEX_BOOT?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # YashanDB
    #url: ***************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##redis服务地址
    address: redis://127.0.0.1:6379
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html

#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: false
  #oss服务地址
  endpoint: http://127.0.0.1:9000
  #minio转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: https://tcinspect.foshantc.com/webfile
  #访问key
  access-key: BJEA6hXrehmRYIrGvxKk
  #密钥key
  secret-key: KiQ1NPjX5bbgQVYCGy2X19SVIErQLiQV7OKujscT
  #存储桶
  bucket-name: yjyc


# Dify配置
dify:
  api:
    base-url: http://localhost
    key: dataset-tZtYb9shiz9qzqXEawJzgTVl
    timeout: 30000
    agentkey:
      analysisagent: app-FdeOw28E2ZjVR4e0HhC3ziTe
      totalanalysisagent: app-hxnjbQRuY3npPCawk9ksGQ30
      finance-analysis-key: app-xa7dRzdO8FoxCq7UZxZgA4Yd
      expenseanalysisagent: app-h67tQfyKFLQvvPDgF5dq55BC

  # 支持的文件类型
  supported-file-types:
    - pdf
    - doc
    - docx
    - xls
    - xlsx
    - txt
    - md
    - csv

  # 文件大小限制（字节）
  max-file-size: 52428800  # 50MB

logging:
  config: classpath:log/logback-dev.xml