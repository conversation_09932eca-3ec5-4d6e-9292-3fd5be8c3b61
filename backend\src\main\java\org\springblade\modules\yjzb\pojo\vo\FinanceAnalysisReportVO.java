/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisReportEntity;

/**
 * 财务分析报告列表视图对象
 *
 * <AUTHOR> Assistant
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "财务分析报告列表视图")
public class FinanceAnalysisReportVO extends FinanceAnalysisReportEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 类型名称
     */
    @Schema(description = "类型名称")
    private String typeName;

    /**
     * 文件大小格式化显示
     */
    @Schema(description = "文件大小格式化显示")
    private String fileSizeFormatted;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createUserName;

    /**
     * 生成耗时（秒）
     */
    @Schema(description = "生成耗时（秒）")
    private Long generateDuration;
}
