import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/yjyc/indicatorAnnualBudget/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/yjyc/indicatorAnnualBudget/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/yjyc/indicatorAnnualBudget/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/yjyc/indicatorAnnualBudget/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/yjyc/indicatorAnnualBudget/submit',
    method: 'post',
    data: row
  })
}

