/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 知识库文件Excel实体类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class FinanceDocumentExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("知识库名称")
    private String knowledgeName;

    @ExcelProperty("分类名称")
    private String categoryName;

    @ExcelProperty("文件名称")
    private String fileName;

    @ExcelProperty("原始文件名")
    private String fileOriginalName;

    @ExcelProperty("文件大小")
    private String fileSizeFormat;

    @ExcelProperty("文件类型")
    private String fileType;

    @ExcelProperty("文件说明")
    private String fileDescription;

    @ExcelProperty("浏览次数")
    private Integer viewCount;

    @ExcelProperty("下载次数")
    private Integer downloadCount;

    @ExcelProperty("上传状态")
    private String uploadStatusDesc;

    @ExcelProperty("同步状态")
    private String syncStatusDesc;

    @ExcelProperty("创建人")
    private String createUserName;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("备注")
    private String remark;
}
