export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键ID",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "指标类型ID",
      prop: "indicatorId",
      type: "input",
      search: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "指标类型名称",
      prop: "indicatorName",
      type: "input",
      search: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
    {
      label: "数据期间",
      prop: "period",
      type: "input",
      search: true,
    },
    {
      label: "凭证号",
      prop: "voucherNo",
      type: "input",
      search: true,
    },
    {
      label: "数据摘要",
      prop: "dataSummary",
      type: "textarea",
      search: true,
    },
    {
      label: "金额",
      prop: "amount",
      type: "number",
      search: false,
    },
    {
      label: "分类",
      prop: "category",
      type: "input",
      search: true,
    },
    {
      label: "创建用户ID",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建用户名称",
      prop: "createUserName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
    {
      label: "创建部门ID",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门名称",
      prop: "createDeptName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "datetime",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
    {
      label: "更新用户ID",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新用户名称",
      prop: "updateUserName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "datetime",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
    {
      label: "状态",
      prop: "statusName",
      type: "select",
      dicData: [
        {
          label: "正常",
          value: "正常"
        },
        {
          label: "禁用",
          value: "禁用"
        }
      ],
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
    {
      label: "数据状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "删除标记",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
