<template>
  <!-- MCP管理页面 - 大模型工具调用协议管理 -->
  <basic-container>
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background: #409EFF;">
              <i class="el-icon-s-platform"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">4</div>
              <div class="stat-label">MCP服务器总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background: #67C23A;">
              <i class="el-icon-connection"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">3</div>
              <div class="stat-label">已连接服务器</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background: #E6A23C;">
              <i class="el-icon-set-up"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">0</div>
              <div class="stat-label">异常数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background: #F56C6C;">
              <i class="el-icon-warning-outline"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">1</div>
              <div class="stat-label">断开连接</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- MCP服务器列表 -->
    <el-card>
      <div slot="header" class="card-header">
        <span>MCP服务器管理</span>
        <div class="header-actions">
          <el-button size="small" type="primary" @click="showAddServerDialog">
            <i class="el-icon-plus"></i> 添加服务器
          </el-button>
          <el-button size="small" @click="refreshServers">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>
      <el-table :data="mcpServers" stripe style="width: 100%">
        <el-table-column prop="name" label="服务器名称" width="180" />
        <el-table-column prop="type" label="类型" width="120">
          <template slot-scope="scope">
            <el-tag size="mini">{{ getTypeText(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="endpoint" label="连接地址" min-width="220" />
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="mini">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="connectServer(scope.row)" v-if="scope.row.status === 'disconnected'">
              连接
            </el-button>
            <el-button size="mini" type="text" @click="disconnectServer(scope.row)" v-if="scope.row.status === 'connected'">
              断开
            </el-button>
            <el-button size="mini" type="text" @click="editServer(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" style="color: #f56c6c;" @click="deleteServer(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑服务器对话框 -->
    <el-dialog
      :title="serverDialogTitle"
      :visible.sync="serverDialogVisible"
      width="600px"
      @close="resetServerForm"
    >
      <el-form :model="serverForm" :rules="serverRules" ref="serverForm" label-width="120px">
        <el-form-item label="服务器名称" prop="name">
          <el-input v-model="serverForm.name" placeholder="请输入服务器名称" />
        </el-form-item>
        <el-form-item label="服务器类型" prop="type">
          <el-select v-model="serverForm.type" placeholder="请选择服务器类型" style="width: 100%;">
            <el-option label="文件系统" value="filesystem" />
            <el-option label="数据库" value="database" />
            <el-option label="API服务" value="api" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="连接地址" prop="endpoint">
          <el-input v-model="serverForm.endpoint" placeholder="请输入连接地址，如：http://localhost:3000" />
        </el-form-item>
        <el-form-item label="认证方式" prop="authType">
          <el-select v-model="serverForm.authType" placeholder="请选择认证方式" style="width: 100%;">
            <el-option label="无认证" value="none" />
            <el-option label="API密钥" value="apikey" />
            <el-option label="Token认证" value="token" />
            <el-option label="基础认证" value="basic" />
          </el-select>
        </el-form-item>
        <el-form-item label="认证信息" prop="authConfig" v-if="serverForm.authType !== 'none'">
          <el-input v-model="serverForm.authConfig" type="textarea" :rows="3" placeholder="请输入认证配置信息" />
        </el-form-item>
        <el-form-item label="超时设置" prop="timeout">
          <el-input-number v-model="serverForm.timeout" :min="1000" :max="30000" :step="1000" style="width: 100%;" />
          <div style="font-size: 12px; color: #999; margin-top: 5px;">单位：毫秒，建议5000-10000ms</div>
        </el-form-item>
        <el-form-item label="自动重连" prop="autoReconnect">
          <el-switch v-model="serverForm.autoReconnect" />
        </el-form-item>
        <el-form-item label="描述信息" prop="description">
          <el-input v-model="serverForm.description" type="textarea" :rows="2" placeholder="请输入服务器描述" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="serverDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveServer" :loading="saving">保存</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
export default {
  name: 'McpMgmt',
  data() {
    return {
      // 统计数据
      stats: {
        totalServers: 8,
        connectedServers: 6,
        totalTools: 24,
        errorCount: 2
      },
      
      // 搜索和筛选
      serverSearch: '',
      statusFilter: '',
      typeFilter: '',
      toolSearch: '',
      
      // 对话框状态
      serverDialogVisible: false,
      toolDetailVisible: false,
      saving: false,
      selectedTool: null,
      
      // 表单数据
      serverForm: {
        name: '',
        type: '',
        endpoint: '',
        authType: 'none',
        authConfig: '',
        timeout: 5000,
        autoReconnect: true,
        description: ''
      },
      
      // 表单验证规则
      serverRules: {
        name: [
          { required: true, message: '请输入服务器名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择服务器类型', trigger: 'change' }
        ],
        endpoint: [
          { required: true, message: '请输入连接地址', trigger: 'blur' }
        ],
        authType: [
          { required: true, message: '请选择认证方式', trigger: 'change' }
        ]
      },
      
      // Mock MCP服务器数据
      mcpServers: [
        { id: 1, name: 'FileSystem-Server', type: 'filesystem', endpoint: 'http://localhost:3001', status: 'connected' },
        { id: 2, name: 'Database-Server', type: 'database', endpoint: 'http://localhost:3002', status: 'connected' },
        { id: 3, name: 'API-Gateway', type: 'api', endpoint: 'http://localhost:3003', status: 'disconnected' },
        { id: 4, name: 'Analytics-Server', type: 'custom', endpoint: 'http://localhost:3004', status: 'connected' }
      ],
      
      // Mock工具数据
      allTools: [
        {
          id: 1,
          name: 'read_file',
          description: '读取文件内容',
          serverName: 'FileSystem-Server',
          usageCount: 45,
          avgExecutionTime: 120,
          successRate: 98.5,
          lastUsed: '2024-01-15 14:30',
          parameters: [
            { name: 'file_path', type: 'string', required: true, description: '文件路径' },
            { name: 'encoding', type: 'string', required: false, description: '文件编码' }
          ]
        },
        {
          id: 2,
          name: 'write_file',
          description: '写入文件内容',
          serverName: 'FileSystem-Server',
          usageCount: 32,
          avgExecutionTime: 150,
          successRate: 97.2,
          lastUsed: '2024-01-15 14:25',
          parameters: [
            { name: 'file_path', type: 'string', required: true, description: '文件路径' },
            { name: 'content', type: 'string', required: true, description: '文件内容' }
          ]
        },
        {
          id: 3,
          name: 'execute_query',
          description: '执行数据库查询',
          serverName: 'Database-Server',
          usageCount: 28,
          avgExecutionTime: 200,
          successRate: 95.8,
          lastUsed: '2024-01-15 14:20',
          parameters: [
            { name: 'sql', type: 'string', required: true, description: 'SQL查询语句' },
            { name: 'parameters', type: 'array', required: false, description: '查询参数' }
          ]
        },
        {
          id: 4,
          name: 'http_request',
          description: '发送HTTP请求',
          serverName: 'API-Gateway',
          usageCount: 15,
          avgExecutionTime: 300,
          successRate: 92.1,
          lastUsed: '2024-01-15 12:15',
          parameters: [
            { name: 'url', type: 'string', required: true, description: '请求URL' },
            { name: 'method', type: 'string', required: false, description: 'HTTP方法' },
            { name: 'headers', type: 'object', required: false, description: '请求头' }
          ]
        }
      ]
    };
  },
  
  computed: {
    serverDialogTitle() {
      return this.serverForm.id ? '编辑MCP服务器' : '添加MCP服务器';
    },
    
    filteredServers() {
      let servers = this.mcpServers;
      
      // 按名称搜索
      if (this.serverSearch) {
        servers = servers.filter(server =>
          server.name.toLowerCase().includes(this.serverSearch.toLowerCase())
        );
      }
      
      // 按状态筛选
      if (this.statusFilter) {
        servers = servers.filter(server => server.status === this.statusFilter);
      }
      
      // 按类型筛选
      if (this.typeFilter) {
        servers = servers.filter(server => server.type === this.typeFilter);
      }
      
      return servers;
    },
    
    connectedServers() {
      return this.mcpServers.filter(server => server.status === 'connected');
    },
    
    filteredTools() {
      let tools = this.allTools;
      
      if (this.toolSearch) {
        tools = tools.filter(tool =>
          tool.name.toLowerCase().includes(this.toolSearch.toLowerCase()) ||
          tool.description.toLowerCase().includes(this.toolSearch.toLowerCase())
        );
      }
      
      return tools;
    }
  },
  
  methods: {
    // 状态相关方法
    getStatusTagType(status) {
      const types = {
        'connected': 'success',
        'disconnected': 'info',
        'connecting': 'warning',
        'error': 'danger'
      };
      return types[status] || 'info';
    },
    
    getStatusText(status) {
      const texts = {
        'connected': '已连接',
        'disconnected': '未连接',
        'connecting': '连接中',
        'error': '连接失败'
      };
      return texts[status] || '未知';
    },
    
    getTypeText(type) {
      const texts = {
        'filesystem': '文件系统',
        'database': '数据库',
        'api': 'API服务',
        'custom': '自定义'
      };
      return texts[type] || '未知';
    },
    
    getLatencyColor(latency) {
      if (latency < 50) return '#67C23A';
      if (latency < 100) return '#E6A23C';
      return '#F56C6C';
    },
    
    // 服务器操作方法
    showAddServerDialog() {
      this.serverDialogVisible = true;
    },
    
    editServer(server) {
      this.serverForm = { ...server };
      this.serverDialogVisible = true;
    },
    
    connectServer(server) {
      server.status = 'connecting';
      // 模拟连接过程
      setTimeout(() => {
        server.status = 'connected';
        server.lastConnected = this.formatCurrentTime();
        server.latency = Math.floor(Math.random() * 100) + 20;
        this.$message.success(`服务器 ${server.name} 连接成功`);
      }, 2000);
    },
    
    disconnectServer(server) {
      server.status = 'disconnected';
      server.latency = 0;
      server.callCount = 0;
      this.$message.success(`服务器 ${server.name} 已断开连接`);
    },
    
    deleteServer(server) {
      this.$confirm(`确认删除服务器 "${server.name}" 吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.mcpServers.findIndex(s => s.id === server.id);
        if (index > -1) {
          this.mcpServers.splice(index, 1);
          this.$message.success('删除成功');
        }
      });
    },
    
    saveServer() {
      this.$refs.serverForm.validate((valid) => {
        if (valid) {
          this.saving = true;
          
          // 模拟保存过程
          setTimeout(() => {
            if (this.serverForm.id) {
              // 编辑模式
              const index = this.mcpServers.findIndex(s => s.id === this.serverForm.id);
              if (index > -1) {
                this.mcpServers.splice(index, 1, { ...this.serverForm });
              }
              this.$message.success('服务器配置更新成功');
            } else {
              // 新增模式
              const newServer = {
                ...this.serverForm,
                id: Date.now(),
                status: 'disconnected',
                toolCount: 0,
                lastConnected: '-',
                latency: 0,
                callCount: 0
              };
              this.mcpServers.push(newServer);
              this.$message.success('服务器添加成功');
            }
            
            this.saving = false;
            this.serverDialogVisible = false;
            this.resetServerForm();
          }, 1500);
        }
      });
    },
    
    resetServerForm() {
      this.serverForm = {
        name: '',
        type: '',
        endpoint: '',
        authType: 'none',
        authConfig: '',
        timeout: 5000,
        autoReconnect: true,
        description: ''
      };
      if (this.$refs.serverForm) {
        this.$refs.serverForm.resetFields();
      }
    },
    
    refreshServers() {
      this.$message.success('服务器列表已刷新');
    },
    
    // 工具相关方法
    viewServerTools(server) {
      this.$message.info(`查看服务器 ${server.name} 的工具列表`);
    },
    
    testTool(tool) {
      this.$message.success(`工具 ${tool.name} 测试成功`);
    },
    
    viewToolDetail(tool) {
      this.selectedTool = tool;
      this.toolDetailVisible = true;
    },
    
    refreshTools() {
      this.$message.success('工具列表已刷新');
    },
    
    // 工具方法
    formatCurrentTime() {
      const now = new Date();
      return `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    }
  }
};
</script>

<style scoped>
/* 统计卡片样式 */
.stat-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-right: 15px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

/* 连接监控样式 */
.connection-monitor {
  max-height: 200px;
  overflow-y: auto;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.connection-item:last-child {
  border-bottom: none;
}

.connection-info {
  flex: 1;
}

.server-name {
  font-weight: 500;
  margin-bottom: 5px;
}

.connection-stats {
  display: flex;
  gap: 8px;
}

.connection-chart {
  width: 60px;
  text-align: center;
}

.mini-chart {
  width: 50px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 工具列表样式 */
.tools-list {
  padding: 5px 0;
}

.tool-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.tool-item:last-child {
  border-bottom: none;
}

.tool-info {
  flex: 1;
  margin-right: 10px;
}

.tool-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.tool-description {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
}

.tool-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
}

.tool-usage {
  color: #909399;
}

.tool-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
</style> 