/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity;
import org.springblade.modules.yjzb.pojo.dto.FinanceCategoryDTO;
import org.springblade.modules.yjzb.pojo.vo.FinanceCategoryVO;
import java.util.List;

/**
 * 知识分类 服务类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
public interface IFinanceCategoryService extends IService<FinanceCategoryEntity> {

    /**
     * 自定义分页查询
     *
     * @param page 分页对象
     * @param financeCategory 查询条件
     * @return 分页结果
     */
    IPage<FinanceCategoryVO> selectFinanceCategoryPage(IPage<FinanceCategoryVO> page, FinanceCategoryVO financeCategory);

    /**
     * 根据ID查询详情
     *
     * @param id 主键ID
     * @return 详情信息
     */
    FinanceCategoryVO getFinanceCategoryById(Long id);

    /**
     * 查询树形结构
     *
     * @param knowledgeId 知识库ID
     * @return 树形结构列表
     */
    List<FinanceCategoryVO> getCategoryTree(Long knowledgeId);

    /**
     * 查询子分类列表
     *
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<FinanceCategoryVO> getChildrenByParentId(Long parentId);

    /**
     * 新增分类
     *
     * @param financeCategoryDTO 分类信息
     * @return 是否成功
     */
    boolean saveFinanceCategory(FinanceCategoryDTO financeCategoryDTO);

    /**
     * 更新分类
     *
     * @param financeCategoryDTO 分类信息
     * @return 是否成功
     */
    boolean updateFinanceCategory(FinanceCategoryDTO financeCategoryDTO);

    /**
     * 删除分类
     *
     * @param ids 主键ID列表
     * @return 是否成功
     */
    boolean deleteFinanceCategory(String ids);

    /**
     * 构建完整路径
     *
     * @param parentId 父分类ID
     * @param categoryName 分类名称
     * @return 完整路径
     */
    String buildFullPath(Long parentId, String categoryName);

    /**
     * 统计分类下的文档数量
     *
     * @param categoryId 分类ID
     * @return 文档数量
     */
    Integer countDocumentsByCategory(Long categoryId);
}
