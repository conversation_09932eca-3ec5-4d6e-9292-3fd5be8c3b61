package org.springblade.modules.yjzb.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;

/**
 * 财务分析视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "财务分析视图")
public class FinanceAnalysisVO extends FinanceAnalysisEntity {

    private static final long serialVersionUID = 1L;

}