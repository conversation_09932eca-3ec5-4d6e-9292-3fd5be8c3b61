/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.FinanceKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.dto.FinanceKnowledgeDTO;
import org.springblade.modules.yjzb.pojo.vo.FinanceKnowledgeVO;
import org.springblade.modules.yjzb.service.IFinanceKnowledgeService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.Map;

/**
 * 财务知识库 控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/finance-knowledge")
@Tag(name = "财务知识库", description = "财务知识库接口")
public class FinanceKnowledgeController extends BladeController {

    private final IFinanceKnowledgeService financeKnowledgeService;

    /**
     * 财务知识库 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入financeKnowledge")
    public R<FinanceKnowledgeVO> detail(FinanceKnowledgeEntity financeKnowledge) {
        FinanceKnowledgeEntity detail = financeKnowledgeService.getOne(Condition.getQueryWrapper(financeKnowledge));
        return R.data(financeKnowledgeService.getFinanceKnowledgeById(detail.getId()));
    }

    /**
     * 财务知识库 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入financeKnowledge")
    public R<IPage<FinanceKnowledgeVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> financeKnowledge, Query query) {
        IPage<FinanceKnowledgeVO> pages = financeKnowledgeService.selectFinanceKnowledgePage(Condition.getPage(query), Condition.getQueryWrapper(financeKnowledge, FinanceKnowledgeVO.class).getEntity());
        return R.data(pages);
    }

    /**
     * 财务知识库 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入financeKnowledge")
    public R<IPage<FinanceKnowledgeVO>> page(FinanceKnowledgeVO financeKnowledge, Query query) {
        IPage<FinanceKnowledgeVO> pages = financeKnowledgeService.selectFinanceKnowledgePage(Condition.getPage(query), financeKnowledge);
        return R.data(pages);
    }

    /**
     * 财务知识库 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入financeKnowledge")
    @PreAuth("hasRole('administrator')")
    public R save(@Valid @RequestBody FinanceKnowledgeDTO financeKnowledge) {
        return R.status(financeKnowledgeService.saveFinanceKnowledge(financeKnowledge));
    }

    /**
     * 财务知识库 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入financeKnowledge")
    @PreAuth("hasRole('administrator')")
    public R update(@Valid @RequestBody FinanceKnowledgeDTO financeKnowledge) {
        return R.status(financeKnowledgeService.updateFinanceKnowledge(financeKnowledge));
    }

    /**
     * 财务知识库 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入financeKnowledge")
    @PreAuth("hasRole('administrator')")
    public R submit(@Valid @RequestBody FinanceKnowledgeDTO financeKnowledge) {
        return R.status(financeKnowledgeService.saveOrUpdate(financeKnowledge));
    }

    /**
     * 财务知识库 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    @PreAuth("hasRole('administrator')")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(financeKnowledgeService.deleteFinanceKnowledge(ids));
    }

    /**
     * 同步知识库到Dify
     */
    @PostMapping("/sync-dify/{id}")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "同步到Dify", description = "传入知识库ID")
    @PreAuth("hasRole('administrator')")
    public R syncToDify(@PathVariable Long id) {
        return R.status(financeKnowledgeService.syncToDify(id));
    }
}
