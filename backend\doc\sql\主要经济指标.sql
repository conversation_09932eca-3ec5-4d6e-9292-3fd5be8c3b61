WITH base_data AS (
    SELECT 
        i.code,
        i.name AS indicator_name,  -- 提前取 name
        EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) AS year,
        ROUND(SUM(iv.value) / 10000, 2) AS actual_value,
        ROUND(SUM(iv.value) / 10000, 2) AS budget_value
    FROM yjzb_indicator i
    JOIN yjzb_indicator_values iv ON i.id = iv.indicator_id
    WHERE EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) IN (2024, 2025)
      AND EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) BETWEEN 1 AND 5
    GROUP BY i.code, i.name, EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM'))
),

cigarette_sales_data AS (
    SELECT
        EXTRACT(YEAR FROM TO_DATE(period, 'YYYY-MM')) AS year,
        SUM(sales) AS total_sales
    FROM yjzb_cigarette_sales
    WHERE EXTRACT(YEAR FROM TO_DATE(period, 'YYYY-MM')) IN (2024, 2025)
      AND EXTRACT(MONTH FROM TO_DATE(period, 'YYYY-MM')) BETWEEN 1 AND 5
      AND is_deleted = 0
    GROUP BY EXTRACT(YEAR FROM TO_DATE(period, 'YYYY-MM'))
),

-- ✅ 优化：提前 JOIN 并 PIVOT，同时保留 code 和 name
base_pivot_data AS (
    SELECT
        code,
        indicator_name,
        MAX(CASE WHEN year = 2025 THEN actual_value END) AS v_2025_actual,
        MAX(CASE WHEN year = 2024 THEN actual_value END) AS v_2024_actual,
        MAX(CASE WHEN year = 2025 THEN budget_value END) AS v_2025_budget
    FROM base_data
    GROUP BY code, indicator_name
),

-- 合并卷烟销售数量（优先使用 yjzb_cigarette_sales 表）
pivot_data AS (
    SELECT * FROM base_pivot_data

    UNION ALL

    SELECT
        'juanyan_xiaoshou_shuliang' AS code,
        '卷烟销售数量' AS indicator_name,
        COALESCE((SELECT total_sales FROM cigarette_sales_data WHERE year = 2025), 0) AS v_2025_actual,
        COALESCE((SELECT total_sales FROM cigarette_sales_data WHERE year = 2024), 0) AS v_2024_actual,
        0 AS v_2025_budget
),

-- ✅ 后续 calculated 不再需要 JOIN，直接通过 indicator_name 匹配
calculated AS (
    -- 税利总额
    SELECT
        '税利总额' AS item,
        COALESCE(p.v_2025_actual, 0) AS v_2025_actual,
        COALESCE(p.v_2024_actual, 0) AS v_2024_actual,
        COALESCE(p.v_2025_budget, 0) AS v_2025_budget
    FROM pivot_data p
    WHERE p.indicator_name = '税利合计' OR p.code = 'shuilixiangji'

    UNION ALL

    -- 其中：利润总额
    SELECT
        '其中：利润总额',
        COALESCE(p.v_2025_actual, 0),
        COALESCE(p.v_2024_actual, 0),
        COALESCE(p.v_2025_budget, 0)
    FROM pivot_data p
    WHERE p.code = 'lirun_zonge_shui'

    UNION ALL

    -- 税费（不含所得税） = 税利合计 - 利润总额
    SELECT
        '税费（不含所得税）',
        COALESCE(p1.v_2025_actual, 0) - COALESCE(p2.v_2025_actual, 0),
        COALESCE(p1.v_2024_actual, 0) - COALESCE(p2.v_2024_actual, 0),
        COALESCE(p1.v_2025_budget, 0) - COALESCE(p2.v_2025_budget, 0)
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '税利合计' OR code = 'shuilixiangji') p1,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'lirun_zonge_shui') p2

    UNION ALL

    -- 卷烟销售数量
    SELECT
        '卷烟销售数量',
        COALESCE(p.v_2025_actual, 0),
        COALESCE(p.v_2024_actual, 0),
        COALESCE(p.v_2025_budget, 0)
    FROM pivot_data p
    WHERE p.indicator_name = '卷烟销售数量' OR p.code = 'juanyan_xiaoshou_shuliang'

    UNION ALL

    -- 卷烟销售收入
    SELECT
        '卷烟销售收入',
        COALESCE(p.v_2025_actual, 0),
        COALESCE(p.v_2024_actual, 0),
        COALESCE(p.v_2025_budget, 0)
    FROM pivot_data p
    WHERE p.indicator_name = '主营业务收入' OR p.code = 'zhuying_yewu_shouru'

    UNION ALL

    -- 卷烟销售成本
    SELECT
        '卷烟销售成本',
        COALESCE(p.v_2025_actual, 0),
        COALESCE(p.v_2024_actual, 0),
        COALESCE(p.v_2025_budget, 0)
    FROM pivot_data p
    WHERE p.indicator_name = '主营业务成本' OR p.code = 'zhuying_yewu_chengben'

    UNION ALL

    -- 毛利额 = 收入 - 成本
    SELECT
        '毛利额',
        COALESCE(p1.v_2025_actual, 0) - COALESCE(p2.v_2025_actual, 0),
        COALESCE(p1.v_2024_actual, 0) - COALESCE(p2.v_2024_actual, 0),
        COALESCE(p1.v_2025_budget, 0) - COALESCE(p2.v_2025_budget, 0)
    FROM 
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '主营业务收入' OR code = 'zhuying_yewu_shouru') p1,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '主营业务成本' OR code = 'zhuying_yewu_chengben') p2

    UNION ALL

    -- 其他业务收入
    SELECT
        '其他业务收入',
        COALESCE(p.v_2025_actual, 0),
        COALESCE(p.v_2024_actual, 0),
        COALESCE(p.v_2025_budget, 0)
    FROM pivot_data p
    WHERE p.indicator_name = '其他业务收入' OR p.code = 'qita_yewu_shouru'

    UNION ALL

    -- 其他业务成本
    SELECT
        '其他业务成本',
        COALESCE(p.v_2025_actual, 0),
        COALESCE(p.v_2024_actual, 0),
        COALESCE(p.v_2025_budget, 0)
    FROM pivot_data p
    WHERE p.indicator_name = '其他业务成本' OR p.code = 'qita_yewu_chengben'

    UNION ALL

    -- 三项费用总额
    SELECT
        '三项费用总额',
        COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0) + COALESCE(r.v_2025_actual, 0) + COALESCE(f.v_2025_actual, 0),
        COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0) + COALESCE(r.v_2024_actual, 0) + COALESCE(f.v_2024_actual, 0),
        COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0) + COALESCE(r.v_2025_budget, 0) + COALESCE(f.v_2025_budget, 0)
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '销售费用') s,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '管理费用') m,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '研发费用') r,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '财务费用') f
)

-- ✅ 最终输出（保持不变）
SELECT
    item AS "项目",
    TO_CHAR(ROUND(v_2025_actual, 2), 'FM999,999,990.00') AS "2025年1-5月",
    TO_CHAR(ROUND(v_2024_actual, 2), 'FM999,999,990.00') AS "2024年1-5月",
    CASE 
        WHEN v_2024_actual = 0 THEN 'n/a'
        ELSE TO_CHAR(ROUND((v_2025_actual - v_2024_actual) / NULLIF(v_2024_actual, 0) * 100, 2), 'FM990.00')
    END AS "同比增减（%）",
    TO_CHAR(ROUND(v_2025_budget, 2), 'FM999,999,990.00') AS "2025年预算数",
    CASE 
        WHEN v_2025_budget = 0 THEN ''
        ELSE TO_CHAR(ROUND(v_2025_actual / NULLIF(v_2025_budget, 0) * 100, 2), 'FM990.00')
    END AS "预算执行进度（%）"
FROM (
    SELECT * FROM calculated

    UNION ALL

    -- 毛利率（%）
    SELECT
        '毛利率（%）' AS item,
        ROUND((COALESCE(p1.v_2025_actual, 0) - COALESCE(p2.v_2025_actual, 0)) / NULLIF(COALESCE(p1.v_2025_actual, 0), 0) * 100, 2),
        ROUND((COALESCE(p1.v_2024_actual, 0) - COALESCE(p2.v_2024_actual, 0)) / NULLIF(COALESCE(p1.v_2024_actual, 0), 0) * 100, 2),
        ROUND((COALESCE(p1.v_2025_budget, 0) - COALESCE(p2.v_2025_budget, 0)) / NULLIF(COALESCE(p1.v_2025_budget, 0), 0) * 100, 2)
    FROM 
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '主营业务收入') p1,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '主营业务成本') p2

    UNION ALL

    -- 三项费用率（%）
    SELECT
        '三项费用率（%）' AS item,
        ROUND((COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0) + COALESCE(r.v_2025_actual, 0) + COALESCE(f.v_2025_actual, 0)) / NULLIF(p1.v_2025_actual, 0) * 100, 2),
        ROUND((COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0) + COALESCE(r.v_2024_actual, 0) + COALESCE(f.v_2024_actual, 0)) / NULLIF(p1.v_2024_actual, 0) * 100, 2),
        ROUND((COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0) + COALESCE(r.v_2025_budget, 0) + COALESCE(f.v_2025_budget, 0)) / NULLIF(p1.v_2025_budget, 0) * 100, 2)
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '主营业务收入') p1,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '销售费用') s,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '管理费用') m,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '研发费用') r,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '财务费用') f
) t
ORDER BY 
    CASE item
        WHEN '税利总额' THEN 1
        WHEN '其中：利润总额' THEN 2
        WHEN '税费（不含所得税）' THEN 3
        WHEN '卷烟销售数量' THEN 4
        WHEN '卷烟销售收入' THEN 5
        WHEN '卷烟销售成本' THEN 6
        WHEN '毛利额' THEN 7
        WHEN '毛利率（%）' THEN 8
        WHEN '其他业务收入' THEN 9
        WHEN '其他业务成本' THEN 10
        WHEN '三项费用总额' THEN 11
        WHEN '三项费用率（%）' THEN 12
    END;