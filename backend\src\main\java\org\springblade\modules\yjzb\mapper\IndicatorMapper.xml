<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.IndicatorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indicatorResultMap" type="org.springblade.modules.yjzb.pojo.entity.IndicatorEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="description" property="description"/>
        <result column="indicator_type_id" property="indicatorTypeId"/>
        <result column="data_type" property="dataType"/>
        <result column="unit" property="unit"/>
        <result column="calculation_formula" property="calculationFormula"/>
        <result column="data_source_config" property="dataSourceConfig"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectIndicatorPage" resultMap="indicatorResultMap">
        select * from yjzb_indicator where is_deleted = 0
    </select>


    <select id="exportIndicator" resultType="org.springblade.modules.yjzb.excel.IndicatorExcel">
        SELECT * FROM yjzb_indicator ${ew.customSqlSegment}
    </select>

</mapper>
