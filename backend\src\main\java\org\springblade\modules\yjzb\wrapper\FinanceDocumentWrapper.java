/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.yjzb.pojo.entity.FinanceDocumentEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceDocumentVO;

/**
 * 知识库文件包装类，返回视图层所需的字段
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
public class FinanceDocumentWrapper extends BaseEntityWrapper<FinanceDocumentEntity, FinanceDocumentVO> {

    public static FinanceDocumentWrapper build() {
        return new FinanceDocumentWrapper();
    }

    @Override
    public FinanceDocumentVO entityVO(FinanceDocumentEntity financeDocument) {
        FinanceDocumentVO financeDocumentVO = BeanUtil.copy(financeDocument, FinanceDocumentVO.class);
        return financeDocumentVO;
    }
}
