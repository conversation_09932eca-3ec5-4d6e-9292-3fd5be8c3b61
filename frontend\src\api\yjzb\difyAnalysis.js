import request from '@/axios';

/**
 * 调用Dify工作流进行数据分析
 * @param {Object} inputs - 输入参数
 * @param {string} user - 用户标识
 * @returns {Promise} API响应
 */
export const runDifyWorkflow = (inputs, user = 'system') => {
  return request({
    url: '/yjzb/dify/workflow/run-streaming-once',
    method: 'post',
    data: {
      inputs,
      user
    }
  });
}

/**
 * 查询工作流执行详情
 * @param {string} workflowRunId - 工作流运行ID
 * @returns {Promise} API响应
 */
export const getWorkflowRunDetail = (workflowRunId) => {
  return request({
    url: `/yjzb/dify/workflow/run/detail/${workflowRunId}`,
    method: 'get'
  });
}

/**
 * 数据概览AI分析
 * @param {Object} data - 概览数据
 * @returns {Promise} API响应
 */
export const analyzeOverview = (data) => {
  const inputs = {
    analysis_type: 'overview',
    data: JSON.stringify(data),
    description: '请对数据概览进行深度分析，包括当前月份核心指标、预算执行情况、异常检测等'
  };
  return runDifyWorkflow(inputs);
}

/**
 * 结构分析AI分析
 * @param {Object} data - 结构数据
 * @returns {Promise} API响应
 */
export const analyzeStructure = (data) => {
  const inputs = {
    analysis_type: 'structure',
    data: JSON.stringify(data),
    description: '请对费用结构进行分析，识别主要费用类别、占比分布、优化建议等'
  };
  return runDifyWorkflow(inputs);
}

/**
 * 趋势分析AI分析
 * @param {Object} data - 趋势数据
 * @returns {Promise} API响应
 */
export const analyzeTrend = (data) => {
  const inputs = {
    analysis_type: 'trend',
    data: JSON.stringify(data),
    description: '请对费用趋势进行分析，包括趋势方向、拐点识别、季节性分析等'
  };
  return runDifyWorkflow(inputs);
}

/**
 * 波动性分析AI分析
 * @param {Object} data - 波动性数据
 * @returns {Promise} API响应
 */
export const analyzeVolatility = (data) => {
  const inputs = {
    analysis_type: 'volatility',
    data: JSON.stringify(data),
    description: '请对费用波动性进行分析，包括波动幅度、异常检测、风险评估等'
  };
  return runDifyWorkflow(inputs);
}

/**
 * 增长率分析AI分析
 * @param {Object} data - 增长率数据
 * @returns {Promise} API响应
 */
export const analyzeGrowth = (data) => {
  const inputs = {
    analysis_type: 'growth',
    data: JSON.stringify(data),
    description: '请对费用增长率进行分析，包括环比同比分析、增长驱动因素、异常增长识别等'
  };
  return runDifyWorkflow(inputs);
}

/**
 * 预算分析AI分析
 * @param {Object} data - 预算数据
 * @returns {Promise} API响应
 */
export const analyzeBudget = (data) => {
  const inputs = {
    analysis_type: 'budget',
    data: JSON.stringify(data),
    description: '请对预算执行情况进行分析，包括预算偏差、执行率评估、风险等级等'
  };
  return runDifyWorkflow(inputs);
}

/**
 * 预测分析AI分析
 * @param {Object} data - 预测数据
 * @returns {Promise} API响应
 */
export const analyzeForecast = (data) => {
  const inputs = {
    analysis_type: 'forecast',
    data: JSON.stringify(data),
    description: '请对预测结果进行分析，包括预测准确性、趋势预测、风险预警等'
  };
  return runDifyWorkflow(inputs);
}
