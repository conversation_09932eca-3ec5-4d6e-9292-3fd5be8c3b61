/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.pojo.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 指标预测结果 实体类
 *
 * 对应表：yjzb_indicator_forecast
 */
@Data
@TableName("yjzb_indicator_forecast")
@Schema(description = "IndicatorForecast对象")
@EqualsAndHashCode(callSuper = true)
public class IndicatorForecastEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 指标ID（关联指标表） */
    @Schema(description = "指标ID（关联指标表）")
    private Long indicatorId;

    /** 指标名称（为历史保留冗余） */
    @Schema(description = "指标名称（为历史保留冗余）")
    private String indicatorName;

    /** 年份（YYYY） */
    @Schema(description = "年份（YYYY）")
    private Integer year;

    @Schema(description = "2月预测值")
    private BigDecimal forecastM02;
    @Schema(description = "3月预测值")
    private BigDecimal forecastM03;
    @Schema(description = "4月预测值")
    private BigDecimal forecastM04;
    @Schema(description = "5月预测值")
    private BigDecimal forecastM05;
    @Schema(description = "6月预测值")
    private BigDecimal forecastM06;
    @Schema(description = "7月预测值")
    private BigDecimal forecastM07;
    @Schema(description = "8月预测值")
    private BigDecimal forecastM08;
    @Schema(description = "9月预测值")
    private BigDecimal forecastM09;
    @Schema(description = "10月预测值")
    private BigDecimal forecastM10;
    @Schema(description = "11月预测值")
    private BigDecimal forecastM11;
    @Schema(description = "12月预测值")
    private BigDecimal forecastM12;
}
