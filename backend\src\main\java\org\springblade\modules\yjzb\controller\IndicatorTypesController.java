/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.IndicatorTypesEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorTypesVO;
import org.springblade.modules.yjzb.excel.IndicatorTypesExcel;
import org.springblade.modules.yjzb.wrapper.IndicatorTypesWrapper;
import org.springblade.modules.yjzb.service.IIndicatorTypesService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 指标类型 控制器
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/indicatorTypes")
@Tag(name = "指标类型", description = "指标类型接口")
public class IndicatorTypesController extends BladeController {

    private final IIndicatorTypesService indicatorTypesService;

    /**
     * 指标类型 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入indicatorTypes")
    public R<IndicatorTypesVO> detail(IndicatorTypesEntity indicatorTypes) {
        IndicatorTypesEntity detail = indicatorTypesService.getOne(Condition.getQueryWrapper(indicatorTypes));
        return R.data(IndicatorTypesWrapper.build().entityVO(detail));
    }

    /**
     * 指标类型 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入indicatorTypes")
    public R<IPage<IndicatorTypesVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> indicatorTypes,
            Query query) {
        // 针对PostgreSQL数据库处理查询条件，避免bigint字段LIKE查询类型不匹配错误
        IndicatorTypesWrapper.build().indicatorTypesQuery(indicatorTypes);
        IPage<IndicatorTypesEntity> pages = indicatorTypesService.page(Condition.getPage(query),
                Condition.getQueryWrapper(indicatorTypes, IndicatorTypesEntity.class));
        return R.data(IndicatorTypesWrapper.build().pageVO(pages));
    }

    /**
     * 指标类型 All
     */
    @GetMapping("/all")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "All", description = "传入indicatorTypes")
    public R<List<IndicatorTypesVO>> all(@Parameter(hidden = true) @RequestParam Map<String, Object> indicatorTypes) {
        // 针对PostgreSQL数据库处理查询条件，避免bigint字段LIKE查询类型不匹配错误
        IndicatorTypesWrapper.build().indicatorTypesQuery(indicatorTypes);
        List<IndicatorTypesEntity> list = indicatorTypesService
                .list(Condition.getQueryWrapper(indicatorTypes, IndicatorTypesEntity.class));
        return R.data(IndicatorTypesWrapper.build().listVO(list));
    }

    /**
     * 指标类型 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入indicatorTypes")
    public R<IPage<IndicatorTypesVO>> page(IndicatorTypesVO indicatorTypes, Query query) {
        IPage<IndicatorTypesVO> pages = indicatorTypesService.selectIndicatorTypesPage(Condition.getPage(query),
                indicatorTypes);
        return R.data(pages);
    }

    /**
     * 指标类型 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入indicatorTypes")
    public R save(@Valid @RequestBody IndicatorTypesEntity indicatorTypes) {
        return R.status(indicatorTypesService.save(indicatorTypes));
    }

    /**
     * 指标类型 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入indicatorTypes")
    public R update(@Valid @RequestBody IndicatorTypesEntity indicatorTypes) {
        return R.status(indicatorTypesService.updateById(indicatorTypes));
    }

    /**
     * 指标类型 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入indicatorTypes")
    public R submit(@Valid @RequestBody IndicatorTypesEntity indicatorTypes) {
        return R.status(indicatorTypesService.saveOrUpdate(indicatorTypes));
    }

    /**
     * 指标类型 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(indicatorTypesService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 导出数据
     */
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    @GetMapping("/export-indicatorTypes")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "导出数据", description = "传入indicatorTypes")
    public void exportIndicatorTypes(@Parameter(hidden = true) @RequestParam Map<String, Object> indicatorTypes,
            BladeUser bladeUser, HttpServletResponse response) {
        // 针对PostgreSQL数据库处理查询条件，避免bigint字段LIKE查询类型不匹配错误
        IndicatorTypesWrapper.build().indicatorTypesQuery(indicatorTypes);
        QueryWrapper<IndicatorTypesEntity> queryWrapper = Condition.getQueryWrapper(indicatorTypes,
                IndicatorTypesEntity.class);
        // if (!AuthUtil.isAdministrator()) {
        // queryWrapper.lambda().eq(IndicatorTypes::getTenantId,
        // bladeUser.getTenantId());
        // }
        // queryWrapper.lambda().eq(IndicatorTypesEntity::getIsDeleted,
        // BladeConstant.DB_NOT_DELETED);
        List<IndicatorTypesExcel> list = indicatorTypesService.exportIndicatorTypes(queryWrapper);
        ExcelUtil.export(response, "指标类型数据" + DateUtil.time(), "指标类型数据表", list, IndicatorTypesExcel.class);
    }

}
