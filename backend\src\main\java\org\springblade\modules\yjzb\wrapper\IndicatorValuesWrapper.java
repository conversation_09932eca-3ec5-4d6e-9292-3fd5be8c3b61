/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesVO;

import java.util.Map;
import java.util.Objects;

/**
 * IndicatorValues包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public class IndicatorValuesWrapper extends BaseEntityWrapper<IndicatorValuesEntity, IndicatorValuesVO> {

    public static IndicatorValuesWrapper build() {
        return new IndicatorValuesWrapper();
    }

    @Override
    public IndicatorValuesVO entityVO(IndicatorValuesEntity indicatorValues) {
        return Objects.requireNonNull(BeanUtil.copyProperties(indicatorValues, IndicatorValuesVO.class));
    }

    /**
     * 查询条件处理 - 针对PostgreSQL数据库的特殊处理
     * 解决bigint字段LIKE查询类型不匹配的问题
     */
    public void indicatorValuesQuery(Map<String, Object> indicatorValues) {
        // 针对PostgreSQL数据库bigint类型字段查询需要强转的处理

        // 处理indicator_id字段 - 数据库字段为bigint类型
        String indicatorIdKey = "indicatorId";
        if (Func.isNotEmpty(indicatorValues.get(indicatorIdKey))) {
            // 设置"="查询，避免bigint字段使用LIKE查询导致的类型不匹配错误
            indicatorValues.put(indicatorIdKey.concat("_equal"), Func.toLong(indicatorValues.get(indicatorIdKey)));
            // 删除默认的LIKE查询参数，防止PostgreSQL报错
            indicatorValues.remove(indicatorIdKey);
        }
        // 若前端已传入 indicatorId_equal（字符串），也需要显式转为 Long
        String indicatorIdEqualKey = indicatorIdKey.concat("_equal");
        if (Func.isNotEmpty(indicatorValues.get(indicatorIdEqualKey))) {
            indicatorValues.put(indicatorIdEqualKey, Func.toLong(indicatorValues.get(indicatorIdEqualKey)));
        }

        // 处理create_user字段 - 数据库字段为bigint类型
        String createUserKey = "createUser";
        if (Func.isNotEmpty(indicatorValues.get(createUserKey))) {
            indicatorValues.put(createUserKey.concat("_equal"), Func.toLong(indicatorValues.get(createUserKey)));
            indicatorValues.remove(createUserKey);
        }
        String createUserEqualKey = createUserKey.concat("_equal");
        if (Func.isNotEmpty(indicatorValues.get(createUserEqualKey))) {
            indicatorValues.put(createUserEqualKey, Func.toLong(indicatorValues.get(createUserEqualKey)));
        }

        // 处理create_dept字段 - 数据库字段为bigint类型
        String createDeptKey = "createDept";
        if (Func.isNotEmpty(indicatorValues.get(createDeptKey))) {
            indicatorValues.put(createDeptKey.concat("_equal"), Func.toLong(indicatorValues.get(createDeptKey)));
            indicatorValues.remove(createDeptKey);
        }
        String createDeptEqualKey = createDeptKey.concat("_equal");
        if (Func.isNotEmpty(indicatorValues.get(createDeptEqualKey))) {
            indicatorValues.put(createDeptEqualKey, Func.toLong(indicatorValues.get(createDeptEqualKey)));
        }

        // 处理update_user字段 - 数据库字段为bigint类型
        String updateUserKey = "updateUser";
        if (Func.isNotEmpty(indicatorValues.get(updateUserKey))) {
            indicatorValues.put(updateUserKey.concat("_equal"), Func.toLong(indicatorValues.get(updateUserKey)));
            indicatorValues.remove(updateUserKey);
        }
        String updateUserEqualKey = updateUserKey.concat("_equal");
        if (Func.isNotEmpty(indicatorValues.get(updateUserEqualKey))) {
            indicatorValues.put(updateUserEqualKey, Func.toLong(indicatorValues.get(updateUserEqualKey)));
        }

        // 处理status字段 - 数据库字段为int类型
        String statusKey = "status";
        if (Func.isNotEmpty(indicatorValues.get(statusKey))) {
            indicatorValues.put(statusKey.concat("_equal"), Func.toInt(indicatorValues.get(statusKey)));
            indicatorValues.remove(statusKey);
        }

        // 处理is_deleted字段 - 数据库字段为int类型
        String isDeletedKey = "isDeleted";
        if (Func.isNotEmpty(indicatorValues.get(isDeletedKey))) {
            indicatorValues.put(isDeletedKey.concat("_equal"), Func.toInt(indicatorValues.get(isDeletedKey)));
            indicatorValues.remove(isDeletedKey);
        }
    }

}