/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 */
package org.springblade.modules.yjzb.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "简要指标信息，仅含id与name")
public class SimpleIndicatorVO {
    @Schema(description = "指标ID", format = "int64")
    private Long id;

    @Schema(description = "指标名称")
    private String name;
}
