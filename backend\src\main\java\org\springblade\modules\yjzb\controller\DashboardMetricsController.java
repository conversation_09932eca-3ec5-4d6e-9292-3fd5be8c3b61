package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springblade.core.tool.api.R;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.modules.yjzb.service.IDashboardMetricsService;
import org.springblade.modules.yjzb.pojo.vo.DashboardMetricsResponseVO;

@RestController
@RequestMapping("/yjzb/dashboard")
@Tag(name = "首页指标看板", description = "计算5个核心指标")
@RequiredArgsConstructor
public class DashboardMetricsController extends BladeController {

    private final IDashboardMetricsService dashboardMetricsService;

    @GetMapping("/metrics")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "计算首页指标", description = "参数：period(YYYY-MM)，为空则默认当前月")
    public R<DashboardMetricsResponseVO> metrics(@RequestParam(required = false) String period) {
        DashboardMetricsResponseVO data = dashboardMetricsService.computeMetrics(period);
        return R.data(data);
    }

    @GetMapping("/expense-overview")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "费用概览(三项费用当月合计)", description = "返回当月/上月/去年同期三项费用(销售+管理+财务)合计，参数：period(YYYY-MM)")
    public R<org.springblade.modules.yjzb.pojo.vo.ExpenseOverviewVO> expenseOverview(
            @RequestParam(required = false) String period) {
        org.springblade.modules.yjzb.pojo.vo.ExpenseOverviewVO vo = dashboardMetricsService.expenseOverview(period);
        return R.data(vo);
    }
}
