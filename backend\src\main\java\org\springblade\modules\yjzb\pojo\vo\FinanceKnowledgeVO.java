/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.modules.yjzb.pojo.entity.FinanceKnowledgeEntity;

/**
 * 财务知识库 视图对象
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "财务知识库视图对象")
public class FinanceKnowledgeVO extends FinanceKnowledgeEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createUserName;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名")
    private String updateUserName;

    /**
     * 分类数量
     */
    @Schema(description = "分类数量")
    private Integer categoryCount;

    /**
     * 文档数量
     */
    @Schema(description = "文档数量")
    private Integer documentCount;

    /**
     * 同步状态描述
     */
    @Schema(description = "同步状态描述")
    private String syncStatusDesc;
}
