// vite.config.mjs
import {
  defineConfig,
  loadEnv
} from "file:///E:/codebase/yueshu/%E9%98%B3%E6%B1%9FAI%E6%8C%87%E6%A0%87%E7%AE%A1%E6%8E%A7/frontend/node_modules/vite/dist/node/index.js";
import { resolve } from "path";

// vite/plugins/index.js
import vue from "file:///E:/codebase/yueshu/%E9%98%B3%E6%B1%9FAI%E6%8C%87%E6%A0%87%E7%AE%A1%E6%8E%A7/frontend/node_modules/@vitejs/plugin-vue/dist/index.mjs";

// vite/plugins/auto-import.js
import autoImport from "file:///E:/codebase/yueshu/%E9%98%B3%E6%B1%9FAI%E6%8C%87%E6%A0%87%E7%AE%A1%E6%8E%A7/frontend/node_modules/unplugin-auto-import/dist/vite.js";
function createAutoImport() {
  return autoImport({
    imports: ["vue", "vue-router", "vuex"],
    dts: false
  });
}

// vite/plugins/compression.js
import compression from "file:///E:/codebase/yueshu/%E9%98%B3%E6%B1%9FAI%E6%8C%87%E6%A0%87%E7%AE%A1%E6%8E%A7/frontend/node_modules/vite-plugin-compression/dist/index.mjs";
function createCompression(env) {
  const { VITE_BUILD_COMPRESS } = env;
  const plugin = [];
  if (VITE_BUILD_COMPRESS) {
    const compressList = VITE_BUILD_COMPRESS.split(",");
    if (compressList.includes("gzip")) {
      plugin.push(
        compression({
          ext: ".gz",
          deleteOriginFile: false
        })
      );
    }
    if (compressList.includes("brotli")) {
      plugin.push(
        compression({
          ext: ".br",
          algorithm: "brotliCompress",
          deleteOriginFile: false
        })
      );
    }
  }
  return plugin;
}

// vite/plugins/setup-extend.js
import setupExtend from "file:///E:/codebase/yueshu/%E9%98%B3%E6%B1%9FAI%E6%8C%87%E6%A0%87%E7%AE%A1%E6%8E%A7/frontend/node_modules/vite-plugin-vue-setup-extend/dist/index.mjs";
function createSetupExtend() {
  return setupExtend();
}

// vite/plugins/index.js
function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [vue()];
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  isBuild && vitePlugins.push(...createCompression(viteEnv));
  return vitePlugins;
}

// vite.config.mjs
var __vite_injected_original_dirname = "E:\\codebase\\yueshu\\\u9633\u6C5FAI\u6307\u6807\u7BA1\u63A7\\frontend";
var vite_config_default = ({
  mode,
  command
}) => {
  const env = loadEnv(mode, process.cwd());
  const {
    VITE_APP_ENV,
    VITE_APP_BASE
  } = env;
  const isProd = VITE_APP_ENV === "production";
  const buildConfig = {
    target: "esnext",
    minify: isProd ? "terser" : "esbuild"
    // 根据环境选择压缩工具
  };
  if (isProd) {
    buildConfig.terserOptions = {
      compress: {
        drop_console: true,
        // 删除 console
        drop_debugger: true
        // 删除 debugger
      },
      format: {
        comments: false
        // 删除所有注释
      }
    };
  }
  return defineConfig({
    base: VITE_APP_BASE,
    define: {
      __VUE_I18N_FULL_INSTALL__: true,
      __VUE_I18N_LEGACY_API__: true,
      __INTLIFY_PROD_DEVTOOLS__: false
    },
    server: {
      port: 2888,
      proxy: {
        "/api": {
          target: "http://localhost:8080",
          //target: 'https://saber3.bladex.cn/api',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, "")
        }
      }
    },
    resolve: {
      alias: {
        "~": resolve(__vite_injected_original_dirname, "./"),
        "@": resolve(__vite_injected_original_dirname, "./src"),
        components: resolve(__vite_injected_original_dirname, "./src/components"),
        styles: resolve(__vite_injected_original_dirname, "./src/styles"),
        utils: resolve(__vite_injected_original_dirname, "./src/utils")
      }
    },
    plugins: createVitePlugins(env, command === "build"),
    build: buildConfig,
    optimizeDeps: {
      esbuildOptions: {
        target: "esnext"
      }
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
