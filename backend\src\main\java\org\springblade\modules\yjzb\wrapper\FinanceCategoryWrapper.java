/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceCategoryVO;

/**
 * 知识分类包装类，返回视图层所需的字段
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
public class FinanceCategoryWrapper extends BaseEntityWrapper<FinanceCategoryEntity, FinanceCategoryVO> {

    public static FinanceCategoryWrapper build() {
        return new FinanceCategoryWrapper();
    }

    @Override
    public FinanceCategoryVO entityVO(FinanceCategoryEntity financeCategory) {
        FinanceCategoryVO financeCategoryVO = BeanUtil.copy(financeCategory, FinanceCategoryVO.class);
        return financeCategoryVO;
    }
}
