#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查询yjzb_indicator_types获取所有指标类型
"""

import os
import psycopg2
import pandas as pd
import requests
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'gzyc-nlp-2024.rwlb.rds.aliyuncs.com'),
    'port': os.getenv('DB_PORT', '1921'),
    'database': os.getenv('DB_NAME', 'yjyc'),
    'user': os.getenv('DB_USER', 'gzyc'),
    'password': os.getenv('DB_PASSWORD', 'gzyc1234')
}

# API配置
API_BASE_URL = os.getenv('API_BASE_URL', 'http://localhost:8088')


def connect_to_db():
    """
    连接到PostgreSQL数据库
    """
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            database=DB_CONFIG['database'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        return conn
    except Exception as e:
        print(f"数据库连接错误: {e}")
        return None


def get_all_indicator_types():
    """
    查询yjzb_indicator_types表获取所有指标类型
    """
    conn = connect_to_db()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        query = "SELECT id, type_name, type_code, description, data_type, unit FROM yjzb_indicator_types WHERE is_deleted = 0"
        cursor.execute(query)
        rows = cursor.fetchall()
        
        # 转换为DataFrame
        df = pd.DataFrame(rows, columns=['id', 'type_name', 'type_code', 'description', 'data_type', 'unit'])
        return df
    except Exception as e:
        print(f"查询指标类型错误: {e}")
        return None
    finally:
        conn.close()


def main():
    # 获取所有指标类型
    indicator_types_df = get_all_indicator_types()
    
    if indicator_types_df is not None:
        print("成功获取指标类型数据:")
        print(indicator_types_df)
    else:
        print("获取指标类型数据失败")


if __name__ == "__main__":
    main()