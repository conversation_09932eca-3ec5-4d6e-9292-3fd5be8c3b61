WITH base_data AS (
    SELECT
        i.code,
        i.name AS indicator_name,
        EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) AS year,
        ROUND(SUM(iv.value) / 10000, 2) AS actual_value,
        ROUND(SUM(iv.value) / 10000, 2) AS budget_value
    FROM yjzb_indicator i
    JOIN yjzb_indicator_values iv ON i.id = iv.indicator_id
    WHERE EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) IN (2024, 2025)
      AND EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) BETWEEN 1 AND 5
      AND iv.is_deleted = 0
      AND i.is_deleted = 0
    GROUP BY i.code, i.name, EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM'))
),

-- 数据透视，获取2024年和2025年的数据
pivot_data AS (
    SELECT
        code,
        indicator_name,
        MAX(CASE WHEN year = 2025 THEN actual_value END) AS v_2025_actual,
        MAX(CASE WHEN year = 2024 THEN actual_value END) AS v_2024_actual,
        MAX(CASE WHEN year = 2025 THEN budget_value END) AS v_2025_budget
    FROM base_data
    GROUP BY code, indicator_name
),

-- 计算重点费用项目
calculated AS (
    -- 两项费用率（%）（销售+管理）
    SELECT
        '两项费用率（%）' AS item,
        ROUND((COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0)) / NULLIF(r.v_2025_actual, 0) * 100, 2) AS v_2025_actual,
        ROUND((COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0)) / NULLIF(r.v_2024_actual, 0) * 100, 2) AS v_2024_actual,
        ROUND((COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0)) / NULLIF(r.v_2025_budget, 0) * 100, 2) AS v_2025_budget,
        1 AS sort_order
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '销售费用' OR code = 'xiaoshou_feiyong') s,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '管理费用' OR code = 'guanli_feiyong') m,
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '主营业务收入' OR code = 'zhuying_yewu_shouru') r

    UNION ALL

    -- 会议费（销售费用+管理费用）
    SELECT
        '会议费',
        COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
        COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
        COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
        2
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'huiyi_xiaoshou') s
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'huiyi_guanli') m ON 1=1

    UNION ALL

    -- 车辆运行费（车辆修理费+车辆保险费+车辆燃油费+车杂费）
    SELECT
        '车辆运行费',
        COALESCE(xr.v_2025_actual, 0) + COALESCE(xb.v_2025_actual, 0) + COALESCE(xf.v_2025_actual, 0) + COALESCE(xz.v_2025_actual, 0) +
        COALESCE(gr.v_2025_actual, 0) + COALESCE(gb.v_2025_actual, 0) + COALESCE(gf.v_2025_actual, 0) + COALESCE(gz.v_2025_actual, 0),
        COALESCE(xr.v_2024_actual, 0) + COALESCE(xb.v_2024_actual, 0) + COALESCE(xf.v_2024_actual, 0) + COALESCE(xz.v_2024_actual, 0) +
        COALESCE(gr.v_2024_actual, 0) + COALESCE(gb.v_2024_actual, 0) + COALESCE(gf.v_2024_actual, 0) + COALESCE(gz.v_2024_actual, 0),
        COALESCE(xr.v_2025_budget, 0) + COALESCE(xb.v_2025_budget, 0) + COALESCE(xf.v_2025_budget, 0) + COALESCE(xz.v_2025_budget, 0) +
        COALESCE(gr.v_2025_budget, 0) + COALESCE(gb.v_2025_budget, 0) + COALESCE(gf.v_2025_budget, 0) + COALESCE(gz.v_2025_budget, 0),
        3
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'xiuli_cheliang_xiaoshou') xr
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'baoxian_cheliang_xiaoshou') xb ON 1=1
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'ranliao_cheliang_xiaoshou') xf ON 1=1
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'chezha_xiaoshou') xz ON 1=1
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'xiuli_cheliang_guanli') gr ON 1=1
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'baoxian_cheliang_guanli') gb ON 1=1
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'ranliao_cheliang_guanli') gf ON 1=1
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'chezha_guanli') gz ON 1=1

    UNION ALL

    -- （一）车辆修理费
    SELECT
        '（一）车辆修理费',
        COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
        COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
        COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
        4
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'xiuli_cheliang_xiaoshou') s
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'xiuli_cheliang_guanli') m ON 1=1

    UNION ALL

    -- （二）车辆保险费
    SELECT
        '（二）车辆保险费',
        COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
        COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
        COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
        5
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'baoxian_cheliang_xiaoshou') s
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'baoxian_cheliang_guanli') m ON 1=1

    UNION ALL

    -- （三）车辆燃油费
    SELECT
        '（三）车辆燃油费',
        COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
        COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
        COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
        6
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'ranliao_cheliang_xiaoshou') s
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'ranliao_cheliang_guanli') m ON 1=1

    UNION ALL

    -- （四）车杂费
    SELECT
        '（四）车杂费',
        COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
        COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
        COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
        7
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'chezha_xiaoshou') s
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'chezha_guanli') m ON 1=1

    UNION ALL

    -- 福利费（销售费用+管理费用中的员工福利）
    SELECT
        '福利费',
        COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
        COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
        COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
        8
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_yuangongfuli_xiaoshou') s
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_yuangongfuli_guanli') m ON 1=1

    UNION ALL

    -- （一）在职人员福利费支出
    SELECT
        '（一）在职人员福利费支出',
        COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
        COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
        COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
        9
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_yuangongfuli_xiaoshou') s
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_yuangongfuli_guanli') m ON 1=1

    UNION ALL

    -- （二）离退休人员福利费支出
    SELECT
        '（二）离退休人员福利费支出',
        COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
        COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
        COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
        10
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_lizhi_xiaoshou') s
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_lizhi_guanli') m ON 1=1

    UNION ALL

    -- 实发工资（销售费用+管理费用中的工资）
    SELECT
        '实发工资',
        COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
        COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
        COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
        11
    FROM
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_gongzi_xiaoshou') s
    FULL OUTER JOIN
        (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_gongzi_guanli') m ON 1=1

    UNION ALL

    -- 零售终端建设费
    SELECT
        '零售终端建设费',
        COALESCE(p.v_2025_actual, 0),
        COALESCE(p.v_2024_actual, 0),
        COALESCE(p.v_2025_budget, 0),
        12
    FROM pivot_data p
    WHERE p.code = 'lingshou_zhongduan_xiaoshou'

    UNION ALL

    -- （一）终端形象提升
    SELECT
        '（一）终端形象提升',
        0, -- 需要根据实际数据调整
        0,
        0,
        13

    UNION ALL

    -- （二）终端信息化建设
    SELECT
        '（二）终端信息化建设',
        0,
        0,
        0,
        14

    UNION ALL

    -- （三）消费体验区建设
    SELECT
        '（三）消费体验区建设',
        0,
        0,
        0,
        15

    UNION ALL

    -- （四）零售客户培训
    SELECT
        '（四）零售客户培训',
        0,
        0,
        0,
        16

    UNION ALL

    -- 文明吸烟环境建设费
    SELECT
        '文明吸烟环境建设费',
        COALESCE(p.v_2025_actual, 0),
        COALESCE(p.v_2024_actual, 0),
        COALESCE(p.v_2025_budget, 0),
        17
    FROM pivot_data p
    WHERE p.code = 'wenming_xiyan_guanli'

    UNION ALL

    -- 研发费
    SELECT
        '研发费',
        COALESCE(p.v_2025_actual, 0),
        COALESCE(p.v_2024_actual, 0),
        COALESCE(p.v_2025_budget, 0),
        18
    FROM pivot_data p
    WHERE p.code = 'qiyeyanfa_guanli'

    UNION ALL

    -- 对外捐赠支出
    SELECT
        '对外捐赠支出',
        0, -- 需要根据实际数据调整
        0,
        0,
        19

    UNION ALL

    -- （一）扶贫捐赠支出
    SELECT
        '（一）扶贫捐赠支出',
        0,
        0,
        0,
        20

    UNION ALL

    -- （二）水源工程支出
    SELECT
        '（二）水源工程支出',
        0,
        0,
        0,
        21
)

-- 最终输出，包含福利费占实发工资比例的计算
SELECT * FROM (
    SELECT
        item AS "项目",
        CASE
            WHEN item = '两项费用率（%）' THEN TO_CHAR(v_2025_actual, 'FM990.00')
            ELSE TO_CHAR(ROUND(v_2025_actual, 2), 'FM999,999,990.00')
        END AS "2025年1-5月",
        CASE
            WHEN item = '两项费用率（%）' THEN TO_CHAR(v_2024_actual, 'FM990.00')
            ELSE TO_CHAR(ROUND(v_2024_actual, 2), 'FM999,999,990.00')
        END AS "2024年1-5月",
        CASE
            WHEN v_2024_actual = 0 AND item != '两项费用率（%）' THEN 'n/a'
            WHEN item = '两项费用率（%）' THEN TO_CHAR(ROUND(v_2025_actual - v_2024_actual, 2), 'FM990.00')
            ELSE TO_CHAR(ROUND((v_2025_actual - v_2024_actual) / NULLIF(v_2024_actual, 0) * 100, 2), 'FM990.00')
        END AS "同比增减（%）",
        CASE
            WHEN item = '两项费用率（%）' THEN TO_CHAR(v_2025_budget, 'FM990.00')
            ELSE TO_CHAR(ROUND(v_2025_budget, 2), 'FM999,999,990.00')
        END AS "2025年预算数",
        CASE
            WHEN v_2025_budget = 0 OR item = '两项费用率（%）' THEN '-'
            ELSE TO_CHAR(ROUND(v_2025_actual / NULLIF(v_2025_budget, 0) * 100, 2), 'FM990.00')
        END AS "执行进度（%）",
        sort_order
    FROM calculated

    UNION ALL

    -- 福利费占实发工资比例（%）
    SELECT
        '福利费占实发工资比例（%）' AS "项目",
        TO_CHAR(ROUND(
            (SELECT v_2025_actual FROM calculated WHERE item = '福利费') /
            NULLIF((SELECT v_2025_actual FROM calculated WHERE item = '实发工资'), 0) * 100, 2
        ), 'FM990.00') AS "2025年1-5月",
        TO_CHAR(ROUND(
            (SELECT v_2024_actual FROM calculated WHERE item = '福利费') /
            NULLIF((SELECT v_2024_actual FROM calculated WHERE item = '实发工资'), 0) * 100, 2
        ), 'FM990.00') AS "2024年1-5月",
        TO_CHAR(ROUND(
            ((SELECT v_2025_actual FROM calculated WHERE item = '福利费') /
             NULLIF((SELECT v_2025_actual FROM calculated WHERE item = '实发工资'), 0) * 100) -
            ((SELECT v_2024_actual FROM calculated WHERE item = '福利费') /
             NULLIF((SELECT v_2024_actual FROM calculated WHERE item = '实发工资'), 0) * 100), 2
        ), 'FM990.00') AS "同比增减（%）",
        TO_CHAR(ROUND(
            (SELECT v_2025_budget FROM calculated WHERE item = '福利费') /
            NULLIF((SELECT v_2025_budget FROM calculated WHERE item = '实发工资'), 0) * 100, 2
        ), 'FM990.00') AS "2025年预算数",
        TO_CHAR(ROUND(
            ((SELECT v_2025_actual FROM calculated WHERE item = '福利费') /
             NULLIF((SELECT v_2025_actual FROM calculated WHERE item = '实发工资'), 0) * 100) /
            NULLIF(((SELECT v_2025_budget FROM calculated WHERE item = '福利费') /
                    NULLIF((SELECT v_2025_budget FROM calculated WHERE item = '实发工资'), 0) * 100), 0) * 100, 2
        ), 'FM990.00') AS "执行进度（%）",
        12 AS sort_order
) final_result
ORDER BY sort_order;
