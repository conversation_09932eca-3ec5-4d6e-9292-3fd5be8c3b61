/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.modules.yjzb.pojo.entity.FinanceDocumentEntity;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;


/**
 * 知识库文件 数据传输对象
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "知识库文件数据传输对象")
public class FinanceDocumentDTO extends FinanceDocumentEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 上传文件
     */
    @Schema(description = "上传文件")
    private MultipartFile file;

    /**
     * 文件标签列表
     */
    @Schema(description = "文件标签列表")
    private List<String> tagList;

    /**
     * 是否同步到Dify
     */
    @Schema(description = "是否同步到Dify")
    private Boolean syncDify;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String categoryName;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称")
    private String knowledgeName;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createUserName;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名")
    private String updateUserName;
}
