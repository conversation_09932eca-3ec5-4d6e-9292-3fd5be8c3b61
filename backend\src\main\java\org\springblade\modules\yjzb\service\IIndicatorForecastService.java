package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.yjzb.excel.IndicatorForecastExcel;
import org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorForecastVO;
import org.springblade.modules.yjzb.pojo.dto.IndicatorPredictFeatureDTO;

import java.util.List;

/**
 * 指标预测结果 服务类
 */
public interface IIndicatorForecastService extends BaseService<IndicatorForecastEntity> {
    IPage<IndicatorForecastVO> selectIndicatorForecastPage(IPage<IndicatorForecastVO> page, IndicatorForecastVO query);

    List<IndicatorForecastExcel> exportIndicatorForecast(Wrapper<IndicatorForecastEntity> queryWrapper);

    IndicatorForecastEntity findByIndicatorAndYear(Long indicatorId, Integer year);

    /**
     * 基于宽表特征进行单月预测，返回预测值。
     *
     * @param feature 特征输入（来自 费用指标_宽表.csv 的同名字段）
     * @return 预测值
     */
    java.math.BigDecimal predictMonthlyValue(IndicatorPredictFeatureDTO feature);

    /**
     * 传入指标ID与期间(YYYY-MM)，由Service内部查询指标值/去年同期/累计/预算等，
     * 组装特征并调用预测，返回预测值。
     */
    java.math.BigDecimal predictByIndicatorAndPeriod(Long indicatorId, String period);

    /**
     * 传入指标ID，预测当前年份2-12月的月度值：
     * - 对每个目标月份，优先使用真实数据作为滞后特征；
     * - 若缺失，则使用先前月份的预测结果作为滞后特征递推；
     * - 预测完成后，写入/更新预测结果表对应年份记录（forecastM02..M12）。
     *
     * @param indicatorId 指标ID
     * @return 该年份的预测结果实体
     */
    org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity predictCurrentYearForIndicator(Long indicatorId);
}
