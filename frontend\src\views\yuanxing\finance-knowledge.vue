<template>
  <!-- 财务税费管控 - 财务知识库页面 -->
  <basic-container>
    <!-- 页面标题和搜索 -->
    <div class="page-header">
      <h3>财务知识库</h3>
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索知识内容、标题、标签..."
          size="large"
          clearable
          @keyup.enter="handleSearch"
          style="width: 400px; margin-right: 15px;"
        >
          <template #prefix>
            <el-icon><search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" size="large" @click="handleSearch">
          <el-icon><search /></el-icon> 搜索
        </el-button>
        <el-button size="large" @click="handleAdd" v-if="hasPermission">
          <el-icon><plus /></el-icon> 新增文件
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：知识分类导航 -->
      <el-col :span="5">
        <div class="category-panel">
          <div class="panel-header">
            <h4>知识分类</h4>
          </div>
          <div class="category-tree" @contextmenu="handleTreeContextmenu">
            <el-tree
                    :data="categoryTree"
                    :props="treeProps"
                    node-key="id"
                    :default-expanded-keys="['root', 1, 2, 3]"
                    :default-checked-keys="['root']"
                    :current-node-key="currentNodeKey"
                    :highlight-current="true"
                    @node-click="handleCategoryClick"
                    @node-contextmenu="handleNodeContextmenu"
                    :expand-on-click-node="false"
            >
              <template #default="{ node, data }">
                <span class="category-node">
                  <el-icon><folder /></el-icon>
                  <span class="node-label">{{ node.label }}</span>
                  <span class="node-count">({{ data.count || 0 }})</span>
                </span>
              </template>
            </el-tree>

            <!-- 右键菜单 -->
            <div
              v-if="contextMenuVisible"
              class="context-menu"
              :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
              @click.stop
            >
              <div class="menu-item" @click="handleAddCategory" v-if="canAddChild">
                <el-icon><plus /></el-icon>
                <span>{{ currentNodeData ? '新增子分类' : '新增分类' }}</span>
              </div>
              <div class="menu-item" @click="handleEditCategory" v-if="currentNodeData && currentNodeData.id !== 'root'">
                <el-icon><edit /></el-icon>
                <span>编辑分类</span>
              </div>
              <div class="menu-item danger" @click="handleDeleteCategory" v-if="currentNodeData && canDelete">
                <el-icon><delete /></el-icon>
                <span>删除分类</span>
              </div>
            </div>
          </div>
          
          <!-- 热门标签 -->
          <div class="hot-tags">
            <h5>热门标签</h5>
            <div class="tag-list">
              <el-tag
                v-for="tag in hotTags"
                :key="tag.name"
                :type="selectedTags.includes(tag.name) ? 'primary' : 'info'"
                class="tag-item"
                @click="toggleTag(tag.name)"
              >
                {{ tag.name }} ({{ tag.count }})
              </el-tag>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧：知识内容列表 -->
      <el-col :span="19">
        <!-- 筛选工具栏 -->
        <div class="filter-toolbar">
          <div class="filter-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>知识库</el-breadcrumb-item>
              <el-breadcrumb-item v-if="currentCategory && currentCategory.id !== 'root'">{{ currentCategory.label }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="filter-right">
            <el-select v-model="sortType" size="small" style="width: 120px; margin-right: 10px;">
              <el-option label="最新发布" value="latest" />
              <el-option label="最多浏览" value="views" />
              <el-option label="最多收藏" value="favorites" />
              <el-option label="按标题" value="title" />
            </el-select>
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button label="list">列表</el-radio-button>
              <el-radio-button label="grid">卡片</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 选中的标签显示 -->
        <div class="selected-tags" v-if="selectedTags.length">
          <span>已选标签：</span>
          <el-tag
            v-for="tag in selectedTags"
            :key="tag"
            type="primary"
            closable
            @close="removeTag(tag)"
            style="margin-right: 8px;"
          >
            {{ tag }}
          </el-tag>
        </div>

        <!-- 知识内容列表 - 列表视图 -->
        <div class="knowledge-list" v-if="viewMode === 'list'">
          <div
            v-for="item in filteredKnowledgeList"
            :key="item.id"
            class="knowledge-item"
            @click="viewDetail(item)"
          >
            <div class="item-header">
              <h4 class="item-title">
                <el-icon><document /></el-icon>
                {{ item.fileName }}
              </h4>
              <div class="item-meta">
                <el-tag :type="getCategoryTag(item.categoryId)" size="small" v-if="item.categoryName">
                  {{ item.categoryName }}
                </el-tag>
                <span class="meta-info">
                  <el-icon><user /></el-icon>{{ item.createUserName || '未知' }}
                </span>
                <span class="meta-info">
                  <el-icon><clock /></el-icon>{{ item.updateTime }}
                </span>
                <span class="meta-info">
                  <el-icon><view /></el-icon>{{ item.viewCount || 0 }}
                </span>
              </div>
            </div>
            <div class="item-content">
              <p class="item-summary">{{ item.fileDescription || '暂无描述' }}</p>
              <div class="item-tags">
                <el-tag
                  v-for="tag in item.tagList"
                  :key="tag"
                  size="small"
                  type="info"
                  class="content-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
            <div class="item-actions">
              <el-button size="small" type="text">
                <el-icon><view /></el-icon> 查看
              </el-button>
<!--              <el-button size="small" type="text" @click.stop="toggleFavorite(item)">-->
<!--                <el-icon><star /></el-icon> {{ item.favorited ? '已收藏' : '收藏' }}-->
<!--              </el-button>-->
              <el-button size="small" type="text" @click.stop="downloadAttachment(item)" v-if="item.hasAttachment">
                <el-icon><Download /></el-icon> 下载附件
              </el-button>
              <el-button size="small" type="text" @click.stop="handleDeleteDocument(item)" v-if="hasPermission" class="delete-btn">
                <el-icon><delete /></el-icon> 删除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 知识内容列表 - 卡片视图 -->
        <div class="knowledge-grid" v-if="viewMode === 'grid'">
          <el-row :gutter="20">
            <el-col
              :span="8"
              v-for="item in filteredKnowledgeList"
              :key="item.id"
              style="margin-bottom: 20px;"
            >
              <el-card class="knowledge-card" @click="viewDetail(item)">
                <div class="card-header">
                  <h5 class="card-title">{{ item.fileName }}</h5>
                  <el-tag :type="getCategoryTag(item.categoryId)" size="small" v-if="item.categoryName">
                    {{ item.categoryName }}
                  </el-tag>
                </div>
                <div class="card-content">
                  <p class="card-summary">{{ item.fileDescription || '暂无描述' }}</p>
                  <div class="card-tags">
                    <el-tag
                      v-for="tag in (item.tagList || []).slice(0, 3)"
                      :key="tag"
                      size="small"
                      type="info"
                    >
                      {{ tag }}
                    </el-tag>
                    <span v-if="(item.tagList || []).length > 3" class="more-tags">...</span>
                  </div>
                </div>
                <div class="card-footer">
                  <div class="card-meta">
                    <span><el-icon><user /></el-icon>{{ item.createUserName || '未知' }}</span>
                    <span><el-icon><view /></el-icon>{{ item.viewCount || 0 }}</span>
                  </div>
                  <div class="card-actions">
<!--                    <el-button size="small" type="text" @click.stop="toggleFavorite(item)">-->
<!--                      <el-icon><star /></el-icon>-->
<!--                    </el-button>-->
                    <el-button size="small" type="text" @click.stop="downloadAttachment(item)" v-if="item.hasAttachment">
                      <el-icon><download /></el-icon>
                    </el-button>
                    <el-button size="small" type="text" @click.stop="handleDeleteDocument(item)" v-if="hasPermission" class="delete-btn">
                      <el-icon><delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            :page-size="pageSize"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
    </el-row>

    <!-- 知识详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="currentKnowledge?.fileName"
      width="80%"
      destroy-on-close
    >
      <div class="knowledge-detail" v-if="currentKnowledge">
        <!-- 详情头部信息 -->
        <div class="detail-header">
          <div class="detail-meta">
            <el-tag :type="getCategoryTag(currentKnowledge.categoryId)" v-if="currentKnowledge.categoryName">
              {{ currentKnowledge.categoryName }}
            </el-tag>
            <span class="meta-item">
              <el-icon><user /></el-icon>作者：{{ currentKnowledge.createUserName || '未知' }}
            </span>
            <span class="meta-item">
              <el-icon><clock /></el-icon>更新时间：{{ currentKnowledge.updateTime }}
            </span>
            <span class="meta-item">
              <el-icon><view /></el-icon>浏览：{{ currentKnowledge.viewCount || 0 }}次
            </span>
          </div>
          <div class="detail-actions">
<!--            <el-button @click="toggleFavorite(currentKnowledge)">-->
<!--              <el-icon><star /></el-icon>-->
<!--              {{ currentKnowledge.favorited ? '取消收藏' : '收藏' }}-->
<!--            </el-button>-->
<!--            <el-button @click="shareKnowledge">-->
<!--              <el-icon><share /></el-icon> 分享-->
<!--            </el-button>-->
            <el-button @click="downloadAttachment(currentKnowledge)" v-if="currentKnowledge.hasAttachment">
              <el-icon><download /></el-icon> 下载文件
            </el-button>
            <el-button @click="handleDeleteDocument(currentKnowledge)" v-if="hasPermission" type="danger">
              <el-icon><delete /></el-icon> 删除
            </el-button>
          </div>
        </div>

        <!-- 详情内容 -->
        <div class="detail-content">
          <div class="content-html">
            <p><strong>文件说明：</strong></p>
            <p>{{ currentKnowledge.fileDescription || '暂无说明' }}</p>
            <p><strong>文件信息：</strong></p>
            <p>文件名：{{ currentKnowledge.fileName }}</p>
            <p v-if="currentKnowledge.fileSizeFormat">文件大小：{{ currentKnowledge.fileSizeFormat }}</p>
            <p v-if="currentKnowledge.fileType">文件类型：{{ currentKnowledge.fileType }}</p>
          </div>
        </div>

        <!-- 标签 -->
        <div class="detail-tags" v-if="currentKnowledge.tagList && currentKnowledge.tagList.length">
          <h5>相关标签</h5>
          <el-tag
            v-for="tag in currentKnowledge.tagList"
            :key="tag"
            type="primary"
            @click="searchByTag(tag)"
            style="margin-right: 8px; cursor: pointer;"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>
    </el-dialog>

    <!-- 新增文档对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增文件"
      width="800px"
      destroy-on-close
    >
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="100px">
        <el-form-item label="文件上传" prop="file">
          <el-upload
            class="upload-demo"
            drag
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
            accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.md"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、Word、Excel、文本等格式文件
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="addForm.fileName" placeholder="请输入文件名称" />
        </el-form-item>

        <el-form-item label="文件说明">
          <el-input
            v-model="addForm.fileDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入文件说明"
          />
        </el-form-item>

        <el-form-item label="所属分类">
          <el-select v-model="addForm.categoryId" placeholder="请选择分类" clearable>
            <el-option
              v-for="category in flattenCategories(categoryTree)"
              :key="category.id"
              :label="category.fullPath || category.categoryName"
              :value="category.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="文件标签">
          <el-select
            v-model="addForm.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
          >
            <el-option
              v-for="tag in hotTags"
              :key="tag.name"
              :label="tag.name"
              :value="tag.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="同步到Dify">
          <el-switch v-model="addForm.syncDify" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancelAdd">取消</el-button>
          <el-button type="primary" @click="handleSubmitAdd">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分类管理对话框 -->
    <el-dialog
      v-model="categoryDialogVisible"
      :title="categoryDialogTitle"
      width="500px"
      destroy-on-close
    >
      <el-form :model="categoryForm" :rules="categoryFormRules" ref="categoryFormRef" label-width="100px">
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="categoryForm.categoryName" placeholder="请输入分类名称" />
        </el-form-item>

        <el-form-item label="分类描述">
          <el-input
            v-model="categoryForm.categoryDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>

        <el-form-item label="父级分类" v-if="categoryForm.parentId">
          <el-input v-model="parentCategoryName" disabled />
        </el-form-item>

        <el-form-item label="排序">
          <el-input-number v-model="categoryForm.sort" :min="1" :max="999" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancelCategory">取消</el-button>
          <el-button type="primary" @click="handleSubmitCategory">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import { Search, Plus, Folder, Document, User, Clock, View, Star, Download, Share, Paperclip, UploadFilled, Edit, Delete } from '@element-plus/icons-vue'
import {
  getCategoryTree,
  getDocumentsByCategory,
  getDocumentsByKnowledge,
  searchDocuments,
  downloadDocument,
  incrementViewCount,
  uploadDocument,
  addCategory,
  updateCategory,
  removeCategory,
  getCategoryDetail,
  removeDocument
} from '@/api/finance/knowledge'

export default {
  name: 'FinanceKnowledge',
  components: {
    Search, Plus, Folder, Document, User, Clock, View, Star, Download, Share, Paperclip, UploadFilled, Edit, Delete
  },
  data() {
    return {
      searchKeyword: '',
      currentCategory: null,
      currentNodeKey: 'root', // 当前选中的节点key，默认为根节点
      selectedTags: [],
      sortType: 'latest',
      viewMode: 'list',
      detailDialogVisible: false,
      addDialogVisible: false,
      currentKnowledge: null,
      pageSize: 10,
      total: 0,
      hasPermission: true, // 是否有新增权限
      loading: false,
      knowledgeId: 1, // 默认知识库ID
      
      // 树形结构配置
      treeProps: {
        children: 'children',
        label: 'categoryName'
      },

      // 知识分类树
      categoryTree: [],
      
      // 热门标签
      hotTags: [
        { name: '企业所得税', count: 45 },
        { name: '增值税', count: 38 },
        { name: '报销', count: 32 },
        { name: '预算', count: 28 },
        { name: '审计', count: 25 },
        { name: '内控', count: 22 },
        { name: '税收优惠', count: 20 },
        { name: '财务分析', count: 18 }
      ],
      
      // 知识列表数据
      knowledgeList: [],

      // 新增文档表单
      addForm: {
        knowledgeId: 1,
        categoryId: null,
        fileName: '',
        fileDescription: '',
        tags: [],
        file: null,
        syncDify: true
      },

      // 表单验证规则
      addFormRules: {
        fileName: [
          { required: true, message: '请输入文件名称', trigger: 'blur' }
        ],
        file: [
          { required: true, message: '请选择文件', trigger: 'change' }
        ]
      },

      // 右键菜单相关
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      currentNode: null,
      currentNodeData: null,

      // 分类管理对话框
      categoryDialogVisible: false,
      categoryDialogTitle: '',
      categoryForm: {
        id: null,
        knowledgeId: 1,
        parentId: null,
        categoryName: '',
        categoryDescription: '',
        sort: 1
      },
      parentCategoryName: '',

      // 分类表单验证规则
      categoryFormRules: {
        categoryName: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 1, max: 50, message: '分类名称长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    filteredKnowledgeList() {
      let result = [...this.knowledgeList]

      // 标签筛选
      if (this.selectedTags.length) {
        result = result.filter(item =>
          this.selectedTags.some(tag => item.tagList && item.tagList.includes(tag))
        )
      }

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        result = result.filter(item =>
          item.fileName.toLowerCase().includes(keyword) ||
          (item.fileDescription && item.fileDescription.toLowerCase().includes(keyword)) ||
          (item.tagList && item.tagList.some(tag => tag.toLowerCase().includes(keyword)))
        )
      }

      // 排序
      switch (this.sortType) {
        case 'latest':
          result.sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime))
          break
        case 'views':
          result.sort((a, b) => b.viewCount - a.viewCount)
          break
        case 'title':
          result.sort((a, b) => a.fileName.localeCompare(b.fileName))
          break
      }

      this.total = result.length
      return result
    },

    // 是否可以添加子分类（限制2级）
    canAddChild() {
      if (!this.currentNodeData) return true // 右键空白处可以添加根分类
      // 根节点"知识库"可以添加子分类，只有根分类（level=1）可以添加子分类，二级分类不能再添加子分类
      if (this.currentNodeData.id === 'root') return true
      return this.currentNodeData.level === 1
    },

    // 是否可以删除分类
    canDelete() {
      if (!this.currentNodeData) return false
      // 根节点"知识库"不能删除
      if (this.currentNodeData.id === 'root') return false
      // 如果有子分类或有文档，不能删除
      return (!this.currentNodeData.children || this.currentNodeData.children.length === 0) &&
             (this.currentNodeData.count === 0)
    }
  },
  mounted() {
    this.loadCategoryTree()
    this.loadDocuments()
    // 监听全局点击事件，隐藏右键菜单
    document.addEventListener('click', this.hideContextMenu)
  },

  beforeUnmount() {
    // 移除全局点击事件监听
    document.removeEventListener('click', this.hideContextMenu)
  },
  methods: {
    handleCategoryClick(data, node, component) {
      // 这里只响应点击事件，不会导致展开/收起
      console.info('Node clicked:')
      console.info(node);
      console.info(data);
      // 隐藏鼠标右键菜单
      this.hideContextMenu();

      // 如果点击的是根节点"知识库"，则清空当前分类以显示所有文档
      if (data.id === 'root') {
        this.currentCategory = null
        this.currentNodeKey = 'root'
      } else {
        this.currentCategory = data
        this.currentNodeKey = data.id
      }

      // 执行你的文档加载逻辑
      this.loadDocuments();
    },
    // 加载分类树
    async loadCategoryTree() {
      try {
        const res = await getCategoryTree(this.knowledgeId)
        if (res.data.success) {
          this.categoryTree = this.buildCategoryTree(res.data.data)
        }
      } catch (error) {
        console.error('加载分类树失败:', error)
        this.$message.error('加载分类树失败')
      }
    },

    // 构建分类树（添加count字段和level字段）
    buildCategoryTree(categories) {
      // 递归构建并计算每个节点的总文档数和层级
      const processNode = (node, level = 1) => {
        let totalCount = node.documentCount || 0; // 当前节点自身的文档数

        // 如果有子节点，先递归处理子节点
        if (node.children && node.children.length > 0) {
          const processedChildren = node.children.map(child => processNode(child, level + 1));
          // 将子节点的 count 累加到当前节点
          const childrenCount = processedChildren.reduce((sum, child) => sum + child.count, 0);
          totalCount += childrenCount;

          // 用处理后的子节点替换原 children
          node.children = processedChildren;
        }

        return {
          ...node,
          count: totalCount,
          label: node.categoryName,
          level: level,
          children: node.children || []
        };
      };

      // 对每个根节点执行处理
      const processedCategories = categories.map(category => processNode(category));

      // 计算所有文档的总数
      const totalDocumentCount = processedCategories.reduce((sum, category) => sum + category.count, 0);

      // 创建根节点"知识库"
      const rootNode = {
        id: 'root',
        categoryName: '知识库',
        label: '知识库',
        count: totalDocumentCount,
        level: 0,
        children: processedCategories,
        isRoot: true
      };

      // 返回包含根节点的树结构
      return [rootNode];
    },

    // 加载文档列表
    async loadDocuments() {
      try {
        this.loading = true
        let res
        if (this.currentCategory) {
          res = await getDocumentsByCategory(this.currentCategory.id)
        } else {
          res = await getDocumentsByKnowledge(this.knowledgeId)
        }

        if (res.data.success) {
          this.knowledgeList = res.data.data.map(doc => ({
            ...doc,
            title: doc.fileName,
            summary: doc.fileDescription || '暂无描述',
            author: doc.createUserName || '未知',
            updateTime: doc.updateTime,
            views: doc.viewCount || 0,
            favorited: false,
            hasAttachment: doc.hasAttachment || false,
            tags: doc.tagList || [],
            attachments: []
          }))
        }
      } catch (error) {
        console.error('加载文档列表失败:', error)
        this.$message.error('加载文档列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.loadDocuments()
        return
      }

      try {
        this.loading = true
        const res = await searchDocuments({
          keyword: this.searchKeyword,
          knowledgeId: this.knowledgeId,
          categoryId: this.currentCategory?.id,
          tags: this.selectedTags
        })

        if (res.data.success) {
          this.knowledgeList = res.data.data.map(doc => ({
            ...doc,
            title: doc.fileName,
            summary: doc.fileDescription || '暂无描述',
            author: doc.createUserName || '未知',
            updateTime: doc.updateTime,
            views: doc.viewCount || 0,
            favorited: false,
            hasAttachment: doc.hasAttachment || false,
            tags: doc.tagList || [],
            attachments: []
          }))
        }
      } catch (error) {
        console.error('搜索失败:', error)
        this.$message.error('搜索失败')
      } finally {
        this.loading = false
      }
    },

    // 标签操作
    toggleTag(tagName) {
      const index = this.selectedTags.indexOf(tagName)
      if (index > -1) {
        this.selectedTags.splice(index, 1)
      } else {
        this.selectedTags.push(tagName)
      }
      this.handleSearch()
    },

    removeTag(tagName) {
      const index = this.selectedTags.indexOf(tagName)
      if (index > -1) {
        this.selectedTags.splice(index, 1)
      }
      this.handleSearch()
    },

    searchByTag(tagName) {
      this.selectedTags = [tagName]
      this.detailDialogVisible = false
      this.handleSearch()
    },
    
    // 查看详情
    async viewDetail(item) {
      this.currentKnowledge = item
      this.detailDialogVisible = true
      // 增加浏览次数
      try {
        await incrementViewCount(item.id)
        item.views++
      } catch (error) {
        console.error('增加浏览次数失败:', error)
      }
    },

    // 收藏切换
    toggleFavorite(item) {
      item.favorited = !item.favorited
      this.$message.success(item.favorited ? '收藏成功' : '取消收藏成功')
    },

    // 下载附件
    async downloadAttachment(item) {
      try {
        const res = await downloadDocument(item.id)
        // 创建下载链接
        const blob = new Blob([res.data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = item.fileName || '文件'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('下载成功')
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败')
      }
    },

    downloadFile(file) {
      this.downloadAttachment(file)
    },

    // 删除文档
    handleDeleteDocument(item) {
      this.$confirm(`确定要删除文档"${item.fileName}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await removeDocument(item.id)
          if (res.data.success) {
            this.$message.success('删除成功')
            // 如果是在详情对话框中删除，关闭对话框
            if (this.detailDialogVisible && this.currentKnowledge && this.currentKnowledge.id === item.id) {
              this.detailDialogVisible = false
            }
            // 重新加载文档列表和分类树
            this.loadDocuments()
            this.loadCategoryTree() // 重新加载分类树以更新文档数量
          } else {
            this.$message.error(res.data.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除文档失败:', error)
          this.$message.error('删除文档失败')
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 分享
    shareKnowledge() {
      this.$message.success('分享链接已复制到剪贴板')
    },

    // 新增知识
    handleAdd() {
      this.addForm = {
        knowledgeId: this.knowledgeId,
        categoryId: this.currentCategory?.id || null,
        fileName: '',
        fileDescription: '',
        tags: [],
        file: null,
        syncDify: true
      }
      this.addDialogVisible = true
    },
    
    // 文件上传相关
    handleFileChange(file) {
      this.addForm.file = file.raw
      if (!this.addForm.fileName) {
        this.addForm.fileName = file.name
      }
    },

    // 提交新增文档
    async handleSubmitAdd() {
      try {
        // 验证表单
        if (!this.addForm.file) {
          this.$message.error('请选择文件')
          return
        }
        if (!this.addForm.fileName) {
          this.$message.error('请输入文件名称')
          return
        }

        // 构建FormData
        const formData = new FormData()
        formData.append('file', this.addForm.file)
        formData.append('knowledgeId', this.addForm.knowledgeId)
        if (this.addForm.categoryId) {
          formData.append('categoryId', this.addForm.categoryId)
        }
        formData.append('fileName', this.addForm.fileName)
        if (this.addForm.fileDescription) {
          formData.append('fileDescription', this.addForm.fileDescription)
        }
        if (this.addForm.tags && this.addForm.tags.length > 0) {
          this.addForm.tags.forEach(tag => {
            formData.append('tags', tag)
          })
        }
        formData.append('syncDify', this.addForm.syncDify)

        // 上传文件
        const res = await uploadDocument(formData)
        if (res.data.success) {
          this.$message.success('文档上传成功')
          this.addDialogVisible = false
          this.loadDocuments()
          this.loadCategoryTree() // 重新加载分类树以更新文档数量
        } else {
          this.$message.error(res.data.msg || '上传失败')
        }
      } catch (error) {
        console.error('上传文档失败:', error)
        this.$message.error('上传文档失败')
      }
    },

    // 取消新增
    handleCancelAdd() {
      this.addDialogVisible = false
      this.addForm = {
        knowledgeId: this.knowledgeId,
        categoryId: null,
        fileName: '',
        fileDescription: '',
        tags: [],
        file: null,
        syncDify: true
      }
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
    },

    handleCurrentChange(val) {
      console.log('当前页:', val)
    },
    
    // 扁平化分类树
    flattenCategories(categories, result = []) {
      categories.forEach(category => {
        result.push(category)
        if (category.children && category.children.length > 0) {
          this.flattenCategories(category.children, result)
        }
      })
      return result
    },

    // 右键菜单相关方法
    handleNodeContextmenu(event, data, node) {
      event.preventDefault()
      event.stopPropagation()

      this.currentNode = node
      this.currentNodeData = data
      this.contextMenuX = event.clientX
      this.contextMenuY = event.clientY
      this.contextMenuVisible = true
    },

    // 处理树空白区域右键
    handleTreeContextmenu(event) {
      // 检查是否点击在节点上
      const target = event.target
      if (target.closest('.el-tree-node')) {
        return // 如果点击在节点上，不处理
      }

      event.preventDefault()
      event.stopPropagation()

      this.currentNode = null
      this.currentNodeData = null
      this.contextMenuX = event.clientX
      this.contextMenuY = event.clientY
      this.contextMenuVisible = true
    },

    hideContextMenu() {
      this.contextMenuVisible = false
      // this.currentNode = null
      // this.currentNodeData = null
    },

    // 新增分类
    handleAddCategory() {
      this.hideContextMenu()
      this.categoryDialogTitle = '新增分类'

      // 如果当前节点是根节点，则parentId为null
      let parentId = null
      let parentCategoryName = '根分类'

      if (this.currentNodeData && this.currentNodeData.id !== 'root') {
        parentId = this.currentNodeData.id
        parentCategoryName = this.currentNodeData.categoryName
      }

      this.categoryForm = {
        id: null,
        knowledgeId: this.knowledgeId,
        parentId: parentId,
        categoryName: '',
        categoryDescription: '',
        sort: 1
      }
      this.parentCategoryName = parentCategoryName
      this.categoryDialogVisible = true
    },

    // 编辑分类
    async handleEditCategory() {
      this.hideContextMenu()
      if (!this.currentNodeData) return

      // 根节点"知识库"不能编辑
      if (this.currentNodeData.id === 'root') {
        this.$message.warning('根节点不能编辑')
        return
      }

      try {
        // 获取分类详情
        const res = await getCategoryDetail(this.currentNodeData.id)
        if (res.data.success) {
          this.categoryDialogTitle = '编辑分类'
          this.categoryForm = {
            id: res.data.data.id,
            knowledgeId: res.data.data.knowledgeId,
            parentId: res.data.data.parentId,
            categoryName: res.data.data.categoryName,
            categoryDescription: res.data.data.categoryDescription || '',
            sort: res.data.data.sort || 1
          }

          // 设置父分类名称
          if (this.categoryForm.parentId) {
            const parentNode = this.findNodeById(this.categoryTree, this.categoryForm.parentId)
            this.parentCategoryName = parentNode ? parentNode.categoryName : '未知'
          } else {
            this.parentCategoryName = '根分类'
          }

          this.categoryDialogVisible = true
        }
      } catch (error) {
        console.error('获取分类详情失败:', error)
        this.$message.error('获取分类详情失败')
      }
    },

    // 删除分类
    handleDeleteCategory() {
      this.hideContextMenu()
      if (!this.currentNodeData) return

      // 根节点"知识库"不能删除
      if (this.currentNodeData.id === 'root') {
        this.$message.warning('根节点不能删除')
        return
      }

      this.$confirm(`确定要删除分类"${this.currentNodeData.categoryName}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await removeCategory(this.currentNodeData.id)
          if (res.data.success) {
            this.$message.success('删除成功')
            this.loadCategoryTree()
            this.loadDocuments()
          } else {
            this.$message.error(res.data.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除分类失败:', error)
          this.$message.error('删除分类失败')
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 提交分类表单
    async handleSubmitCategory() {
      try {
        // 验证表单
        await this.$refs.categoryFormRef.validate()

        let res
        if (this.categoryForm.id) {
          // 编辑
          res = await updateCategory(this.categoryForm)
        } else {
          // 新增
          res = await addCategory(this.categoryForm)
        }

        if (res.data.success) {
          this.$message.success(this.categoryForm.id ? '编辑成功' : '新增成功')
          this.categoryDialogVisible = false
          this.loadCategoryTree()
          this.loadDocuments()
        } else {
          this.$message.error(res.data.msg || '操作失败')
        }
      } catch (error) {
        if (error !== false) { // 不是表单验证失败
          console.error('提交分类失败:', error)
          this.$message.error('操作失败')
        }
      }
    },

    // 取消分类操作
    handleCancelCategory() {
      this.categoryDialogVisible = false
      this.categoryForm = {
        id: null,
        knowledgeId: this.knowledgeId,
        parentId: null,
        categoryName: '',
        categoryDescription: '',
        sort: 1
      }
      this.parentCategoryName = ''
    },

    // 根据ID查找节点
    findNodeById(nodes, id) {
      for (let node of nodes) {
        if (node.id === id) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, id)
          if (found) return found
        }
      }
      return null
    },

    // 工具方法
    getCategoryTag(category) {
      const categoryMap = {
        11: 'primary', 12: 'success', 13: 'warning',
        21: 'danger', 22: 'info', 23: 'primary', 24: 'success',
        31: 'warning', 32: 'danger', 33: 'info',
        41: 'primary', 42: 'success',
        51: 'warning', 52: 'danger', 53: 'info'
      }
      return categoryMap[category] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  .search-section {
    display: flex;
    align-items: center;
  }
}

.category-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  height: fit-content;
  
  .panel-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    
    h4 {
      margin: 0;
      color: #303133;
    }
  }
  
  .category-tree {
    padding: 15px;
    
    .category-node {
      display: flex;
      align-items: center;
      width: 100%;
      
      .node-label {
        margin-left: 8px;
        flex: 1;
      }
      
      .node-count {
        color: #909399;
        font-size: 12px;
      }
    }
  }
  
  .hot-tags {
    padding: 15px;
    border-top: 1px solid #ebeef5;
    
    h5 {
      margin: 0 0 10px 0;
      color: #303133;
    }
    
    .tag-list {
      .tag-item {
        margin: 5px 5px 5px 0;
        cursor: pointer;
      }
    }
  }
}

.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  margin-bottom: 20px;
  
  .filter-right {
    display: flex;
    align-items: center;
  }
}

.selected-tags {
  padding: 10px 20px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 15px;
  
  span:first-child {
    color: #606266;
    margin-right: 10px;
  }
}

.knowledge-list {
  .knowledge-item {
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      border-color: #409eff;
    }
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 10px;
      
      .item-title {
        margin: 0;
        color: #303133;
        font-size: 16px;
        display: flex;
        align-items: center;
        
        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }
      
      .item-meta {
        display: flex;
        align-items: center;
        gap: 15px;
        
        .meta-info {
          color: #909399;
          font-size: 12px;
          display: flex;
          align-items: center;
          
          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }
    
    .item-content {
      margin-bottom: 15px;
      
      .item-summary {
        margin: 0 0 10px 0;
        color: #606266;
        line-height: 1.6;
      }
      
      .item-tags {
        .content-tag {
          margin-right: 8px;
        }
      }
    }
    
    .item-actions {
      display: flex;
      gap: 10px;

      .delete-btn {
        color: #f56c6c;

        &:hover {
          color: #f56c6c;
          background-color: #fef0f0;
        }
      }
    }
  }
}

.knowledge-grid {
  .knowledge-card {
    cursor: pointer;
    height: 280px;
    transition: all 0.3s;
    border: 1px solid #ebeef5 !important;
    
    &:hover {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 10px;
      
      .card-title {
        margin: 0;
        color: #303133;
        font-size: 14px;
        line-height: 1.4;
        flex: 1;
        margin-right: 10px;
      }
    }
    
    .card-content {
      margin-bottom: 15px;
      
      .card-summary {
        margin: 0 0 10px 0;
        color: #606266;
        font-size: 12px;
        line-height: 1.5;
        height: 60px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      
      .card-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        
        .more-tags {
          color: #909399;
          font-size: 12px;
        }
      }
    }
    
    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-meta {
        display: flex;
        gap: 10px;
        
        span {
          color: #909399;
          font-size: 12px;
          display: flex;
          align-items: center;
          
          .el-icon {
            margin-right: 4px;
          }
        }
      }
      
      .card-actions {
        display: flex;
        gap: 5px;

        .delete-btn {
          color: #f56c6c;

          &:hover {
            color: #f56c6c;
            background-color: #fef0f0;
          }
        }
      }
    }
  }
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.knowledge-detail {
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 20px;
    
    .detail-meta {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .meta-item {
        color: #606266;
        font-size: 14px;
        display: flex;
        align-items: center;
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
    
    .detail-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .detail-content {
    margin-bottom: 30px;
    
    .content-html {
      line-height: 1.8;
      color: #303133;
      
      h3 {
        color: #303133;
        margin-bottom: 15px;
      }
      
      p {
        margin-bottom: 15px;
      }
    }
  }
  
  .detail-attachments {
    margin-bottom: 30px;
    
    h5 {
      margin: 0 0 15px 0;
      color: #303133;
    }
    
    .attachment-list {
      .attachment-item {
        display: flex;
        align-items: center;
        padding: 10px;
        background: #f5f7fa;
        border-radius: 4px;
        margin-bottom: 10px;
        
        .file-name {
          flex: 1;
          margin-left: 8px;
          color: #303133;
        }
        
        .file-size {
          color: #909399;
          margin-right: 15px;
        }
      }
    }
  }
  
  .detail-tags {
    h5 {
      margin: 0 0 15px 0;
      color: #303133;
    }
  }
}

// 右键菜单样式
.context-menu {
  position: fixed;
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  min-width: 120px;
  padding: 4px 0;

  .menu-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f7fa;
      color: #409eff;
    }

    &.danger {
      color: #f56c6c;

      &:hover {
        background-color: #fef0f0;
        color: #f56c6c;
      }
    }

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}
</style>
