<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.FinanceKnowledgeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="financeKnowledgeResultMap" type="org.springblade.modules.yjzb.pojo.entity.FinanceKnowledgeEntity">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="dataset_id" property="datasetId"/>
        <result column="status" property="status"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, tenant_id, name, description, dataset_id, status, sort, remark, 
        is_deleted, create_user, create_dept, create_time, update_user, update_time
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectFinanceKnowledgePage" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceKnowledgeVO">
        SELECT 
            fk.id,
            fk.tenant_id,
            fk.name,
            fk.description,
            fk.dataset_id,
            fk.status,
            fk.sort,
            fk.remark,
            fk.create_user,
            fk.create_dept,
            fk.create_time,
            fk.update_user,
            fk.update_time,
            cu.real_name AS createUserName,
            uu.real_name AS updateUserName,
            (SELECT COUNT(*) FROM yjzb_finance_category fc WHERE fc.knowledge_id = fk.id AND fc.is_deleted = 0) AS categoryCount,
            (SELECT COUNT(*) FROM yjzb_finance_document fd WHERE fd.knowledge_id = fk.id AND fd.is_deleted = 0) AS documentCount
        FROM yjzb_finance_knowledge fk
        LEFT JOIN blade_user cu ON fk.create_user = cu.id
        LEFT JOIN blade_user uu ON fk.update_user = uu.id
        WHERE fk.is_deleted = 0
        <if test="financeKnowledge.name != null and financeKnowledge.name != ''">
            AND fk.name LIKE CONCAT('%', #{financeKnowledge.name}, '%')
        </if>
        <if test="financeKnowledge.status != null">
            AND fk.status = #{financeKnowledge.status}
        </if>
        <if test="financeKnowledge.datasetId != null and financeKnowledge.datasetId != ''">
            AND fk.dataset_id = #{financeKnowledge.datasetId}
        </if>
        ORDER BY fk.sort ASC, fk.create_time DESC
    </select>

    <!-- 根据ID查询详情 -->
    <select id="selectFinanceKnowledgeById" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceKnowledgeVO">
        SELECT 
            fk.id,
            fk.tenant_id,
            fk.name,
            fk.description,
            fk.dataset_id,
            fk.status,
            fk.sort,
            fk.remark,
            fk.create_user,
            fk.create_dept,
            fk.create_time,
            fk.update_user,
            fk.update_time,
            cu.real_name AS createUserName,
            uu.real_name AS updateUserName,
            (SELECT COUNT(*) FROM yjzb_finance_category fc WHERE fc.knowledge_id = fk.id AND fc.is_deleted = 0) AS categoryCount,
            (SELECT COUNT(*) FROM yjzb_finance_document fd WHERE fd.knowledge_id = fk.id AND fd.is_deleted = 0) AS documentCount
        FROM yjzb_finance_knowledge fk
        LEFT JOIN blade_user cu ON fk.create_user = cu.id
        LEFT JOIN blade_user uu ON fk.update_user = uu.id
        WHERE fk.id = #{id} AND fk.is_deleted = 0
    </select>

</mapper>
