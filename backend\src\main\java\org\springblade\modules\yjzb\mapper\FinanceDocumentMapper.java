/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.yjzb.pojo.entity.FinanceDocumentEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceDocumentVO;
import java.util.List;

/**
 * 知识库文件 Mapper 接口
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
public interface FinanceDocumentMapper extends BaseMapper<FinanceDocumentEntity> {

    /**
     * 自定义分页查询
     *
     * @param page 分页对象
     * @param financeDocument 查询条件
     * @return 分页结果
     */
    IPage<FinanceDocumentVO> selectFinanceDocumentPage(IPage<FinanceDocumentVO> page, @Param("financeDocument") FinanceDocumentVO financeDocument);

    /**
     * 根据ID查询详情
     *
     * @param id 主键ID
     * @return 详情信息
     */
    FinanceDocumentVO selectFinanceDocumentById(@Param("id") Long id);

    /**
     * 根据分类ID查询文档列表
     *
     * @param categoryId 分类ID
     * @return 文档列表
     */
    List<FinanceDocumentVO> selectDocumentsByCategory(@Param("categoryId") Long categoryId);

    /**
     * 根据知识库ID查询文档列表
     *
     * @param knowledgeId 知识库ID
     * @return 文档列表
     */
    List<FinanceDocumentVO> selectDocumentsByKnowledge(@Param("knowledgeId") Long knowledgeId);

    /**
     * 搜索文档
     *
     * @param keyword 关键词
     * @param knowledgeId 知识库ID
     * @param categoryId 分类ID
     * @param tags 标签列表
     * @return 文档列表
     */
    List<FinanceDocumentVO> searchDocuments(@Param("keyword") String keyword, 
                                           @Param("knowledgeId") Long knowledgeId,
                                           @Param("categoryId") Long categoryId,
                                           @Param("tags") List<String> tags);

    /**
     * 增加浏览次数
     *
     * @param id 文档ID
     */
    void incrementViewCount(@Param("id") Long id);

    /**
     * 增加下载次数
     *
     * @param id 文档ID
     */
    void incrementDownloadCount(@Param("id") Long id);
}
