/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import java.io.Serial;

/**
 * 卷烟销量 实体类
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Data
@TableName("yjzb_cigarette_sales")
@Schema(description = "CigaretteSales对象")
@EqualsAndHashCode(callSuper = true)
public class CigaretteSalesEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 年份
     */
    @Schema(description = "数据期间（格式：YYYY-MM）")
    private String period;

    /**
     * 销量（箱）
     */
    @Schema(description = "销量（箱）")
    private Integer sales;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

}