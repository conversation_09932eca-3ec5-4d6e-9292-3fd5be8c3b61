package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.yjzb.entity.ExpenseForecast;

import java.math.BigDecimal;

/**
 * 办公费用预测服务接口
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
public interface ExpenseForecastService extends IService<ExpenseForecast> {

    /**
     * 执行费用预测
     *
     * @param indicatorId    指标ID
     * @param forecastPeriod 预测期间
     * @param forecastType   预测类型
     * @param inputData      输入数据
     * @return 预测结果
     */
    ExpenseForecast executeForecast(Long indicatorId, String forecastPeriod, String forecastType, String inputData);

    /**
     * 获取预测结果
     *
     * @param indicatorId    指标ID
     * @param forecastPeriod 预测期间
     * @param forecastType   预测类型
     * @return 预测结果
     */
    ExpenseForecast getForecastResult(Long indicatorId, String forecastPeriod, String forecastType);

    /**
     * 检查是否有缓存的预测结果
     *
     * @param indicatorId    指标ID
     * @param forecastPeriod 预测期间
     * @param forecastType   预测类型
     * @return 是否有缓存
     */
    boolean hasCachedForecast(Long indicatorId, String forecastPeriod, String forecastType);
}
