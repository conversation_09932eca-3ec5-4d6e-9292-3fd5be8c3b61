FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装必要的依赖
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
RUN pip install --no-cache-dir \
    jupyter \
    notebook \
    pandas \
    numpy \
    matplotlib \
    psycopg2-binary \
    requests \
    python-dotenv

# 创建工作目录
RUN mkdir -p /app/notebooks

# 设置工作目录
WORKDIR /app/notebooks

# 暴露Jupyter端口
EXPOSE 8888

# 启动Jupyter
CMD ["jupyter", "notebook", "--ip=0.0.0.0", "--port=8888", "--no-browser", "--allow-root", "--NotebookApp.token=''", "--NotebookApp.password=''"]