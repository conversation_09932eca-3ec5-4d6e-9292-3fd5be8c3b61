<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.FinanceDocumentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="financeDocumentResultMap" type="org.springblade.modules.yjzb.pojo.entity.FinanceDocumentEntity">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="knowledge_id" property="knowledgeId"/>
        <result column="category_id" property="categoryId"/>
        <result column="document_id" property="documentId"/>
        <result column="file_name" property="fileName"/>
        <result column="file_original_name" property="fileOriginalName"/>
        <result column="file_path" property="filePath"/>
        <result column="file_size" property="fileSize"/>
        <result column="file_type" property="fileType"/>
        <result column="file_extension" property="fileExtension"/>
        <result column="file_description" property="fileDescription"/>
        <result column="file_tags" property="fileTags"/>
        <result column="view_count" property="viewCount"/>
        <result column="download_count" property="downloadCount"/>
        <result column="upload_status" property="uploadStatus"/>
        <result column="sync_status" property="syncStatus"/>
        <result column="sync_error_msg" property="syncErrorMsg"/>
        <result column="status" property="status"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, tenant_id, knowledge_id, category_id, document_id, file_name, file_original_name,
        file_path, file_size, file_type, file_extension, file_description, file_tags,
        view_count, download_count, upload_status, sync_status, sync_error_msg, status, sort,
        remark, is_deleted, create_user, create_dept, create_time, update_user, update_time
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectFinanceDocumentPage" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceDocumentVO">
        SELECT 
            fd.id,
            fd.tenant_id,
            fd.knowledge_id,
            fd.category_id,
            fd.document_id,
            fd.file_name,
            fd.file_original_name,
            fd.file_path,
            fd.file_size,
            fd.file_type,
            fd.file_extension,
            fd.file_description,
            fd.file_tags,
            fd.view_count,
            fd.download_count,
            fd.upload_status,
            fd.sync_status,
            fd.sync_error_msg,
            fd.status,
            fd.sort,
            fd.remark,
            fd.create_user,
            fd.create_dept,
            fd.create_time,
            fd.update_user,
            fd.update_time,
            fk.name AS knowledgeName,
            fc.category_name AS categoryName,
            fc.full_path AS categoryPath,
            cu.real_name AS createUserName,
            uu.real_name AS updateUserName,
            CASE WHEN fd.file_path IS NOT NULL AND fd.file_path != '' THEN true ELSE false END AS hasAttachment
        FROM yjzb_finance_document fd
        LEFT JOIN yjzb_finance_knowledge fk ON fd.knowledge_id = fk.id
        LEFT JOIN yjzb_finance_category fc ON fd.category_id = fc.id
        LEFT JOIN blade_user cu ON fd.create_user = cu.id
        LEFT JOIN blade_user uu ON fd.update_user = uu.id
        WHERE fd.is_deleted = 0
        <if test="financeDocument.knowledgeId != null">
            AND fd.knowledge_id = #{financeDocument.knowledgeId}
        </if>
        <if test="financeDocument.categoryId != null">
            AND fd.category_id = #{financeDocument.categoryId}
        </if>
        <if test="financeDocument.fileName != null and financeDocument.fileName != ''">
            AND fd.file_name LIKE CONCAT('%', #{financeDocument.fileName}, '%')
        </if>
        <if test="financeDocument.fileType != null and financeDocument.fileType != ''">
            AND fd.file_type = #{financeDocument.fileType}
        </if>
        <if test="financeDocument.status != null">
            AND fd.status = #{financeDocument.status}
        </if>
        <if test="financeDocument.syncStatus != null">
            AND fd.sync_status = #{financeDocument.syncStatus}
        </if>
        ORDER BY fd.sort ASC, fd.create_time DESC
    </select>

    <!-- 根据ID查询详情 -->
    <select id="selectFinanceDocumentById" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceDocumentVO">
        SELECT 
            fd.*,
            fk.name AS knowledgeName,
            fc.category_name AS categoryName,
            fc.full_path AS categoryPath,
            cu.real_name AS createUserName,
            uu.real_name AS updateUserName,
            (CASE WHEN fd.file_path IS NOT NULL AND fd.file_path != '' THEN true ELSE false END) AS hasAttachment
        FROM yjzb_finance_document fd
        LEFT JOIN yjzb_finance_knowledge fk ON fd.knowledge_id = fk.id
        LEFT JOIN yjzb_finance_category fc ON fd.category_id = fc.id
        LEFT JOIN blade_user cu ON fd.create_user = cu.id
        LEFT JOIN blade_user uu ON fd.update_user = uu.id
        WHERE fd.id = #{id} AND fd.is_deleted = 0
    </select>


    <!-- 根据分类ID查询文档列表 -->
    <select id="selectDocumentsByCategory" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceDocumentVO">
        SELECT
            fd.*,
            COALESCE(fc.category_name, '未分类') AS categoryName,
            cu.real_name AS createUserName,
            (CASE WHEN fd.file_path IS NOT NULL AND fd.file_path != '' THEN true ELSE false END) AS hasAttachment
        FROM yjzb_finance_document fd
        LEFT JOIN yjzb_finance_category fc ON fd.category_id = fc.id AND fc.is_deleted = 0
        LEFT JOIN blade_user cu ON fd.create_user = cu.id
        WHERE
            fd.category_id IN (
                -- 查询目标分类及其所有子分类ID
                SELECT id FROM yjzb_finance_category
                WHERE id = #{categoryId}
                OR parent_id = #{categoryId}
            )
        AND fd.is_deleted = 0
        AND fd.status = 1
        ORDER BY fd.sort ASC, fd.create_time DESC
    </select>

    <select id="selectDocumentsByKnowledge" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceDocumentVO">
        SELECT 
            fd.*,
            COALESCE(fc.category_name, '未分类') AS categoryName,
            cu.real_name AS createUserName,
            (CASE WHEN fd.file_path IS NOT NULL AND fd.file_path != '' THEN true ELSE false END) AS hasAttachment
        FROM yjzb_finance_document fd
        LEFT JOIN yjzb_finance_category fc ON fd.category_id = fc.id AND fc.is_deleted = 0
        LEFT JOIN blade_user cu ON fd.create_user = cu.id
        WHERE fd.knowledge_id = #{knowledgeId} 
          AND fd.is_deleted = 0 
          AND fd.status = 1
        ORDER BY fd.sort ASC, fd.create_time DESC
    </select>

    <select id="searchDocuments" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceDocumentVO">
        SELECT 
            fd.*,
            COALESCE(fc.category_name, '未分类') AS categoryName,
            COALESCE(fc.full_path, '/未分类') AS categoryPath,
            cu.real_name AS createUserName,
            (CASE WHEN fd.file_path IS NOT NULL AND fd.file_path != '' THEN true ELSE false END) AS hasAttachment
        FROM yjzb_finance_document fd
        LEFT JOIN yjzb_finance_category fc ON fd.category_id = fc.id AND fc.is_deleted = 0
        LEFT JOIN blade_user cu ON fd.create_user = cu.id
        WHERE 
            fd.is_deleted = 0 
            AND fd.status = 1
            
        <!-- 按知识库过滤 -->
        <if test="knowledgeId != null">
            AND fd.knowledge_id = #{knowledgeId}
        </if>

        <!-- 按分类ID过滤（包含该分类及其子分类下的文档） -->
        <if test="categoryId != null">
            AND fd.category_id IN (
                SELECT id 
                FROM yjzb_finance_category 
                WHERE id = #{categoryId} 
                   OR parent_id = #{categoryId}
                   AND is_deleted = 0 
                   AND status = 1
            )
        </if>

        <!-- 关键词搜索 -->
        <if test="keyword != null and keyword != ''">
            AND (
                fd.file_name LIKE CONCAT('%', #{keyword}, '%') 
                OR fd.file_description LIKE CONCAT('%', #{keyword}, '%')
                OR fd.file_tags LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>

        <!-- 标签搜索 -->
        <if test="tags != null and tags.size() > 0">
            AND (
            <foreach collection="tags" item="tag" separator=" OR ">
                fd.file_tags LIKE CONCAT('%', #{tag}, '%')
            </foreach>
            )
        </if>

        ORDER BY fd.view_count DESC, fd.create_time DESC
    </select>

    <!-- 增加浏览次数 -->
    <update id="incrementViewCount">
        UPDATE yjzb_finance_document 
        SET view_count = view_count + 1, update_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 增加下载次数 -->
    <update id="incrementDownloadCount">
        UPDATE yjzb_finance_document 
        SET download_count = download_count + 1, update_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

</mapper>
