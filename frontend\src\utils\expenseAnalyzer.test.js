/**
 * 办公费用分析工具测试文件
 * 用于验证分析功能的正确性
 */

import { ExpenseAnalyzer } from './expenseAnalyzer.js';

// 创建测试实例
const analyzer = new ExpenseAnalyzer();

// 模拟测试数据
const mockFeatureData = [
  {
    年月: '2024-01',
    二级分类: '办公用品费',
    月度总金额: 45230.80,
    月度平均金额: 15076.93,
    月度交易次数: 3,
    年: 2024,
    月: 1,
    季度: 1,
    较上月增长率: 0.12,
    较去年同期增长率: 0.15,
    年度预算: 377215.93,
    当年累计金额: 45230.80,
    预算完成率: 0.12,
    预算偏差: -0.042
  },
  {
    年月: '2024-02',
    二级分类: '办公用品费',
    月度总金额: 38450.25,
    月度平均金额: 12816.75,
    月度交易次数: 3,
    年: 2024,
    月: 2,
    季度: 1,
    较上月增长率: -0.15,
    较去年同期增长率: 0.08,
    年度预算: 377215.93,
    当年累计金额: 83680.05,
    预算完成率: 0.22,
    预算偏差: -0.058
  },
  {
    年月: '2024-03',
    二级分类: '其他杂项',
    月度总金额: 266486.96,
    月度平均金额: 38069.57,
    月度交易次数: 7,
    年: 2024,
    月: 3,
    季度: 1,
    较上月增长率: 6.47,
    较去年同期增长率: 8.87,
    年度预算: 788594.05,
    当年累计金额: 266486.96,
    预算完成率: 0.34,
    预算偏差: 0.222
  },
  {
    年月: '2024-04',
    二级分类: '其他杂项',
    月度总金额: 161782.08,
    月度平均金额: 32356.42,
    月度交易次数: 5,
    年: 2024,
    月: 4,
    季度: 2,
    较上月增长率: -0.39,
    较去年同期增长率: 4.25,
    年度预算: 788594.05,
    当年累计金额: 428269.04,
    预算完成率: 0.54,
    预算偏差: 0.125
  }
];

// 测试函数
async function runTests() {
  console.log('开始测试办公费用分析工具...\n');

  try {
    // 设置测试数据
    analyzer.featureData = mockFeatureData;
    
    // 测试1: 异常检测
    console.log('=== 测试1: 异常检测 ===');
    const anomalies = analyzer.detectAnomalies();
    console.log(`检测到 ${anomalies.length} 个异常项目:`);
    anomalies.forEach((anomaly, index) => {
      console.log(`${index + 1}. ${anomaly.category} (${anomaly.date}): ${anomaly.reason}`);
      console.log(`   严重程度: ${anomaly.severity}, 金额: ¥${analyzer.formatNumber(anomaly.amount)}`);
    });
    console.log('');

    // 测试2: 预算分析
    console.log('=== 测试2: 预算执行分析 ===');
    const budgetAnalysis = analyzer.analyzeBudgetExecution();
    console.log(`整体预算执行率: ${budgetAnalysis.overallSummary.overallRate}%`);
    console.log('各类别预算执行情况:');
    budgetAnalysis.categoryAnalysis.forEach(category => {
      console.log(`- ${category.category}: ${category.rate}% (风险等级: ${category.riskLevel})`);
    });
    console.log('');

    // 测试3: 趋势分析
    console.log('=== 测试3: 趋势分析 ===');
    const trendAnalysis = analyzer.analyzeTrends();
    console.log(`历史趋势数据点: ${trendAnalysis.historicalTrends.length}`);
    console.log('季节性模式:');
    Object.entries(trendAnalysis.seasonalPatterns.quarterly).forEach(([quarter, amount]) => {
      console.log(`- ${quarter}: ¥${analyzer.formatNumber(amount)}`);
    });
    console.log(`趋势洞察: ${trendAnalysis.insights.length} 条`);
    trendAnalysis.insights.forEach((insight, index) => {
      console.log(`${index + 1}. [${insight.level}] ${insight.title}: ${insight.message}`);
    });
    console.log('');

    // 测试4: 预测分析
    console.log('=== 测试4: 预测分析 ===');
    const forecastAnalysis = analyzer.generateForecast(6);
    console.log('未来6个月预测:');
    forecastAnalysis.totalForecast.forEach(forecast => {
      console.log(`- ${forecast.period}: ¥${analyzer.formatNumber(forecast.amount)} (置信度: ${forecast.confidence}%)`);
    });
    console.log(`预算建议: ${forecastAnalysis.budgetRecommendations.length} 条`);
    forecastAnalysis.budgetRecommendations.forEach((rec, index) => {
      console.log(`${index + 1}. [${rec.priority}] ${rec.title}: ${rec.description}`);
    });
    console.log('');

    // 测试5: 工具方法
    console.log('=== 测试5: 工具方法 ===');
    const testValues = [100, 200, 150, 300, 250];
    const mean = analyzer.calculateMean(testValues);
    const stdDev = analyzer.calculateStandardDeviation(testValues, mean);
    console.log(`测试数据 [${testValues.join(', ')}]:`);
    console.log(`- 平均值: ${mean}`);
    console.log(`- 标准差: ${stdDev.toFixed(2)}`);
    
    const slope = analyzer.calculateTrendSlope(testValues);
    const correlation = analyzer.calculateTrendCorrelation(testValues);
    console.log(`- 趋势斜率: ${slope.toFixed(2)}`);
    console.log(`- 相关性: ${correlation.toFixed(2)}`);
    console.log('');

    console.log('✅ 所有测试完成！分析工具运行正常。');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error(error.stack);
  }
}

// 导出测试函数
export { runTests };

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.runExpenseAnalyzerTests = runTests;
  console.log('测试函数已挂载到 window.runExpenseAnalyzerTests，可在控制台调用');
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境
  runTests();
}
