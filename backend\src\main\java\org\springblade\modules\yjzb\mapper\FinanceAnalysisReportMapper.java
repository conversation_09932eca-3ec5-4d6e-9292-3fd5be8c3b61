/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisReportEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisReportVO;

import java.util.Map;

/**
 * 财务分析报告列表Mapper接口
 *
 * <AUTHOR> Assistant
 */
@Mapper
public interface FinanceAnalysisReportMapper extends BaseMapper<FinanceAnalysisReportEntity> {

    /**
     * 分页查询财务分析报告列表
     *
     * @param page 分页参数
     * @param report 查询条件
     * @return 分页数据
     */
    IPage<FinanceAnalysisReportVO> selectReportPage(IPage<FinanceAnalysisReportVO> page, @Param("report") FinanceAnalysisReportVO report);

    /**
     * 获取报告统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getReportStatistics();

    /**
     * 根据ID查询详情
     *
     * @param id 报告ID
     * @return 报告详情
     */
    FinanceAnalysisReportVO selectReportDetail(@Param("id") Long id);

    /**
     * 更新下载次数
     *
     * @param id 报告ID
     * @return 更新结果
     */
    int updateDownloadCount(@Param("id") Long id);

    /**
     * 根据状态统计报告数量
     *
     * @param status 状态
     * @return 数量
     */
    int countByStatus(@Param("status") String status);

    /**
     * 获取本年完成的报告数量
     *
     * @param year 年份
     * @return 数量
     */
    int countCompletedByYear(@Param("year") Integer year);
}
