worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server {
        listen       80;
        server_name  10.78.81.8;

        location / {
            root   /app/dist;
            index  index.html;
            try_files $uri /index.html;  # 支持前端路由
        }


        location /api/ {
            client_max_body_size 100M;
            if ($request_uri ~* /api/(.*)$) {
                set $new $1;
                proxy_pass http://10.78.81.8:8088/$new;
            }
        }

        location /blade-auth/ {
            client_max_body_size 100M;
            if ($request_uri ~* /blade-auth/(.*)$) {
                set $new $1;
                proxy_pass http://10.78.81.8:8088/blade-auth/$new;
            }
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /app/dist;
        }
    }
}
