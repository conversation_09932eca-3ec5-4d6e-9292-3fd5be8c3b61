/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.service.impl;

import org.springblade.modules.yjzb.pojo.entity.IndicatorEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorVO;
import org.springblade.modules.yjzb.excel.IndicatorExcel;
import org.springblade.modules.yjzb.mapper.IndicatorMapper;
import org.springblade.modules.yjzb.service.IIndicatorService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 指标 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class IndicatorServiceImpl extends BaseServiceImpl<IndicatorMapper, IndicatorEntity> implements IIndicatorService {

	@Override
	public IPage<IndicatorVO> selectIndicatorPage(IPage<IndicatorVO> page, IndicatorVO indicator) {
		return page.setRecords(baseMapper.selectIndicatorPage(page, indicator));
	}


	@Override
	public List<IndicatorExcel> exportIndicator(Wrapper<IndicatorEntity> queryWrapper) {
		List<IndicatorExcel> indicatorList = baseMapper.exportIndicator(queryWrapper);
		//indicatorList.forEach(indicator -> {
		//	indicator.setTypeName(DictCache.getValue(DictEnum.YES_NO, Indicator.getType()));
		//});
		return indicatorList;
	}

}
