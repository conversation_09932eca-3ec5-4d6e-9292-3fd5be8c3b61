/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.modules.yjzb.pojo.entity.FinanceDocumentEntity;
import java.util.List;

/**
 * 知识库文件 视图对象
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "知识库文件视图对象")
public class FinanceDocumentVO extends FinanceDocumentEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称")
    private String knowledgeName;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String categoryName;

    /**
     * 分类完整路径
     */
    @Schema(description = "分类完整路径")
    private String categoryPath;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createUserName;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名")
    private String updateUserName;

    /**
     * 文件标签列表
     */
    @Schema(description = "文件标签列表")
    private List<String> tagList;

    /**
     * 文件大小格式化
     */
    @Schema(description = "文件大小格式化")
    private String fileSizeFormat;

    /**
     * 上传状态描述
     */
    @Schema(description = "上传状态描述")
    private String uploadStatusDesc;

    /**
     * 同步状态描述
     */
    @Schema(description = "同步状态描述")
    private String syncStatusDesc;

    /**
     * 是否有附件
     */
    @Schema(description = "是否有附件")
    private Boolean hasAttachment;

    /**
     * 下载链接
     */
    @Schema(description = "下载链接")
    private String downloadUrl;
}
