package org.springblade.modules.yjzb.pojo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;

/**
 * 财务分析数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinanceAnalysisDTO extends FinanceAnalysisEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 查询条件：开始时间
     */
    private String beginTime;

    /**
     * 查询条件：结束时间
     */
    private String endTime;
}