import request from '@/axios';

// 获取指标数据明细列表
export const getDetailList = (current, size, params) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

// 根据指标ID和期间查询明细
export const getDetailByIndicatorAndPeriod = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/by-indicator-period',
    method: 'get',
    params: {
      indicatorId,
      period
    }
  })
}

// 根据指标ID和期间区间查询明细
export const getDetailByIndicatorAndPeriodRange = (indicatorId, startPeriod, endPeriod) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/by-indicator-period-range',
    method: 'get',
    params: {
      indicatorId,
      startPeriod,
      endPeriod
    }
  })
}

// 根据凭证号查询明细
export const getDetailByVoucherNo = (voucherNo) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/by-voucher-no',
    method: 'get',
    params: {
      voucherNo
    }
  })
}

// 统计指定指标和期间的总金额
export const getSumAmount = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/sum-amount',
    method: 'get',
    params: {
      indicatorId,
      period
    }
  })
}

// 按分类统计金额
export const getSumByCategory = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/sum-by-category',
    method: 'get',
    params: {
      indicatorId,
      period
    }
  })
}

// 新增明细
export const addDetail = (row) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/save',
    method: 'post',
    data: row
  })
}

// 更新明细
export const updateDetail = (row) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/update',
    method: 'post',
    data: row
  })
}

// 删除明细
export const removeDetail = (ids) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

// 批量删除明细
export const removeDetailBatch = (ids) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/remove-batch',
    method: 'post',
    data: ids
  })
}

// Excel导入明细
export const importDetailExcel = (formData) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 费用明细数据导入
export const importFeeDetailData = (formData) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/import-fee-detail',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 费用明细数据预览
export const previewFeeDetailData = (formData) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/preview-fee-detail',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// Excel导出明细
export const exportDetailExcel = (params) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
