/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.modules.yjzb.mapper.IndicatorValuesDetailMapper;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesDetailEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO;
import org.springblade.modules.yjzb.pojo.dto.IndicatorValuesDetailDTO;
import org.springblade.modules.yjzb.service.IIndicatorValuesDetailService;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;
import java.util.List;
import java.util.Date;
import java.math.BigDecimal;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Arrays;
import org.springframework.web.multipart.MultipartFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import java.io.IOException;
import java.io.InputStream;

/**
 * 指标数据明细 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
@AllArgsConstructor
public class IndicatorValuesDetailServiceImpl extends
        ServiceImpl<IndicatorValuesDetailMapper, IndicatorValuesDetailEntity> implements IIndicatorValuesDetailService {

    private final IndicatorValuesDetailMapper indicatorValuesDetailMapper;

    @Override
    public IPage<IndicatorValuesDetailVO> selectIndicatorValuesDetailPage(IPage<IndicatorValuesDetailVO> page,
            IndicatorValuesDetailEntity indicatorValuesDetail) {
        return indicatorValuesDetailMapper.selectIndicatorValuesDetailPage(page, indicatorValuesDetail);
    }

    @Override
    public List<IndicatorValuesDetailVO> selectByIndicatorIdAndPeriod(Long indicatorId, String period) {
        return indicatorValuesDetailMapper.selectByIndicatorIdAndPeriod(indicatorId, period);
    }

    @Override
    public List<IndicatorValuesDetailVO> selectByIndicatorIdAndPeriodRange(Long indicatorId, String startPeriod,
            String endPeriod) {
        return indicatorValuesDetailMapper.selectByIndicatorIdAndPeriodRange(indicatorId, startPeriod, endPeriod);
    }

    @Override
    public BigDecimal sumAmountByIndicatorIdAndPeriod(Long indicatorId, String period) {
        return indicatorValuesDetailMapper.sumAmountByIndicatorIdAndPeriod(indicatorId, period);
    }

    @Override
    public BigDecimal sumAmountByIndicatorIdAndPeriodRange(Long indicatorId, String startPeriod, String endPeriod) {
        return indicatorValuesDetailMapper.sumAmountByIndicatorIdAndPeriodRange(indicatorId, startPeriod, endPeriod);
    }

    @Override
    public List<IndicatorValuesDetailVO> sumAmountByCategory(Long indicatorId, String period) {
        return indicatorValuesDetailMapper.sumAmountByCategory(indicatorId, period);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsert(List<IndicatorValuesDetailEntity> list) {
        if (Func.isEmpty(list)) {
            return false;
        }

        // 设置创建信息
        BladeUser user = AuthUtil.getUser();
        Date now = new Date();
        for (IndicatorValuesDetailEntity entity : list) {
            if (Func.isEmpty(entity.getId())) {
                entity.setCreateUser(user.getUserId());
                entity.setCreateTime(now);
                entity.setStatus(1);
                entity.setIsDeleted(0);
            }
        }

        return indicatorValuesDetailMapper.batchInsert(list) > 0;
    }

    @Override
    public List<IndicatorValuesDetailVO> selectByVoucherNo(String voucherNo) {
        return indicatorValuesDetailMapper.selectByVoucherNo(voucherNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveIndicatorValuesDetail(IndicatorValuesDetailDTO indicatorValuesDetailDTO) {
        IndicatorValuesDetailEntity entity = BeanUtil.copy(indicatorValuesDetailDTO, IndicatorValuesDetailEntity.class);

        // 设置创建信息
        BladeUser user = AuthUtil.getUser();
        Date now = new Date();
        entity.setCreateUser(user.getUserId());
        entity.setCreateTime(now);
        entity.setStatus(1);
        entity.setIsDeleted(0);

        return save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateIndicatorValuesDetail(IndicatorValuesDetailDTO indicatorValuesDetailDTO) {
        if (Func.isEmpty(indicatorValuesDetailDTO.getId())) {
            throw new RuntimeException("更新时ID不能为空");
        }

        IndicatorValuesDetailEntity entity = BeanUtil.copy(indicatorValuesDetailDTO, IndicatorValuesDetailEntity.class);

        // 设置更新信息
        BladeUser user = AuthUtil.getUser();
        Date now = new Date();
        entity.setUpdateUser(user.getUserId());
        entity.setUpdateTime(now);

        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIndicatorValuesDetail(Long id) {
        if (Func.isEmpty(id)) {
            return false;
        }

        IndicatorValuesDetailEntity entity = new IndicatorValuesDetailEntity();
        entity.setId(id);
        entity.setIsDeleted(1);

        // 设置更新信息
        BladeUser user = AuthUtil.getUser();
        Date now = new Date();
        entity.setUpdateUser(user.getUserId());
        entity.setUpdateTime(now);

        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIndicatorValuesDetailBatch(List<Long> ids) {
        if (Func.isEmpty(ids)) {
            return false;
        }

        // 设置更新信息
        BladeUser user = AuthUtil.getUser();
        Date now = new Date();

        List<IndicatorValuesDetailEntity> entities = listByIds(ids);
        for (IndicatorValuesDetailEntity entity : entities) {
            entity.setIsDeleted(1);
            entity.setUpdateUser(user.getUserId());
            entity.setUpdateTime(now);
        }

        return updateBatchById(entities);
    }

    @Override
    public List<Map<String, Object>> previewFeeDetailFile(MultipartFile file) {
        List<Map<String, Object>> previewData = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook;

            if (file.getOriginalFilename().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(inputStream);
            } else if (file.getOriginalFilename().endsWith(".xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                throw new RuntimeException("不支持的文件格式，请上传Excel文件(.xlsx或.xls)");
            }

            Sheet sheet = workbook.getSheetAt(0);

            // 验证表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new RuntimeException("文件格式不正确，缺少表头");
            }

            String[] expectedHeaders = { "日期", "凭证号", "摘要", "金额", "类别" };
            for (int i = 0; i < expectedHeaders.length; i++) {
                Cell cell = headerRow.getCell(i);
                if (cell == null || !cell.getStringCellValue().contains(expectedHeaders[i])) {
                    throw new RuntimeException("文件格式不正确，请确保包含：日期、凭证号、摘要、金额、类别 列");
                }
            }

            // 读取数据行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null)
                    continue;

                // 检查第一列是否为日期
                Cell dateCell = row.getCell(0);
                if (dateCell == null)
                    continue;

                String dateValue = getCellValueAsString(dateCell);
                if (dateValue == null || dateValue.trim().isEmpty())
                    continue;

                // 验证日期格式
                String period = extractPeriodFromDate(dateValue);
                if (period == null)
                    continue; // 跳过无效日期的行

                // 检查是否有其他数据
                boolean hasOtherData = false;
                for (int j = 1; j < 5; j++) {
                    Cell cell = row.getCell(j);
                    if (cell != null && !cell.toString().trim().isEmpty()) {
                        hasOtherData = true;
                        break;
                    }
                }

                if (!hasOtherData)
                    continue;

                Map<String, Object> rowData = new HashMap<>();
                rowData.put("date", dateValue);
                rowData.put("voucherNo", getCellValueAsString(row.getCell(1)));
                rowData.put("summary", getCellValueAsString(row.getCell(2)));
                rowData.put("amount", getCellValueAsNumber(row.getCell(3)));
                rowData.put("category", getCellValueAsString(row.getCell(4)));

                previewData.add(rowData);
            }

            workbook.close();

        } catch (IOException e) {
            throw new RuntimeException("读取文件失败：" + e.getMessage());
        }

        return previewData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importFeeDetailFile(Long indicatorId, MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        try {
            List<Map<String, Object>> previewData = previewFeeDetailFile(file);

            if (previewData.isEmpty()) {
                result.put("success", false);
                result.put("totalCount", 0);
                result.put("successCount", 0);
                result.put("failCount", 0);
                result.put("errors", Arrays.asList("导入数据为空"));
                return result;
            }

            // 转换为实体对象
            List<IndicatorValuesDetailEntity> entities = new ArrayList<>();
            for (Map<String, Object> item : previewData) {
                try {
                    String dateStr = Func.toStr(item.get("date"), "");
                    if (dateStr.isEmpty()) {
                        failCount++;
                        errors.add("第" + (entities.size() + 1) + "行：日期为空，跳过导入");
                        continue;
                    }

                    // 从日期提取期间
                    String period = extractPeriodFromDate(dateStr);
                    if (period == null) {
                        failCount++;
                        errors.add("第" + (entities.size() + 1) + "行：日期格式不正确，无法提取期间");
                        continue;
                    }

                    IndicatorValuesDetailEntity entity = new IndicatorValuesDetailEntity();
                    entity.setIndicatorId(indicatorId);
                    entity.setPeriod(period);
                    entity.setVoucherNo(Func.toStr(item.get("voucherNo"), ""));
                    entity.setDataSummary(Func.toStr(item.get("summary"), ""));

                    // 处理金额
                    Object amountObj = item.get("amount");
                    BigDecimal amount = BigDecimal.ZERO;
                    if (amountObj != null) {
                        if (amountObj instanceof Number) {
                            amount = new BigDecimal(amountObj.toString());
                        } else if (amountObj instanceof String) {
                            try {
                                amount = new BigDecimal(amountObj.toString().replaceAll("[^\\d.-]", ""));
                            } catch (NumberFormatException e) {
                                amount = BigDecimal.ZERO;
                            }
                        }
                    }
                    entity.setAmount(amount);

                    entity.setCategory(Func.toStr(item.get("category"), ""));
                    entity.setStatus(1);
                    entity.setIsDeleted(0);

                    entities.add(entity);
                } catch (Exception e) {
                    failCount++;
                    errors.add("数据转换失败: " + e.getMessage());
                }
            }

            // 批量插入
            if (!entities.isEmpty()) {
                boolean success = batchInsert(entities);
                if (success) {
                    successCount = entities.size();
                } else {
                    failCount += entities.size();
                    errors.add("数据库插入失败");
                }
            }

            result.put("success", successCount > 0);
            result.put("totalCount", previewData.size());
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errors", errors);

        } catch (Exception e) {
            result.put("success", false);
            result.put("totalCount", 0);
            result.put("successCount", 0);
            result.put("failCount", 1);
            result.put("errors", Arrays.asList("导入异常: " + e.getMessage()));
        }

        return result;
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null)
            return "";

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 获取单元格值作为数字
     */
    private Number getCellValueAsNumber(Cell cell) {
        if (cell == null)
            return 0;

        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case STRING:
                try {
                    return Double.parseDouble(cell.getStringCellValue().replaceAll("[^\\d.-]", ""));
                } catch (NumberFormatException e) {
                    return 0;
                }
            case FORMULA:
                try {
                    return cell.getNumericCellValue();
                } catch (Exception e) {
                    return 0;
                }
            default:
                return 0;
        }
    }

    /**
     * 从日期字符串提取期间（YYYY-MM格式）
     */
    private String extractPeriodFromDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 处理Excel日期数值
            if (dateStr.matches("\\d+(\\.\\d+)?")) {
                double excelDate = Double.parseDouble(dateStr);
                // Excel日期从1900年1月1日开始计算
                java.util.Date date = DateUtil.getJavaDate(excelDate);
                java.time.LocalDate localDate = date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                return String.format("%04d-%02d", localDate.getYear(), localDate.getMonthValue());
            }

            // 处理标准日期格式
            String[] patterns = {
                    "yyyy-MM-dd",
                    "yyyy/MM/dd",
                    "yyyy年MM月dd日",
                    "yyyy-MM-dd HH:mm:ss",
                    "yyyy/MM/dd HH:mm:ss"
            };

            for (String pattern : patterns) {
                try {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(pattern);
                    java.util.Date date = sdf.parse(dateStr);
                    java.time.LocalDate localDate = date.toInstant().atZone(java.time.ZoneId.systemDefault())
                            .toLocalDate();
                    return String.format("%04d-%02d", localDate.getYear(), localDate.getMonthValue());
                } catch (Exception e) {
                    // 继续尝试下一个格式
                }
            }

            // 如果都不匹配，返回null
            return null;

        } catch (Exception e) {
            return null;
        }
    }

}
