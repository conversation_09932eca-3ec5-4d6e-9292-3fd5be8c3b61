/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.resource.builder.OssBuilder;
import org.springblade.modules.yjzb.mapper.FinanceAnalysisReportMapper;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisReportEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisReportVO;
import org.springblade.modules.yjzb.service.IFinanceAnalysisReportService;
import org.springblade.modules.yjzb.service.IFinanceAnalysisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 财务分析报告列表服务实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceAnalysisReportServiceImpl extends ServiceImpl<FinanceAnalysisReportMapper, FinanceAnalysisReportEntity>
        implements IFinanceAnalysisReportService {

    private final FinanceAnalysisReportMapper reportMapper;
    private final IFinanceAnalysisService financeAnalysisService;
    private final OssBuilder ossBuilder;

    @Value("${oss.endpoint:http://127.0.0.1:9000}")
    private String minioEndpoint;

    @Value("${oss.access-key:}")
    private String minioAccessKey;

    @Value("${oss.secret-key:}")
    private String minioSecretKey;

    @Value("${oss.bucket-name:yjyc}")
    private String bucketName;

    @Override
    public IPage<FinanceAnalysisReportVO> selectReportPage(IPage<FinanceAnalysisReportVO> page, FinanceAnalysisReportVO report) {
        return reportMapper.selectReportPage(page, report);
    }

    @Override
    public Map<String, Object> getReportStatistics() {
        return reportMapper.getReportStatistics();
    }

    @Override
    public FinanceAnalysisReportVO getReportDetail(Long id) {
        return reportMapper.selectReportDetail(id);
    }

    @Override
    public FinanceAnalysisReportEntity createOrUpdateReport(String title, String type, Integer queryYear,
                                                            Integer compareYear, Integer startMonth, Integer endMonth) {
        // 查询是否已存在未删除的同名报告
        LambdaQueryWrapper<FinanceAnalysisReportEntity> wrapper =
                new LambdaQueryWrapper<>();
        wrapper.eq(FinanceAnalysisReportEntity::getTitle, title)
                .eq(FinanceAnalysisReportEntity::getIsDeleted, 0);
        FinanceAnalysisReportEntity existingReport = this.getOne(wrapper);

        boolean isUpdate = false;
        FinanceAnalysisReportEntity report;

        if (existingReport != null) {
            report = existingReport;
            isUpdate = true;
        } else {
            report = new FinanceAnalysisReportEntity();
            report.setTitle(title);
            report.setDownloadCount(0);
            report.setGenerateTime(new Date());
        }

        // 更新字段
        report.setType(type);
        report.setQueryYear(queryYear);
        report.setCompareYear(compareYear);
        report.setStartMonth(startMonth);
        report.setEndMonth(endMonth);
        report.setReportStatus("generating");
        report.setPeriod(String.format("%d年%d-%d月", queryYear, startMonth, endMonth));
        report.setUpdateTime(new Date());

        if (isUpdate) {
            this.updateById(report);
        } else {
            this.save(report);
        }

        return report;
    }

    @Override
    public boolean updateReportStatus(Long reportId, String status, String errorMessage) {
        FinanceAnalysisReportEntity report = this.getById(reportId);
        if (report == null) {
            return false;
        }

        report.setReportStatus(status);
        if (Func.isNotBlank(errorMessage)) {
            report.setErrorMessage(errorMessage);
        }

        if ("completed".equals(status)) {
            report.setCompleteTime(new Date());
        }

        return this.updateById(report);
    }

    @Override
    public boolean completeReport(Long reportId, String filePath, String fileName, Long fileSize) {
        FinanceAnalysisReportEntity report = this.getById(reportId);
        if (report == null) {
            return false;
        }

        report.setReportStatus("completed");
        report.setFilePath(filePath);
        report.setFileName(fileName);
        report.setFileSize(fileSize);
        report.setCompleteTime(new Date());

        return this.updateById(report);
    }

    @Override
    public boolean incrementDownloadCount(Long id) {
        return reportMapper.updateDownloadCount(id) > 0;
    }

    @Override
    public boolean deleteReports(String ids) {
        return this.removeByIds(Func.toLongList(ids));
    }

    @Override
    public Long generateAnalysisReport(String title, String type, Integer queryYear,
                                       Integer compareYear, Integer startMonth, Integer endMonth) {
        // 1. 创建报告记录
        FinanceAnalysisReportEntity report = createOrUpdateReport(title, type, queryYear, compareYear, startMonth, endMonth);

        // 2. 异步执行分析任务
        CompletableFuture.runAsync(() -> {
            try {
                executeAnalysisAndGenerateReport(report);
            } catch (Exception e) {
                log.error("生成分析报告失败: reportId={}", report.getId(), e);
                updateReportStatus(report.getId(), "failed", e.getMessage());
            }
        });

        return report.getId();
    }

    /**
     * 执行分析并生成报告
     */
    private void executeAnalysisAndGenerateReport(FinanceAnalysisReportEntity report) {
        try {
            log.info("开始执行分析报告生成: reportId={}, title={}", report.getId(), report.getTitle());

            // 执行各项分析
            executeAnalysisTasks(report);

            // 生成Word文档
            generateWordDocument(report);

            log.info("分析报告生成完成: reportId={}", report.getId());

        } catch (Exception e) {
            log.error("执行分析报告生成失败: reportId={}", report.getId(), e);
            updateReportStatus(report.getId(), "failed", e.getMessage());
            throw e;
        }
    }

    /**
     * 执行分析任务
     */
    private void executeAnalysisTasks(FinanceAnalysisReportEntity report) {
        try {
            log.info("开始执行分析任务: reportId={}, title={}", report.getId(), report.getTitle());

            // 执行四项分析任务
            List<FinanceAnalysisEntity> analysisResults = executeAllAnalysis(report);

            // 等待所有分析完成
            waitForAnalysisCompletion(analysisResults);

            log.info("分析任务执行完成: reportId={}", report.getId());

        } catch (Exception e) {
            log.error("执行分析任务失败: reportId={}", report.getId(), e);
            throw new RuntimeException("执行分析任务失败", e);
        }
    }

    /**
     * 执行所有分析
     */
    private List<FinanceAnalysisEntity> executeAllAnalysis(FinanceAnalysisReportEntity report) throws Exception {
        List<FinanceAnalysisEntity> results = new java.util.ArrayList<>();

        try {
            // 1. 实现税利情况分析+卷烟经营情况分析
            log.info("执行实现税利情况分析: reportId={}", report.getId());
            String workflowRunId1 = financeAnalysisService.analyzeMainEconomicIndicators(
                    report.getQueryYear(), report.getCompareYear(),
                    report.getStartMonth(), report.getEndMonth(), report.getId());
            if (workflowRunId1 != null) {
                String[] workflowRunIds1 = workflowRunId1.split(",");
                for (String id : workflowRunIds1) {
                    FinanceAnalysisEntity entity1 = financeAnalysisService.getOneByWorkflowRunId(id);
                    if (entity1 != null) {
                        results.add(entity1);
                    }
                }

            }

            // 2. 三项费用支出总体情况分析
            log.info("执行三项费用支出总体情况分析: reportId={}", report.getId());
            String workflowRunId3 = financeAnalysisService.analyzeThreeExpenses(
                    report.getQueryYear(), report.getCompareYear(),
                    report.getStartMonth(), report.getEndMonth(), report.getId());
            if (workflowRunId3 != null) {
                FinanceAnalysisEntity entity3 = financeAnalysisService.getOneByWorkflowRunId(workflowRunId3);
                if (entity3 != null) {
                    results.add(entity3);
                }
            }

            // 3. 重点费用支出情况分析
            log.info("执行重点费用支出情况分析: reportId={}", report.getId());
            String workflowRunId4 = financeAnalysisService.analyzeKeyExpenses(
                    report.getQueryYear(), report.getCompareYear(),
                    report.getStartMonth(), report.getEndMonth(), report.getId());
            if (workflowRunId4 != null) {
                FinanceAnalysisEntity entity4 = financeAnalysisService.getOneByWorkflowRunId(workflowRunId4);
                if (entity4 != null) {
                    results.add(entity4);
                }
            }

        } catch (Exception e) {
            log.error("执行分析失败: reportId={}", report.getId(), e);
            throw e;
        }

        return results;
    }

    /**
     * 等待分析完成
     */
    private void waitForAnalysisCompletion(List<FinanceAnalysisEntity> analysisResults) {
        int maxWaitTime = 300; // 最大等待5分钟
        int waitInterval = 10; // 每10秒检查一次
        int waitedTime = 0;

        while (waitedTime < maxWaitTime) {
            boolean allCompleted = true;

            for (FinanceAnalysisEntity entity : analysisResults) {
                FinanceAnalysisEntity current = financeAnalysisService.getById(entity.getId());
                if (current == null || !"COMPLETED".equals(current.getExecuteStatus())) {
                    allCompleted = false;
                    break;
                }
            }

            if (allCompleted) {
                log.info("所有分析任务已完成");
                return;
            }

            try {
                Thread.sleep(waitInterval * 1000);
                waitedTime += waitInterval;
                log.info("等待分析完成中... 已等待{}秒", waitedTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待分析完成被中断", e);
            }
        }

        log.warn("等待分析完成超时，继续生成报告");
    }

    /**
     * 生成Word文档
     */
    private void generateWordDocument(FinanceAnalysisReportEntity report) {
        try {
            log.info("开始生成Word文档: reportId={}", report.getId());

            // 获取分析结果
            List<FinanceAnalysisEntity> analysisResults = getAnalysisResults(report);

            // 生成Word文档内容
            byte[] documentBytes = createWordDocument(report, analysisResults);

            // 上传到MinIO
            String fileName = generateFileName(report);
            String filePath = uploadToMinio(documentBytes, fileName);

            // 更新报告状态
            completeReport(report.getId(), filePath, fileName, (long) documentBytes.length);

            log.info("Word文档生成完成: reportId={}, fileName={}, size={}",
                    report.getId(), fileName, documentBytes.length);

        } catch (Exception e) {
            log.error("生成Word文档失败: reportId={}", report.getId(), e);
            throw new RuntimeException("生成Word文档失败", e);
        }
    }

    /**
     * 获取分析结果
     */
    private List<FinanceAnalysisEntity> getAnalysisResults(FinanceAnalysisReportEntity report) {
        // 根据报告的查询条件获取对应的分析结果
        return financeAnalysisService.list(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<FinanceAnalysisEntity>()
                        .eq(FinanceAnalysisEntity::getQueryYear, report.getQueryYear())
                        .eq(FinanceAnalysisEntity::getCompareYear, report.getCompareYear())
                        .eq(FinanceAnalysisEntity::getStartMonth, report.getStartMonth())
                        .eq(FinanceAnalysisEntity::getEndMonth, report.getEndMonth())
                        .eq(FinanceAnalysisEntity::getExecuteStatus, "COMPLETED")
                        .orderByDesc(FinanceAnalysisEntity::getExecuteTime)
        );
    }

    /**
     * 创建Word文档
     */
    private byte[] createWordDocument(FinanceAnalysisReportEntity report, List<FinanceAnalysisEntity> analysisResults)
            throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 添加标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(org.apache.poi.xwpf.usermodel.ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("广东烟草阳江市有限责任公司");
            titleRun.setBold(true);
            titleRun.setFontSize(16);

            XWPFParagraph subtitleParagraph = document.createParagraph();
            subtitleParagraph.setAlignment(org.apache.poi.xwpf.usermodel.ParagraphAlignment.CENTER);
            XWPFRun subtitleRun = subtitleParagraph.createRun();
            subtitleRun.setText(report.getTitle());
            subtitleRun.setBold(true);
            subtitleRun.setFontSize(14);

            // 添加空行
            document.createParagraph();

            // 添加分析内容
            for (FinanceAnalysisEntity analysis : analysisResults) {
                if (analysis.getAnswerContent() != null && !analysis.getAnswerContent().trim().isEmpty()) {
                    addAnalysisContent(document, analysis);
                }
            }

            document.write(out);
            return out.toByteArray();
        }
    }

    /**
     * 添加分析内容到文档
     */
    private void addAnalysisContent(XWPFDocument document, FinanceAnalysisEntity analysis) {
        // 添加分析标题
        XWPFParagraph titleParagraph = document.createParagraph();
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(getAnalysisTitle(analysis.getName()));
        titleRun.setBold(true);
        titleRun.setFontSize(12);

        // 添加分析内容
        String content = analysis.getAnswerContent();
        if (content != null && !content.trim().isEmpty()) {
            // 按段落分割内容
            String[] paragraphs = content.split("\n\n");
            for (String paragraph : paragraphs) {
                if (!paragraph.trim().isEmpty()) {
                    XWPFParagraph contentParagraph = document.createParagraph();
                    XWPFRun contentRun = contentParagraph.createRun();
                    contentRun.setText(paragraph.trim());
                    contentRun.setFontSize(10);
                }
            }
        }

        // 添加空行
        document.createParagraph();
    }

    /**
     * 获取分析标题
     */
    private String getAnalysisTitle(String analysisName) {
        if (analysisName == null) {
            return "分析报告";
        }

        if (analysisName.contains("税利")) {
            return "一、实现税利情况分析";
        } else if (analysisName.contains("卷烟")) {
            return "二、卷烟经营情况分析";
        } else if (analysisName.contains("三项费用")) {
            return "三、三项费用支出总体情况分析";
        } else if (analysisName.contains("重点费用")) {
            return "四、重点费用支出情况分析";
        } else {
            return analysisName;
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(FinanceAnalysisReportEntity report) {
        return String.format("%d年%d-%d月分析.docx",
                report.getQueryYear(), report.getStartMonth(), report.getEndMonth());
    }

    /**
     * 上传到MinIO
     */
    private String uploadToMinio(byte[] documentBytes, String fileName) {
        try {
            // 使用OssBuilder上传文件
            String objectName = "finance/reports/" + System.currentTimeMillis() + "_" + fileName;

            ByteArrayInputStream inputStream = new ByteArrayInputStream(documentBytes);
            ossBuilder.template().putFile(objectName, inputStream);

            log.info("文件上传到MinIO成功: objectName={}, size={}", objectName, documentBytes.length);
            return objectName;

        } catch (Exception e) {
            log.error("上传文件到MinIO失败: fileName={}", fileName, e);
            throw new RuntimeException("上传文件失败", e);
        }
    }
}
