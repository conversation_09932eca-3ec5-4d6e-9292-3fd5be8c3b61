/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesDetailEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO;
import org.springblade.modules.yjzb.service.IIndicatorTypesService;
import org.springblade.modules.yjzb.pojo.entity.IndicatorTypesEntity;
import org.springblade.core.tool.utils.Func;
import lombok.AllArgsConstructor;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 指标数据明细 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
public class IndicatorValuesDetailWrapper
        extends BaseEntityWrapper<IndicatorValuesDetailEntity, IndicatorValuesDetailVO> {

    private final IIndicatorTypesService indicatorTypesService;

    public IndicatorValuesDetailWrapper(IIndicatorTypesService indicatorTypesService) {
        this.indicatorTypesService = indicatorTypesService;
    }

    public static IndicatorValuesDetailWrapper build(IIndicatorTypesService indicatorTypesService) {
        return new IndicatorValuesDetailWrapper(indicatorTypesService);
    }

    @Override
    public IndicatorValuesDetailVO entityVO(IndicatorValuesDetailEntity indicatorValuesDetail) {
        IndicatorValuesDetailVO indicatorValuesDetailVO = BeanUtil.copy(indicatorValuesDetail,
                IndicatorValuesDetailVO.class);

        // 设置指标类型名称
        if (Func.isNotEmpty(indicatorValuesDetail.getIndicatorId())) {
            IndicatorTypesEntity indicatorType = indicatorTypesService.getById(indicatorValuesDetail.getIndicatorId());
            if (indicatorType != null) {
                indicatorValuesDetailVO.setIndicatorName(indicatorType.getTypeName());
            }
        }

        // 设置状态名称
        if (Func.isNotEmpty(indicatorValuesDetail.getStatus())) {
            indicatorValuesDetailVO.setStatusName(indicatorValuesDetail.getStatus() == 1 ? "正常" : "禁用");
        }

        return indicatorValuesDetailVO;
    }

    public List<IndicatorValuesDetailVO> entityVOList(List<IndicatorValuesDetailEntity> list) {
        return list.stream().map(this::entityVO).collect(Collectors.toList());
    }

}
