/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.yjzb.pojo.entity.FinanceKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.dto.FinanceKnowledgeDTO;
import org.springblade.modules.yjzb.pojo.vo.FinanceKnowledgeVO;

/**
 * 财务知识库 服务类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
public interface IFinanceKnowledgeService extends IService<FinanceKnowledgeEntity> {

    /**
     * 自定义分页查询
     *
     * @param page 分页对象
     * @param financeKnowledge 查询条件
     * @return 分页结果
     */
    IPage<FinanceKnowledgeVO> selectFinanceKnowledgePage(IPage<FinanceKnowledgeVO> page, FinanceKnowledgeVO financeKnowledge);

    /**
     * 根据ID查询详情
     *
     * @param id 主键ID
     * @return 详情信息
     */
    FinanceKnowledgeVO getFinanceKnowledgeById(Long id);

    /**
     * 新增知识库
     *
     * @param financeKnowledgeDTO 知识库信息
     * @return 是否成功
     */
    boolean saveFinanceKnowledge(FinanceKnowledgeDTO financeKnowledgeDTO);

    /**
     * 更新知识库
     *
     * @param financeKnowledgeDTO 知识库信息
     * @return 是否成功
     */
    boolean updateFinanceKnowledge(FinanceKnowledgeDTO financeKnowledgeDTO);

    /**
     * 删除知识库
     *
     * @param ids 主键ID列表
     * @return 是否成功
     */
    boolean deleteFinanceKnowledge(String ids);

    /**
     * 同步知识库到Dify
     *
     * @param id 知识库ID
     * @return 是否成功
     */
    boolean syncToDify(Long id);
}
