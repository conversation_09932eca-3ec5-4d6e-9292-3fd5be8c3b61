import request from '@/axios';

// 财务知识库API

/**
 * 获取知识库列表
 */
export const getKnowledgeList = (current, size, params) => {
  return request({
    url: '/yjzb/finance-knowledge/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

/**
 * 获取知识库详情
 */
export const getKnowledgeDetail = (id) => {
  return request({
    url: '/yjzb/finance-knowledge/detail',
    method: 'get',
    params: {
      id,
    },
  });
};

/**
 * 新增知识库
 */
export const addKnowledge = (data) => {
  return request({
    url: '/yjzb/finance-knowledge/save',
    method: 'post',
    data,
  });
};

/**
 * 更新知识库
 */
export const updateKnowledge = (data) => {
  return request({
    url: '/yjzb/finance-knowledge/update',
    method: 'post',
    data,
  });
};

/**
 * 删除知识库
 */
export const removeKnowledge = (ids) => {
  return request({
    url: '/yjzb/finance-knowledge/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

/**
 * 同步知识库到Dify
 */
export const syncKnowledgeToDify = (id) => {
  return request({
    url: `/yjzb/finance-knowledge/sync-dify/${id}`,
    method: 'post',
  });
};

// 知识分类API

/**
 * 获取分类树形结构
 */
export const getCategoryTree = (knowledgeId) => {
  return request({
    url: '/yjzb/finance-category/tree',
    method: 'get',
    params: {
      knowledgeId,
    },
  });
};

/**
 * 获取分类列表
 */
export const getCategoryList = (current, size, params) => {
  return request({
    url: '/yjzb/finance-category/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

/**
 * 获取分类详情
 */
export const getCategoryDetail = (id) => {
  return request({
    url: '/yjzb/finance-category/detail',
    method: 'get',
    params: {
      id,
    },
  });
};

/**
 * 新增分类
 */
export const addCategory = (data) => {
  return request({
    url: '/yjzb/finance-category/save',
    method: 'post',
    data,
  });
};

/**
 * 更新分类
 */
export const updateCategory = (data) => {
  return request({
    url: '/yjzb/finance-category/update',
    method: 'post',
    data,
  });
};

/**
 * 删除分类
 */
export const removeCategory = (ids) => {
  return request({
    url: '/yjzb/finance-category/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

/**
 * 获取子分类列表
 */
export const getCategoryChildren = (parentId) => {
  return request({
    url: '/yjzb/finance-category/children',
    method: 'get',
    params: {
      parentId,
    },
  });
};

/**
 * 统计分类下的文档数量
 */
export const countCategoryDocuments = (categoryId) => {
  return request({
    url: '/yjzb/finance-category/count-documents',
    method: 'get',
    params: {
      categoryId,
    },
  });
};

// 知识库文档API

/**
 * 获取文档列表
 */
export const getDocumentList = (current, size, params) => {
  return request({
    url: '/yjzb/finance-document/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

/**
 * 根据分类获取文档列表
 */
export const getDocumentsByCategory = (categoryId) => {
  return request({
    url: '/yjzb/finance-document/by-category',
    method: 'get',
    params: {
      categoryId,
    },
  });
};

/**
 * 根据知识库获取文档列表
 */
export const getDocumentsByKnowledge = (knowledgeId) => {
  return request({
    url: '/yjzb/finance-document/by-knowledge',
    method: 'get',
    params: {
      knowledgeId,
    },
  });
};

/**
 * 搜索文档
 */
export const searchDocuments = (params) => {
  return request({
    url: '/yjzb/finance-document/search',
    method: 'get',
    params,
  });
};

/**
 * 获取文档详情
 */
export const getDocumentDetail = (id) => {
  return request({
    url: '/yjzb/finance-document/detail',
    method: 'get',
    params: {
      id,
    },
  });
};

/**
 * 上传文档
 */
export const uploadDocument = (formData) => {
  return request({
    url: '/yjzb/finance-document/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 更新文档
 */
export const updateDocument = (data) => {
  return request({
    url: '/yjzb/finance-document/update',
    method: 'post',
    data,
  });
};

/**
 * 删除文档
 */
export const removeDocument = (ids) => {
  return request({
    url: '/yjzb/finance-document/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

/**
 * 下载文档
 */
export const downloadDocument = (id) => {
  return request({
    url: `/yjzb/finance-document/download/${id}`,
    method: 'get',
    responseType: 'blob',
  });
};

/**
 * 增加浏览次数
 */
export const incrementViewCount = (id) => {
  return request({
    url: `/yjzb/finance-document/view/${id}`,
    method: 'post',
  });
};

/**
 * 同步文档到Dify
 */
export const syncDocumentToDify = (id) => {
  return request({
    url: `/yjzb/finance-document/sync-dify/${id}`,
    method: 'post',
  });
};
