/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import java.io.Serial;
import java.util.Date;

/**
 * 指标AI解读分析 实体类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-10
 */
@Data
@TableName("yjzb_indicator_ai_analysis")
@Schema(description = "IndicatorAiAnalysis对象")
@EqualsAndHashCode(callSuper = true)
public class IndicatorAiAnalysisEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 指标ID（关联指标表）
     */
    @Schema(description = "指标ID（关联指标表）")
    private Long indicatorId;

    /**
     * 数据期间（格式：YYYY-MM）
     */
    @Schema(description = "数据期间（格式：YYYY-MM）")
    private String period;

    /**
     * 传入参数（文本/JSON字符串）
     */
    @Schema(description = "传入参数（文本/JSON字符串）")
    private String inputParams;

    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    private Date executeTime;

    /**
     * 执行状态（RUNNING、PENDING、COMPLETED、FAILED）
     */
    @Schema(description = "执行状态（RUNNING、PENDING、COMPLETED、FAILED）")
    private String executeStatus;

    /**
     * 执行结果（文本/JSON字符串）
     */
    @Schema(description = "执行结果（文本/JSON字符串）")
    private String result;

    /**
     * 工作流运行ID（workflowRunId）
     */
    @Schema(description = "工作流运行ID（workflowRunId）")
    private String workflowRunId;
}
