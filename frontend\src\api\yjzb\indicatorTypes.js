import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/yjzb/indicatorTypes/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/yjzb/indicatorTypes/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/yjzb/indicatorTypes/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/yjzb/indicatorTypes/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/yjzb/indicatorTypes/submit',
    method: 'post',
    data: row
  })
}

