/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 财务知识库Excel实体类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class FinanceKnowledgeExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("知识库名称")
    private String name;

    @ExcelProperty("知识库描述")
    private String description;

    @ExcelProperty("Dify知识库ID")
    private String datasetId;

    @ExcelProperty("状态")
    private String statusName;

    @ExcelProperty("排序")
    private Integer sort;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("备注")
    private String remark;
}
