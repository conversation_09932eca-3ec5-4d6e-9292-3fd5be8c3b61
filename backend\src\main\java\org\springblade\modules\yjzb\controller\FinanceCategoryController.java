/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity;
import org.springblade.modules.yjzb.pojo.dto.FinanceCategoryDTO;
import org.springblade.modules.yjzb.pojo.vo.FinanceCategoryVO;
import org.springblade.modules.yjzb.service.IFinanceCategoryService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.List;
import java.util.Map;

/**
 * 知识分类 控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/finance-category")
@Tag(name = "知识分类", description = "知识分类接口")
public class FinanceCategoryController extends BladeController {

    private final IFinanceCategoryService financeCategoryService;

    /**
     * 知识分类 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入financeCategory")
    public R<FinanceCategoryVO> detail(FinanceCategoryEntity financeCategory) {
        FinanceCategoryEntity detail = financeCategoryService.getOne(Condition.getQueryWrapper(financeCategory));
        return R.data(financeCategoryService.getFinanceCategoryById(detail.getId()));
    }

    /**
     * 知识分类 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入financeCategory")
    public R<IPage<FinanceCategoryVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> financeCategory, Query query) {
        IPage<FinanceCategoryVO> pages = financeCategoryService.selectFinanceCategoryPage(Condition.getPage(query), Condition.getQueryWrapper(financeCategory, FinanceCategoryVO.class).getEntity());
        return R.data(pages);
    }

    /**
     * 知识分类 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入financeCategory")
    public R<IPage<FinanceCategoryVO>> page(FinanceCategoryVO financeCategory, Query query) {
        IPage<FinanceCategoryVO> pages = financeCategoryService.selectFinanceCategoryPage(Condition.getPage(query), financeCategory);
        return R.data(pages);
    }

    /**
     * 知识分类 树形结构
     */
    @GetMapping("/tree")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "树形结构", description = "传入知识库ID")
    public R<List<FinanceCategoryVO>> tree(@RequestParam Long knowledgeId) {
        List<FinanceCategoryVO> tree = financeCategoryService.getCategoryTree(knowledgeId);
        return R.data(tree);
    }

    /**
     * 知识分类 子分类列表
     */
    @GetMapping("/children")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "子分类列表", description = "传入父分类ID")
    public R<List<FinanceCategoryVO>> children(@RequestParam Long parentId) {
        List<FinanceCategoryVO> children = financeCategoryService.getChildrenByParentId(parentId);
        return R.data(children);
    }

    /**
     * 知识分类 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增", description = "传入financeCategory")
    @PreAuth("hasRole('administrator')")
    public R save(@Valid @RequestBody FinanceCategoryDTO financeCategory) {
        return R.status(financeCategoryService.saveFinanceCategory(financeCategory));
    }

    /**
     * 知识分类 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "修改", description = "传入financeCategory")
    @PreAuth("hasRole('administrator')")
    public R update(@Valid @RequestBody FinanceCategoryDTO financeCategory) {
        return R.status(financeCategoryService.updateFinanceCategory(financeCategory));
    }

    /**
     * 知识分类 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "新增或修改", description = "传入financeCategory")
    @PreAuth("hasRole('administrator')")
    public R submit(@Valid @RequestBody FinanceCategoryDTO financeCategory) {
        return R.status(financeCategoryService.saveOrUpdate(financeCategory));
    }

    /**
     * 知识分类 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "逻辑删除", description = "传入ids")
    @PreAuth("hasRole('administrator')")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(financeCategoryService.deleteFinanceCategory(ids));
    }

    /**
     * 统计分类下的文档数量
     */
    @GetMapping("/count-documents")
    @ApiOperationSupport(order = 10)
    @Operation(summary = "统计文档数量", description = "传入分类ID")
    public R<Integer> countDocuments(@RequestParam Long categoryId) {
        Integer count = financeCategoryService.countDocumentsByCategory(categoryId);
        return R.data(count);
    }
}
