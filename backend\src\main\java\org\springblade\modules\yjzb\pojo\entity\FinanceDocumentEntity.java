/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serial;

/**
 * 知识库文件 实体类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Data
@TableName("yjzb_finance_document")
@Schema(description = "知识库文件对象")
@EqualsAndHashCode(callSuper = true)
public class FinanceDocumentEntity extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 所属知识库ID
     */
    @Schema(description = "所属知识库ID")
    private Long knowledgeId;

    /**
     * 所属分类ID
     */
    @Schema(description = "所属分类ID")
    private Long categoryId;

    /**
     * Dify文档ID
     */
    @Schema(description = "Dify文档ID")
    private String documentId;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名")
    private String fileOriginalName;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private String fileType;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名")
    private String fileExtension;

    /**
     * 文件说明
     */
    @Schema(description = "文件说明")
    private String fileDescription;

    /**
     * 文件标签（JSON数组格式）
     */
    @Schema(description = "文件标签（JSON数组格式）")
    private String fileTags;

    /**
     * 浏览次数
     */
    @Schema(description = "浏览次数")
    private Integer viewCount;

    /**
     * 下载次数
     */
    @Schema(description = "下载次数")
    private Integer downloadCount;

    /**
     * 上传状态：0-上传中，1-上传成功，2-上传失败
     */
    @Schema(description = "上传状态：0-上传中，1-上传成功，2-上传失败")
    private Integer uploadStatus;

    /**
     * 同步状态：0-未同步，1-同步成功，2-同步失败
     */
    @Schema(description = "同步状态：0-未同步，1-同步成功，2-同步失败")
    private Integer syncStatus;

    /**
     * 同步错误信息
     */
    @Schema(description = "同步错误信息")
    private String syncErrorMsg;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
