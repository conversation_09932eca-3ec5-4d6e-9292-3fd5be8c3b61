package org.springblade.modules.yjzb.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 指标数据导入服务接口
 *
 * <AUTHOR>
 */
public interface IIndicatorImportService {

    /**
     * 导入Excel数据到指标值表
     *
     * @param file Excel文件
     * @param templateType 模板类型 (profit, balance, expense, tax)
     * @param period 数据期间 (YYYY-MM)
     * @return 导入结果
     */
    Map<String, Object> importExcelData(MultipartFile file, String templateType, String period);
}
