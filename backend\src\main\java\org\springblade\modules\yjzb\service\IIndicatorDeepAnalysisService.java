package org.springblade.modules.yjzb.service;

import java.util.Map;

/**
 * 指标深度分析 服务接口
 *
 * 基于指标数据明细生成前端图表所需的 JSON 结构。
 */
public interface IIndicatorDeepAnalysisService {

    /**
     * 构建“费用明细与结构占比”表的数据(JSON结构)。
     *
     * 输入：指标ID + 期间(YYYY-MM)
     * 输出示例：
     * {
     * "title": "表1：11月办公费用明细与结构占比",
     * "columns": ["费用项目", "本月实际支出(元)", "占比(%)"],
     * "rows": [ {"name":"办公用品费","amount":28000.00,"ratio":29.47}, ... ],
     * "total": {"amount":95000.00, "ratio":100.00}
     * }
     */
    Map<String, Object> generateCategoryStructureChartData(Long indicatorId, String period);

    /**
     * 构建"费用趋势对比"图表的数据(JSON结构)。
     *
     * 输入：指标ID + 期间(YYYY-MM)
     * 输出示例：
     * {
     * "currentYear": [{"month":"01","value":125000}, ...],
     * "lastYear": [{"month":"01","value":120000}, ...],
     * "months": ["01","02","03","04","05","06"],
     * "indicators": {
     * "currentYearTotal": 905487,
     * "lastYearTotal": 750000,
     * "growthRate": 20.7,
     * "avgMonthly": 150914,
     * "maxMonth": "05",
     * "minMonth": "02"
     * }
     * }
     */
    Map<String, Object> generateTrendComparisonChartData(Long indicatorId, String period);

    /**
     * 构建"费用波动性分析"图表的数据(JSON结构)。
     *
     * 输入：指标ID + 期间(YYYY-MM)
     * 输出示例：
     * {
     * "monthlyData": [{"month":"2024-01","value":125000}, ...],
     * "indicators": {
     * "mean": 150000,
     * "stdDev": 45000,
     * "coefficientOfVariation": 0.3,
     * "volatilityRange": 90000,
     * "anomalyMonths": 2,
     * "maxValue": 266487,
     * "minValue": 90000
     * },
     * "controlLimits": {
     * "upper3Sigma": 285000,
     * "upper2Sigma": 240000,
     * "lower2Sigma": 60000,
     * "lower3Sigma": 15000
     * }
     * }
     */
    Map<String, Object> generateVolatilityAnalysisChartData(Long indicatorId, String period);

    /**
     * 构建"费用增长率分析"图表的数据(JSON结构)。
     *
     * 输入：指标ID + 期间(YYYY-MM)
     * 输出示例：
     * {
     * "monthlyData":
     * [{"month":"2024-01","value":125000,"momGrowth":0.15,"yoyGrowth":0.08}, ...],
     * "indicators": {
     * "avgMomGrowth": 0.12,
     * "avgYoyGrowth": 0.18,
     * "maxGrowthMonth": "2024-05",
     * "maxGrowthRate": 0.85,
     * "minGrowthMonth": "2024-02",
     * "minGrowthRate": -0.28,
     * "positiveGrowthMonths": 8,
     * "negativeGrowthMonths": 4
     * },
     * "growthTrends": {
     * "momTrend": "increasing",
     * "yoyTrend": "stable",
     * "volatility": "high"
     * }
     * }
     */
    Map<String, Object> generateGrowthAnalysisChartData(Long indicatorId, String period);

    /**
     * 构建"费用预算分析"图表的数据(JSON结构)。
     *
     * 输入：指标ID + 期间(YYYY-MM)
     * 输出示例：
     * {
     * "monthlyData":
     * [{"month":"2024-01","budget":120000,"actual":125000,"variance":5000,"varianceRate":0.042},
     * ...],
     * "indicators": {
     * "totalBudget": 1500000,
     * "totalActual": 1580000,
     * "totalVariance": 80000,
     * "budgetExecutionRate": 1.053,
     * "overBudgetMonths": 8,
     * "underBudgetMonths": 4,
     * "maxOverBudget": 15000,
     * "maxUnderBudget": -8000
     * },
     * "budgetRisk": {
     * "riskLevel": "medium",
     * "riskScore": 0.65,
     * "riskFactors": ["连续超支", "预算执行率过高"]
     * }
     * }
     */
    Map<String, Object> generateBudgetAnalysisChartData(Long indicatorId, String period);

    /**
     * 构建"费用预测分析"图表的数据(JSON结构)。
     *
     * 输入：指标ID + 期间(YYYY-MM)
     * 输出示例：
     * {
     * "monthlyData":
     * [{"month":"2024-01","value":125000,"budget":132000,"isCurrentMonth":false},
     * ...],
     * "indicators": {
     * "currentMonthActual": 130000,
     * "currentMonthBudget": 143000,
     * "currentMonthVariance": -13000,
     * "currentMonthExecutionRate": 0.909,
     * "totalActual": 750000,
     * "totalBudget": 825000,
     * "totalVariance": -75000,
     * "overallExecutionRate": 0.909,
     * "forecastAccuracy": 0.85,
     * "trendDirection": "上升",
     * "confidenceLevel": 0.78
     * },
     * "forecastData": {
     * "historicalData": [{"month":"2024-01","value":125000,"budget":132000}, ...],
     * "predictedData": [{"month":"2024-07","value":135000,"budget":148500}, ...],
     * "budgetData": [{"month":"2024-01","budget":132000}, ...],
     * "predictionMethod": "LinearRegression",
     * "confidenceInterval": {
     * "upperBound": 170000,
     * "lowerBound": 150000,
     * "confidenceLevel": 0.95
     * }
     * }
     * }
     */
    Map<String, Object> generateForecastAnalysisChartData(Long indicatorId, String period);
}
