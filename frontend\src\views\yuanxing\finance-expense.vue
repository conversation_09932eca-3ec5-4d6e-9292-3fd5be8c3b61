<template>
  <!-- 财务税费管控 - 费用管理页面 -->
  <basic-container>
    <!-- 顶部筛选条件 -->
    <div class="filter-panel">
      <el-form :model="filterForm" inline label-width="80px">
        <el-form-item label="选择月份">
          <el-date-picker
            v-model="filterForm.selectedMonth"
            type="month"
            placeholder="选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
          />
        </el-form-item>
        <el-form-item label="指标名称">
          <el-input v-model="filterForm.expenseName" placeholder="请输入指标名称" style="width: 200px;" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 费用统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="8">
        <div class="stat-card">
          <div class="stat-title">本月总支出</div>
          <div class="stat-value">¥ 2,345,678</div>
          <div class="stat-trend up">
            <i class="el-icon-arrow-up"></i>
            同比增长 12.5%
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="stat-card">
          <div class="stat-title">上月总支出</div>
          <div class="stat-value">¥ 2,100,000</div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="stat-card">
          <div class="stat-title">去年同期总支出</div>
          <div class="stat-value">¥ 1,900,000</div>
        </div>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20">
      <!-- 左侧：费用清单表格 -->
      <el-col :span="14">
        <div class="expense-table-panel">
          <div class="panel-header">
            <h4>指标科目</h4>
            <div class="panel-actions">
              <el-button size="small" type="primary" @click="handleAdd">
                <i class="el-icon-plus"></i> 新增
              </el-button>
              <el-button size="small" @click="handleExport">
                <i class="el-icon-download"></i> 导出
              </el-button>
            </div>
          </div>
          
          <el-table :data="expenseList" stripe border>
            <el-table-column label="当前月份" width="100">
              <template #default="{ row }">
                {{ getCurrentMonth() }}
              </template>
            </el-table-column>
            <el-table-column prop="expenseName" label="指标名称" width="100">
              <template #default="{ row }">
                <el-tag size="small">
                  {{ row.expenseName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="金额(元)" width="120" align="right">
              <template #default="{ row }">
                <span :class="{ 'expense-abnormal': row.isAbnormal }">
                  {{ row.amount.toLocaleString() }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="费用说明" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="mini" type="text" @click="handleEdit(row)">编辑</el-button>
                <el-button size="mini" type="text" @click="handleAnalysis(row)">分析</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div style="margin-top: 15px; text-align: right;">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="expenseTotal"
              :page-size="pageSize"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-col>

      <!-- 右侧：趋势图和预测 -->
      <el-col :span="10">


        <!-- 异常费用预警 -->
        <div class="warning-panel">
          <div class="panel-header">
            <h4>异常费用预警</h4>
            <el-badge :value="abnormalExpenses.length" class="warning-badge">
              <i class="el-icon-warning"></i>
            </el-badge>
          </div>
          <div class="warning-list">
            <div
              v-for="item in abnormalExpenses"
              :key="item.id"
              class="warning-item"
              @click="handleWarningClick(item)"
            >
              <div class="warning-content">
                <div class="warning-title">{{ item.title }}</div>
                <div class="warning-desc">{{ item.description }}</div>
              </div>
              <div class="warning-level" :class="`level-${item.level}`">
                {{ item.levelName }}
              </div>
            </div>
          </div>
        </div>
            </el-col>
    </el-row>

    <!-- 费用分析弹窗 -->
    <el-dialog title="费用分析" v-model="analysisDialogVisible" width="900px" destroy-on-close @open="onAnalysisDialogOpen" @close="onAnalysisDialogClose">
      <div v-if="currentAnalysisExpense">
        <div class="analysis-content">
          <!-- 费用基本信息 -->
          <div class="expense-info">
            <h4>{{ currentAnalysisExpense.expenseName }} - {{ getCurrentMonth() }}</h4>
            <p class="expense-amount">金额：¥{{ currentAnalysisExpense.amount.toLocaleString() }}</p>
            <p class="expense-desc">说明：{{ currentAnalysisExpense.description }}</p>
          </div>
          
          <!-- 费用趋势分析图 -->
          <div class="trend-chart-section">
            <h4>费用趋势分析</h4>
            <div id="expenseLineChart" style="width: 100%; height: 300px;"></div>
          </div>
          
          <!-- AI解读 -->
          <div class="ai-analysis-section">
            <h4>🤖 AI智能解读</h4>
            <div class="ai-content">
              <div class="analysis-item">
                <div class="analysis-label">趋势分析：</div>
                <div class="analysis-text">
                  该费用项目在过去5个月呈现波动上升趋势，4月份达到峰值200K，相比1月份增长了28%。
                  整体趋势显示业务规模在扩张，但需要关注成本控制。
                </div>
              </div>
              
              <div class="analysis-item">
                <div class="analysis-label">异常检测：</div>
                <div class="analysis-text">
                  4月份费用异常偏高，建议核查是否存在一次性大额支出或预算执行异常。
                  5月份有所回落，属于正常波动范围。
                </div>
              </div>
              
              <div class="analysis-item">
                <div class="analysis-label">优化建议：</div>
                <div class="analysis-text">
                  1. 建议制定月度预算上限，避免单月费用过度集中；<br>
                  2. 优化{{ currentAnalysisExpense.expenseName }}的资源配置，提高使用效率；<br>
                  3. 建立费用预警机制，当月度支出超过均值20%时及时预警。
                </div>
              </div>
              
              <div class="analysis-item">
                <div class="analysis-label">风险评估：</div>
                <div class="analysis-text risk-low">
                  <el-tag type="success" size="small">低风险</el-tag>
                  当前费用水平在可控范围内，但需持续监控趋势变化。
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="analysisDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="exportAnalysis">导出分析报告</el-button>
      </template>
    </el-dialog>
  
    </basic-container>
  </template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'FinanceExpense',
  data() {
    return {
      // 筛选表单
      filterForm: {
        selectedMonth: '',
        expenseName: ''
      },
      // 分页
      pageSize: 10,
      expenseTotal: 156,
      // 分析弹窗
      analysisDialogVisible: false,
      currentAnalysisExpense: null,
      // Mock 费用列表数据
      expenseList: [
        {
          id: 1,
          date: '2024-01-15',
          department: '技术研发部',
          expenseName: '职工薪酬',
          amount: 156800,
          description: '职工薪酬',
          status: 'approved',
          statusName: '已审批',
          applicant: '张三',
          approver: '李四',
          isAbnormal: false
        },
        {
          id: 2,
          date: '2024-01-14',
          department: '市场营销部',
          expenseName: '销售费用',
          amount: 89500,
          description: '销售费用',
          status: 'pending',
          statusName: '待审核',
          applicant: '王五',
          approver: '',
          isAbnormal: true
        },
        {
          id: 3,
          date: '2024-01-13',
          department: '生产制造部',
          expenseName: '修理费',
          amount: 245000,
          description: '设备修理费',
          status: 'approved',
          statusName: '已审批',
          applicant: '赵六',
          approver: '李四',
          isAbnormal: false
        },
        {
          id: 4,
          date: '2024-01-12',
          department: '财务部',
          expenseName: '办公费',
          amount: 12800,
          description: '办公用品采购',
          status: 'rejected',
          statusName: '已拒绝',
          applicant: '孙七',
          approver: '李四',
          isAbnormal: false
        }
      ],
      // Mock 异常费用预警
      abnormalExpenses: [
        {
          id: 1,
          title: '销售费用异常',
          description: '市场营销部本月销售费用超预算20%',
          level: 'high',
          levelName: '高风险'
        },
        {
          id: 2,
          title: '修理费过高',
          description: '生产制造部修理费比往月增长50%',
          level: 'medium',
          levelName: '中风险'
        },
        {
          id: 3,
          title: '差旅费异常',
          description: '技术研发部差旅费用连续3个月增长',
          level: 'low',
          levelName: '低风险'
        }
      ],
      expenseLineChartInstance: null
    };
  },
  methods: {
    // 查询筛选
    handleFilter() {
      this.$message.success('查询成功');
    },
    
    // 重置筛选
    resetFilter() {
      this.filterForm = {
        selectedMonth: '',
        expenseName: ''
      };
    },
    
    // 新增费用
    handleAdd() {
      this.$message.info('打开新增费用表单');
    },
    
    // 导出数据
    handleExport() {
      this.$message.success('导出中...');
    },
    
    // 编辑费用
    handleEdit(row) {
      this.$message.info(`编辑费用：${row.id}`);
    },
    
    // 分页
    handleSizeChange(size) {
      this.pageSize = size;
    },
    
    handleCurrentChange(page) {
      this.$message.info(`切换到第${page}页`);
    },
    
    // 点击预警
    handleWarningClick(item) {
      this.$message.warning(`查看预警详情：${item.title}`);
    },
    
    // 获取费用类型标签
    getExpenseTypeTag(type) {
      // 已对齐指标说明.txt
      const tags = {
        salary: 'primary',
        office: 'success',
        travel: 'warning',
        sales: 'danger',
        admin: 'info',
        rd: 'info',
        rent: 'info',
        repair: 'info',
        insurance: 'info',
        utility: 'info',
        consumable: 'info',
        other: 'info'
      };
      return tags[type] || '';
    },
    
    // 获取状态标签
    getStatusTag(status) {
      const tags = {
        approved: 'success',
        pending: 'warning',
        rejected: 'danger'
      };
      return tags[status] || '';
    },
    
    // 获取当前月份
    getCurrentMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      return `${year}-${month}`;
    },
    
    // 费用分析
    handleAnalysis(row) {
      console.log('分析费用:', row);
      this.currentAnalysisExpense = row;
      this.analysisDialogVisible = true;
    },
    
    // 导出分析报告
    exportAnalysis() {
      this.$message.success('分析报告导出中...');
    },

    // 分析弹窗打开事件
    onAnalysisDialogOpen() {
      this.$nextTick(() => {
        this.renderExpenseLineChart();
      });
    },
    
    // 分析弹窗关闭事件
    onAnalysisDialogClose() {
      if (this.expenseLineChartInstance) {
        this.expenseLineChartInstance.dispose();
        this.expenseLineChartInstance = null;
      }
    },

    // 费用分析弹窗相关方法
    renderExpenseLineChart() {
      const chartDom = document.getElementById('expenseLineChart');
      if (!chartDom) return;
      if (this.expenseLineChartInstance) {
        this.expenseLineChartInstance.dispose();
      }
      this.expenseLineChartInstance = echarts.init(chartDom);
      this.expenseLineChartInstance.setOption({
        title: { show: false },
        tooltip: { trigger: 'axis' },
        legend: { data: ['今年', '去年'] },
        grid: { left: 40, right: 20, bottom: 30, top: 30 },
        xAxis: { type: 'category', data: ['1月','2月','3月','4月','5月'] },
        yAxis: { type: 'value' },
        series: [
          { name: '今年', type: 'line', data: [120, 140, 110, 160, 130], smooth: false, label: { show: true, position: 'top' } },
          { name: '去年', type: 'line', data: [100, 120, 90, 140, 110], smooth: false, label: { show: true, position: 'top' } }
        ]
      });
      this.expenseLineChartInstance.resize();
    }
  }
};
</script>

<style scoped>
.filter-panel {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.stat-card {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.stat-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.stat-trend.up { color: #f56c6c; }
.stat-trend.down { color: #67c23a; }
.stat-trend.normal { color: #909399; }
.stat-trend.warning { color: #e6a23c; }

.expense-table-panel,
.warning-panel {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.panel-actions {
  display: flex;
  gap: 10px;
}

.expense-abnormal {
  color: #f56c6c;
  font-weight: bold;
}



.warning-badge {
  position: relative;
}

.warning-list {
  max-height: 300px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.warning-item:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
}

.warning-desc {
  font-size: 12px;
  color: #666;
}

.warning-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.warning-level.level-high {
  background: #fef0f0;
  color: #f56c6c;
}

.warning-level.level-medium {
  background: #fdf6ec;
  color: #e6a23c;
}

.warning-level.level-low {
  background: #f0f9ff;
  color: #409eff;
}

/* 分析弹窗样式 */
.analysis-content {
  padding: 10px 0;
}

.expense-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.expense-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
}

.expense-amount {
  font-size: 16px;
  font-weight: bold;
  color: #e6a23c;
  margin: 5px 0;
}

.expense-desc {
  color: #666;
  margin: 5px 0 0 0;
}

.trend-chart-section {
  margin-bottom: 25px;
}

.trend-chart-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.mock-chart {
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 20px;
}

.chart-placeholder {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.trend-data {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  margin-top: 20px;
  height: 120px;
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.trend-item .month {
  font-size: 12px;
  color: #666;
}

.trend-item .bar {
  width: 30px;
  border-radius: 3px 3px 0 0;
  transition: all 0.3s;
}

.trend-item .amount {
  font-size: 12px;
  font-weight: bold;
  color: #303133;
}

.ai-analysis-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.ai-content {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 20px;
}

.analysis-item {
  margin-bottom: 15px;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.analysis-label {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
  font-size: 14px;
}

.analysis-text {
  color: #666;
  line-height: 1.6;
  font-size: 13px;
}

.risk-low {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style> 