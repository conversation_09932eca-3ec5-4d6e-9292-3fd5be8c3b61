/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorForecastVO;
import org.springblade.modules.yjzb.excel.IndicatorForecastExcel;

import java.util.List;

/**
 * 指标预测结果 Mapper 接口
 */
public interface IndicatorForecastMapper extends BaseMapper<IndicatorForecastEntity> {

    List<IndicatorForecastVO> selectIndicatorForecastPage(IPage<IndicatorForecastVO> page, IndicatorForecastVO query);

    List<IndicatorForecastExcel> exportIndicatorForecast(@Param("ew") Wrapper<IndicatorForecastEntity> queryWrapper);

    IndicatorForecastEntity findByIndicatorAndYear(@Param("indicatorId") Long indicatorId, @Param("year") Integer year);
}
