# 运行阶段
FROM bladex/alpine-java:openjdk17_cn_slim

LABEL maintainer="<EMAIL>"
LABEL description="AI指标管控平台后端服务"
LABEL version="1.0.0"

# 安装必要的工具
RUN apk add --no-cache \
    curl \
    tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata

# 创建应用用户
RUN addgroup -g 1000 blade && \
    adduser -D -s /bin/sh -u 1000 -G blade blade

# 创建应用目录
RUN mkdir -p /blade/logs /blade/config /blade/temp && \
    chown -R blade:blade /blade

WORKDIR /blade

# 复制构建的jar文件
COPY ./target/blade-api.jar ./app.jar

# 设置文件权限
RUN chown blade:blade app.jar

# 切换到应用用户
USER blade

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# JVM 参数和启动命令
ENTRYPOINT ["java", \
    "--add-opens", "java.base/java.lang=ALL-UNNAMED", \
    "--add-opens", "java.base/java.lang.reflect=ALL-UNNAMED", \
    "-Djava.security.egd=file:/dev/./urandom", \
    "-Dspring.profiles.active=${SPRING_PROFILES_ACTIVE:-prod}", \
    "-Dfile.encoding=UTF-8", \
    "-Duser.timezone=Asia/Shanghai", \
    "-jar", "app.jar"]
