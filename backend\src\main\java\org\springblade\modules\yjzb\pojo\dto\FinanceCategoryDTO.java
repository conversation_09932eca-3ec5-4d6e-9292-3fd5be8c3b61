/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity;
import java.util.List;

/**
 * 知识分类 数据传输对象
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "知识分类数据传输对象")
public class FinanceCategoryDTO extends FinanceCategoryEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 分类标签列表
     */
    @Schema(description = "分类标签列表")
    private List<String> tagList;

    /**
     * 子分类列表
     */
    @Schema(description = "子分类列表")
    private List<FinanceCategoryDTO> children;

    /**
     * 文档数量
     */
    @Schema(description = "文档数量")
    private Integer documentCount;
}
