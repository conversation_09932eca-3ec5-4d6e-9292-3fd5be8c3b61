<template>
  <!-- 问数助手页面 - 对话模式 -->
  <basic-container>
    <el-row :gutter="20">
      <!-- 左侧：历史对话 -->
      <el-col :span="5">
        <div class="query-history-panel">
          <div class="panel-header">
            <h4>对话历史</h4>
            <el-button size="small" type="text" @click="clearHistory">清空</el-button>
          </div>
          <el-input
            v-model="historySearch"
            placeholder="搜索历史对话"
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 10px;"
          />
          <el-scrollbar height="600px">
            <div class="history-list">
              <div
                v-for="item in filteredHistory"
                :key="item.id"
                class="history-item"
                :class="{ active: selectedHistory?.id === item.id }"
                @click="selectHistory(item)"
              >
                <div class="query-text">{{ item.query }}</div>
                <div class="query-time">{{ item.time }}</div>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </el-col>

      <!-- 右侧：对话区 -->
      <el-col :span="19">
        <div class="chat-panel">
          <div class="chat-content" ref="chatContent">
            <div v-for="msg in chatList" :key="msg.id" :class="['chat-bubble', msg.role]">
              <div class="bubble-content">
                <template v-if="msg.role === 'user'">
                  <i class="el-icon-user" style="margin-right: 6px;"></i>
                  <span>{{ msg.content }}</span>
                </template>
                <template v-else>
                  <i class="el-icon-s-custom" style="margin-right: 6px;"></i>
                  <span v-if="!msg.chart">{{ msg.content }}</span>
                  <div v-else>
                    <span>{{ msg.content }}</span>
                    <div
                      :id="'chart-' + msg.id"
                      style="width: 100%; height: 300px; margin-top: 10px; cursor: pointer;"
                      v-if="msg.chart"
                      @click="openChartDialog(msg.id)"
                    ></div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div class="chat-input-panel">
            <el-input
              v-model="currentQuery"
              type="textarea"
              :rows="2"
              placeholder="请输入您的问题，例如：本月车辆运行费是多少、1-5月车辆运行费和去年同期对比、生成车辆运行费趋势图"
              @keydown.ctrl.enter.native="handleQuery"
            />
            <div class="input-actions">
              <el-button type="primary" @click="handleQuery" :loading="querying">
                <i class="el-icon-s-promotion"></i> 发送
              </el-button>
              <el-button @click="currentQuery = ''">清空</el-button>
            </div>
          </div>
          <el-dialog
            :visible.sync="showChartDialog"
            width="60%"
            top="5vh"
            :before-close="closeChartDialog"
            @opened="onDialogOpened"
          >
            <div :id="'dialog-chart-' + dialogChartId" style="width: 100%; height: 500px;"></div>
          </el-dialog>
        </div>
      </el-col>
    </el-row>
  </basic-container>
</template>

<script>
import * as echarts from 'echarts';
export default {
  name: 'AskHelper',
  data() {
    return {
      currentQuery: '',
      querying: false,
      historySearch: '',
      selectedHistory: null,
      chatList: [
        // 示例对话
        { id: 1, role: 'user', content: '本月车辆运行费是多少？' },
        { id: 2, role: 'assistant', content: '本月车辆运行费为 40.2 万元。' },
        { id: 3, role: 'user', content: '1-5月车辆运行费和去年同期对比' },
        { id: 4, role: 'assistant', content: '2025年1-5月车辆运行费为 200.5 万元，2024年同期为 180.3 万元，同比增长 11.2%。' },
        { id: 5, role: 'user', content: '生成车辆运行费趋势图' },
        { id: 6, role: 'assistant', content: '以下为2024-2025年1-5月车辆运行费趋势图：', chart: true }
      ],
      // mock车辆运行费数据
      profitData: {
        months: ['1月','2月','3月','4月','5月'],
        thisYear: [35.2, 38.1, 40.2, 42.5, 44.5],
        lastYear: [30.1, 32.0, 36.5, 40.0, 41.7]
      },
      showChartDialog: false,
      dialogChartId: null
    };
  },
  computed: {
    filteredHistory() {
      if (!this.historySearch) return this.chatList.filter(i=>i.role==='user').map(i=>({id:i.id,query:i.content,time:'--'}));
      return this.chatList.filter(i=>i.role==='user' && i.content.toLowerCase().includes(this.historySearch.toLowerCase())).map(i=>({id:i.id,query:i.content,time:'--'}));
    }
  },
  watch: {
    chatList: {
      handler(newList) {
        // 渲染所有带chart的消息
        this.$nextTick(() => {
          newList.forEach(msg => {
            if (msg.chart) {
              this.renderProfitChart(msg.id, false);
            }
          });
        });
      },
      deep: true
    }
  },
  mounted() {
    // 页面初次加载时渲染所有chart
    this.chatList.forEach(msg => {
      if (msg.chart) {
        this.renderProfitChart(msg.id, false);
      }
    });
  },
  methods: {
    handleQuery() {
      if (!this.currentQuery.trim()) {
        this.$message.warning('请输入问题');
        return;
      }
      const userMsg = { id: Date.now(), role: 'user', content: this.currentQuery };
      this.chatList.push(userMsg);
      this.querying = true;
      setTimeout(() => {
        let reply = '';
        let chart = false;
        // 简单规则模拟
        if (/车辆运行费趋势图/.test(this.currentQuery)) {
          reply = '以下为2024-2025年1-5月车辆运行费趋势图：';
          chart = true;
        } else if (/1-5月.*车辆运行费.*对比/.test(this.currentQuery)) {
          reply = '2025年1-5月车辆运行费为 200.5 万元，2024年同期为 180.3 万元，同比增长 11.2%。';
        } else if (/本月.*车辆运行费/.test(this.currentQuery)) {
          reply = '本月车辆运行费为 40.2 万元。';
        } else {
          reply = '很抱歉，目前仅支持查询车辆运行费相关的简单对比和趋势图。';
        }
        const assistantMsg = { id: Date.now() + 1, role: 'assistant', content: reply, chart };
        this.chatList.push(assistantMsg);
        this.querying = false;
        this.$nextTick(() => {
          if (chart) {
            this.renderProfitChart(assistantMsg.id);
          }
          this.scrollToBottom();
        });
      }, 1000);
      this.currentQuery = '';
      this.scrollToBottom();
    },
    renderProfitChart(id, isDialog = false) {
      const domId = (isDialog ? 'dialog-' : '') + 'chart-' + id;
      this.$nextTick(() => {
        const chartDom = document.getElementById(domId);
        console.log('尝试渲染ECharts:', domId, chartDom);
        if (!chartDom) return;
        // 容器宽高为0时，延迟重试
        if (chartDom.offsetWidth === 0 || chartDom.offsetHeight === 0) {
          setTimeout(() => this.renderProfitChart(id, isDialog), 200);
          return;
        }
        // 销毁旧实例
        if (chartDom.__echarts__) {
          echarts.dispose(chartDom);
        }
        const { months, thisYear, lastYear } = this.profitData;
        const myChart = echarts.init(chartDom);
        const option = {
          tooltip: { trigger: 'axis' },
          legend: { data: ['2025年', '2024年'] },
          xAxis: { type: 'category', data: months },
          yAxis: { type: 'value', name: '车辆运行费(万元)' },
          series: [
            { name: '2025年', type: 'line', data: thisYear, smooth: true },
            { name: '2024年', type: 'line', data: lastYear, smooth: true }
          ]
        };
        myChart.setOption(option);
        // 只绑定一次resize
        if (!chartDom._resizeBinded) {
          window.addEventListener('resize', () => { myChart.resize(); });
          chartDom._resizeBinded = true;
        }
      });
    },
    openChartDialog(id) {
      console.log('点击小图，弹窗id:', id);
      this.showChartDialog = true;
      this.dialogChartId = id;
      // 弹窗打开后自动渲染
    },
    onDialogOpened() {
      console.log('弹窗已打开，渲染大图id:', this.dialogChartId);
      if (this.dialogChartId) {
        this.renderProfitChart(this.dialogChartId, true);
      }
    },
    closeChartDialog() {
      this.showChartDialog = false;
      this.dialogChartId = null;
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const chatContent = this.$refs.chatContent;
        if (chatContent) {
          chatContent.scrollTop = chatContent.scrollHeight;
        }
      });
    },
    selectHistory(item) {
      // 选中历史问题，自动填充输入框
      this.currentQuery = item.query;
    },
    clearHistory() {
      this.$confirm('确认清空所有对话历史？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.chatList = [];
        this.selectedHistory = null;
        this.$message.success('历史记录已清空');
      });
    }
  }
};
</script>

<style scoped>
.query-history-panel {
  background: #fff;
  border-radius: 4px;
  padding: 15px;
  height: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.history-list {
  padding: 5px 0;
}

.history-item {
  padding: 10px;
  margin-bottom: 5px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  border-left: 3px solid transparent;
}

.history-item:hover {
  background-color: #f5f7fa;
}

.history-item.active {
  background-color: #ecf5ff;
  border-left-color: #409eff;
}

.query-text {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.query-time {
  font-size: 12px;
  color: #909399;
}

.chat-panel {
  background: #fff;
  border-radius: 4px;
  padding: 0 0 20px 0;
  min-height: 700px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-content {
  flex: none;
  height: 650px;
  overflow-y: auto;
  padding: 30px 30px 10px 30px;
  min-height: unset;
}

.chat-bubble {
  display: flex;
  margin-bottom: 18px;
}

.chat-bubble.user {
  justify-content: flex-end;
}

.chat-bubble.assistant {
  justify-content: flex-start;
}

.bubble-content {
  max-width: 70%;
  background: #f5f7fa;
  color: #303133;
  border-radius: 8px;
  padding: 12px 18px;
  font-size: 15px;
  line-height: 1.7;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  word-break: break-all;
}

.chat-bubble.user .bubble-content {
  background: #409eff;
  color: #fff;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 8px;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
}

.chat-bubble.assistant .bubble-content {
  background: #f5f7fa;
  color: #303133;
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 8px;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
}

.chat-input-panel {
  background: #fff;
  border-radius: 4px;
  padding: 20px 30px 0 30px;
  border-top: 1px solid #ebeef5;
}

.input-actions {
  margin-top: 10px;
  text-align: right;
}
:deep(#avue-view) {
  height: 100vh;
  overflow-y: auto;
}
</style> 