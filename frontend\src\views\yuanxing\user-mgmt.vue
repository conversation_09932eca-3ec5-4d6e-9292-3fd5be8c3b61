<template>
  <!-- 用户管理页面 -->
  <basic-container>
    <!-- 顶部统计 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background: #409EFF;">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalUsers }}</div>
              <div class="stat-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background: #67C23A;">
              <i class="el-icon-success"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.activeUsers }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background: #E6A23C;">
              <i class="el-icon-s-custom"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.adminUsers }}</div>
              <div class="stat-label">管理员用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background: #F56C6C;">
              <i class="el-icon-remove-outline"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.disabledUsers }}</div>
              <div class="stat-label">禁用用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户列表 -->
    <el-card>
      <div slot="header" class="card-header">
        <span>用户管理</span>
        <div class="header-actions">
          <el-button size="small" type="primary" @click="showAddUserDialog">
            <i class="el-icon-plus"></i> 新增用户
          </el-button>
          <el-button size="small" @click="refreshUsers">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>

      <!-- 搜索筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="15">
          <el-col :span="6">
            <el-input
              v-model="searchForm.username"
              placeholder="搜索用户名"
              size="small"
              prefix-icon="el-icon-search"
            />
          </el-col>
          <el-col :span="6">
            <el-select v-model="searchForm.status" placeholder="用户状态" size="small" clearable>
              <el-option label="正常" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="searchForm.roleId" placeholder="用户角色" size="small" clearable>
              <el-option
                v-for="role in roleOptions"
                :key="role.id"
                :label="role.name"
                :value="role.id"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" size="small" @click="searchUsers">搜索</el-button>
            <el-button size="small" @click="resetSearch">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 用户表格 -->
      <el-table :data="userList" stripe v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="avatar" label="头像" width="80">
          <template slot-scope="scope">
            <el-avatar :src="scope.row.avatar" size="small">
              {{ scope.row.realName.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="account" label="用户名" width="120" />
        <el-table-column prop="realName" label="真实姓名" width="120" />
        <el-table-column prop="email" label="邮箱" width="180" />
        <el-table-column prop="phone" label="手机号" width="120" />
        <el-table-column prop="deptName" label="所属部门" width="120" />
        <el-table-column prop="roleName" label="角色" width="120">
          <template slot-scope="scope">
            <el-tag size="mini" :type="getRoleTagType(scope.row.roleId)">
              {{ scope.row.roleName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="mini">
              {{ scope.row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="editUser(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" @click="assignRole(scope.row)">分配角色</el-button>
            <el-button size="mini" type="text" @click="resetPassword(scope.row)">重置密码</el-button>
            <el-button 
              size="mini" 
              type="text" 
              :style="{ color: scope.row.status === 1 ? '#f56c6c' : '#67c23a' }"
              @click="toggleStatus(scope.row)"
            >
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.size"
          :total="page.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑用户对话框 -->
    <el-dialog
      :title="userForm.id ? '编辑用户' : '新增用户'"
      :visible.sync="userDialogVisible"
      width="600px"
      @close="resetUserForm"
    >
      <el-form :model="userForm" :rules="userRules" ref="userForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="account">
              <el-input v-model="userForm.account" placeholder="请输入用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="userForm.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="userForm.phone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属部门" prop="deptId">
              <el-select v-model="userForm.deptId" placeholder="请选择部门" style="width: 100%;">
                <el-option
                  v-for="dept in deptOptions"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户角色" prop="roleIds">
              <el-select v-model="userForm.roleIds" placeholder="请选择角色" multiple style="width: 100%;">
                <el-option
                  v-for="role in roleOptions"
                  :key="role.id"
                  :label="role.name"
                  :value="role.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="用户状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="userForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="userDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">保存</el-button>
      </div>
    </el-dialog>

    <!-- 角色分配对话框 -->
    <el-dialog
      title="分配角色"
      :visible.sync="roleDialogVisible"
      width="500px"
    >
      <div v-if="selectedUser">
        <p>为用户 <strong>{{ selectedUser.realName }}({{ selectedUser.account }})</strong> 分配角色：</p>
        <el-checkbox-group v-model="selectedRoles" style="margin-top: 15px;">
          <el-checkbox
            v-for="role in roleOptions"
            :key="role.id"
            :label="role.id"
            style="display: block; margin-bottom: 10px;"
          >
            {{ role.name }} - {{ role.description }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div slot="footer">
        <el-button @click="roleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveUserRoles" :loading="saving">保存</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
export default {
  name: 'UserMgmt',
  data() {
    return {
      loading: false,
      saving: false,
      userDialogVisible: false,
      roleDialogVisible: false,
      selectedUser: null,
      selectedRoles: [],
      
      // 统计数据
      stats: {
        totalUsers: 156,
        activeUsers: 142,
        adminUsers: 8,
        disabledUsers: 6
      },
      
      // 搜索表单
      searchForm: {
        username: '',
        status: '',
        roleId: ''
      },
      
      // 分页信息
      page: {
        current: 1,
        size: 10,
        total: 156
      },
      
      // 用户表单
      userForm: {
        id: null,
        account: '',
        realName: '',
        email: '',
        phone: '',
        deptId: '',
        roleIds: [],
        status: 1,
        remark: ''
      },
      
      // 表单验证规则
      userRules: {
        account: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        deptId: [
          { required: true, message: '请选择所属部门', trigger: 'change' }
        ],
        roleIds: [
          { required: true, message: '请选择用户角色', trigger: 'change' }
        ]
      },
      
      // Mock数据
      userList: [
        {
          id: 1,
          account: 'admin',
          realName: '系统管理员',
          email: '<EMAIL>',
          phone: '***********',
          avatar: '',
          deptId: 1,
          deptName: '管理部门',
          roleId: 1,
          roleName: '超级管理员',
          status: 1,
          createTime: '2024-01-01 09:00:00',
          remark: '系统超级管理员'
        },
        {
          id: 2,
          account: 'finance01',
          realName: '张财务',
          email: '<EMAIL>',
          phone: '***********',
          avatar: '',
          deptId: 2,
          deptName: '财务部',
          roleId: 2,
          roleName: '财务管理员',
          status: 1,
          createTime: '2024-01-02 09:00:00',
          remark: '财务部门管理员'
        },
        {
          id: 3,
          account: 'analyst01',
          realName: '李分析师',
          email: '<EMAIL>',
          phone: '***********',
          avatar: '',
          deptId: 3,
          deptName: '分析部',
          roleId: 3,
          roleName: '数据分析师',
          status: 1,
          createTime: '2024-01-03 09:00:00',
          remark: '负责数据分析工作'
        }
      ],
      
      // 部门选项
      deptOptions: [
        { id: 1, name: '管理部门' },
        { id: 2, name: '财务部' },
        { id: 3, name: '分析部' },
        { id: 4, name: '技术部' },
        { id: 5, name: '市场部' }
      ],
      
      // 角色选项
      roleOptions: [
        { id: 1, name: '超级管理员', description: '拥有系统所有权限' },
        { id: 2, name: '财务管理员', description: '财务模块管理权限' },
        { id: 3, name: '数据分析师', description: '数据查看和分析权限' },
        { id: 4, name: '普通用户', description: '基础查看权限' }
      ]
    };
  },
  
  methods: {
    // 获取角色标签类型
    getRoleTagType(roleId) {
      const types = { 1: 'danger', 2: 'warning', 3: 'primary', 4: 'info' };
      return types[roleId] || 'info';
    },
    
    // 搜索用户
    searchUsers() {
      this.loading = true;
      setTimeout(() => {
        this.$message.success('搜索完成');
        this.loading = false;
      }, 1000);
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        username: '',
        status: '',
        roleId: ''
      };
    },
    
    // 显示新增用户对话框
    showAddUserDialog() {
      this.userDialogVisible = true;
    },
    
    // 编辑用户
    editUser(user) {
      this.userForm = { ...user, roleIds: [user.roleId] };
      this.userDialogVisible = true;
    },
    
    // 分配角色
    assignRole(user) {
      this.selectedUser = user;
      this.selectedRoles = [user.roleId];
      this.roleDialogVisible = true;
    },
    
    // 重置密码
    resetPassword(user) {
      this.$confirm(`确认重置用户 "${user.realName}" 的密码吗？`, '确认重置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('密码重置成功，新密码已发送到用户邮箱');
      });
    },
    
    // 切换用户状态
    toggleStatus(user) {
      const action = user.status === 1 ? '禁用' : '启用';
      this.$confirm(`确认${action}用户 "${user.realName}" 吗？`, `确认${action}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        user.status = user.status === 1 ? 0 : 1;
        this.$message.success(`用户${action}成功`);
      });
    },
    
    // 保存用户
    saveUser() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          this.saving = true;
          setTimeout(() => {
            this.$message.success(this.userForm.id ? '用户更新成功' : '用户创建成功');
            this.saving = false;
            this.userDialogVisible = false;
            this.resetUserForm();
          }, 1500);
        }
      });
    },
    
    // 保存用户角色
    saveUserRoles() {
      this.saving = true;
      setTimeout(() => {
        this.$message.success('角色分配成功');
        this.saving = false;
        this.roleDialogVisible = false;
      }, 1000);
    },
    
    // 重置用户表单
    resetUserForm() {
      this.userForm = {
        id: null,
        account: '',
        realName: '',
        email: '',
        phone: '',
        deptId: '',
        roleIds: [],
        status: 1,
        remark: ''
      };
      if (this.$refs.userForm) {
        this.$refs.userForm.resetFields();
      }
    },
    
    // 刷新用户列表
    refreshUsers() {
      this.loading = true;
      setTimeout(() => {
        this.$message.success('用户列表已刷新');
        this.loading = false;
      }, 1000);
    },
    
    // 分页相关
    handleSizeChange(size) {
      this.page.size = size;
      this.searchUsers();
    },
    
    handleCurrentChange(current) {
      this.page.current = current;
      this.searchUsers();
    }
  },
  
  mounted() {
    // 初始化数据
  }
};
</script>

<style scoped>
.stat-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-right: 15px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}
</style> 