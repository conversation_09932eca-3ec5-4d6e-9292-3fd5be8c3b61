/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.service.IDifyService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.AbstractResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.BufferedReader;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Dify接口对接服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DifyServiceImpl implements IDifyService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${dify.api.base-url:http://localhost}")
    private String difyBaseUrl;

    @Value("${dify.api.key:}")
    private String difyApiKey;

    @Value("${dify.api.agentkey.analysisagent:}")
    private String difyAgentKey;

    // 流式执行时，为避免 30s 默认读超时导致连接中断，这里提供可配置超时（默认30分钟）
    @Value("${dify.api.stream-timeout-ms:1800000}")
    private int difyStreamTimeoutMs;

    // 等待首个事件中拿到 workflow_run_id 的超时时间（默认30秒）
    @Value("${dify.api.first-id-timeout-ms:30000}")
    private int difyFirstIdTimeoutMs;

    @Value("${oss.bucket-name:}")
    private String bucketName;

    @Value("${oss.endpoint:}")
    private String minioEndpoint;

    @Value("${oss.access-key:}")
    private String minioAccessKey;

    @Value("${oss.secret-key:}")
    private String minioSecretKey;

    @Override
    public String createDataset(String name, String description) {
        try {
            String url = difyBaseUrl + "/v1/datasets";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("name", name);
            if (Func.isNotBlank(description)) {
                requestBody.put("description", description);
            }
            requestBody.put("permission", "only_me");

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                if (jsonNode.has("id")) {
                    return jsonNode.get("id").asText();
                }
            }

            log.error("创建Dify知识库失败，响应状态: {}, 响应内容: {}", response.getStatusCode(), response.getBody());
            return null;
        } catch (Exception e) {
            log.error("创建Dify知识库异常", e);
            return null;
        }
    }

    @Override
    public boolean updateDataset(String datasetId, String name, String description) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId;

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("name", name);
            if (Func.isNotBlank(description)) {
                requestBody.put("description", description);
            }

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.PATCH, entity, String.class);

            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.error("更新Dify知识库异常", e);
            return false;
        }
    }

    @Override
    public boolean deleteDataset(String datasetId) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId;

            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, String.class);

            return response.getStatusCode() == HttpStatus.NO_CONTENT || response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.error("删除Dify知识库异常", e);
            return false;
        }
    }

    @Override
    public String uploadDocument(String datasetId, MultipartFile file, String fileName) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/document/create-by-file";

            HttpHeaders headers = createMultipartHeaders();

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", file.getResource());

            // 添加处理规则
            Map<String, Object> data = new HashMap<>();
            data.put("indexing_technique", "high_quality");
            data.put("process_rule", Map.of("mode", "automatic"));
            if (Func.isNotBlank(fileName)) {
                data.put("name", fileName);
            }

            try {
                body.add("data", objectMapper.writeValueAsString(data));
            } catch (Exception e) {
                log.error("序列化data参数失败", e);
                return null;
            }

            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(body, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                if (jsonNode.has("document") && jsonNode.get("document").has("id")) {
                    return jsonNode.get("document").get("id").asText();
                }
            }

            log.error("上传文档到Dify失败，响应状态: {}, 响应内容: {}", response.getStatusCode(), response.getBody());
            return null;
        } catch (Exception e) {
            log.error("上传文档到Dify异常", e);
            return null;
        }
    }

    @Override
    public String uploadDocumentByPath(String datasetId, String filePath, String fileName) {
        File tempFile = null;
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/document/create-by-file";

            HttpHeaders headers = createMultipartHeaders();

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 从Minio获取文件流并创建Resource
            try {
                // 获取MinioClient
                MinioClient minioClient = getMinioClient();

                // 使用MinioClient获取对象流
                InputStream fileStream = minioClient.getObject(
                        GetObjectArgs.builder()
                                .bucket(bucketName)
                                .object(filePath)
                                .build());

                // 创建临时文件
                String fileExtension = getFileExtension(fileName);
                tempFile = File.createTempFile("dify_upload_", "." + fileExtension);

                // 将InputStream写入临时文件
                try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    long totalBytes = 0;
                    while ((bytesRead = fileStream.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                        totalBytes += bytesRead;
                    }
                    log.info("从Minio读取文件并保存到临时文件成功，文件大小: {} bytes, 临时文件: {}",
                            totalBytes, tempFile.getAbsolutePath());
                }

                // 使用FileSystemResource，这是Dify接口期望的方式
                FileSystemResource fileResource = new FileSystemResource(tempFile) {
                    @Override
                    public String getFilename() {
                        return fileName;
                    }
                };

                body.add("file", fileResource);
            } catch (Exception e) {
                log.error("从Minio获取文件失败: {}", filePath, e);
                return null;
            }

            // 添加处理规则
            Map<String, Object> data = new HashMap<>();
            data.put("indexing_technique", "high_quality");
            data.put("process_rule", Map.of("mode", "automatic"));
            if (Func.isNotBlank(fileName)) {
                data.put("name", fileName);
            }

            try {
                body.add("data", objectMapper.writeValueAsString(data));
            } catch (Exception e) {
                log.error("序列化data参数失败", e);
                return null;
            }

            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(body, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                if (jsonNode.has("document") && jsonNode.get("document").has("id")) {
                    return jsonNode.get("document").get("id").asText();
                }
            }

            log.error("上传文档到Dify失败，响应状态: {}, 响应内容: {}", response.getStatusCode(), response.getBody());
            return null;
        } catch (Exception e) {
            log.error("上传文档到Dify异常", e);
            return null;
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.delete(tempFile.toPath());
                    log.debug("临时文件已删除: {}", tempFile.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", tempFile.getAbsolutePath(), e);
                }
            }
        }
    }

    @Override
    public boolean updateDocument(String datasetId, String documentId, String fileName) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/documents/" + documentId + "/update_by_text";

            Map<String, Object> requestBody = new HashMap<>();
            if (Func.isNotBlank(fileName)) {
                requestBody.put("name", fileName);
            }
            // 注意：这个方法可能需要text参数，根据实际需求调整
            requestBody.put("text", ""); // 可能需要传入实际的文本内容

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            return response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED;
        } catch (Exception e) {
            log.error("更新Dify文档异常", e);
            return false;
        }
    }

    @Override
    public boolean deleteDocument(String datasetId, String documentId) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/documents/" + documentId;

            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, String.class);

            return response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.NO_CONTENT;
        } catch (Exception e) {
            log.error("删除Dify文档异常", e);
            return false;
        }
    }

    @Override
    public byte[] downloadDocument(String datasetId, String documentId) {
        try {
            // 注意：Dify API文档中没有明确的下载接口，这个方法可能需要根据实际API调整
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/documents/" + documentId + "/download";

            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.GET, entity, byte[].class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }

            log.warn("下载Dify文档失败，响应状态: {}", response.getStatusCode());
            return null;
        } catch (Exception e) {
            log.error("下载Dify文档异常", e);
            return null;
        }
    }

    @Override
    public String getDocumentUploadFile(String datasetId, String documentId) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/documents/" + documentId + "/upload-file";

            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                // 调用Dify接口获取文件信息
                if (Func.isNotBlank(response.getBody())) {
                    try {
                        JsonNode jsonNode = objectMapper.readTree(response.getBody());
                        if (jsonNode.has("download_url")) {
                            return difyBaseUrl + jsonNode.get("download_url").asText();
                        }
                    } catch (Exception e) {
                        log.error("解析Dify文件信息失败", e);
                    }
                }
            }

            log.warn("获取Dify文档上传文件信息失败，响应状态: {}", response.getStatusCode());
            return null;
        } catch (Exception e) {
            log.error("获取Dify文档上传文件信息异常", e);
            return null;
        }
    }

    @Override
    public String getDatasetList() {
        try {
            String url = difyBaseUrl + "/v1/datasets?page=1&limit=20";

            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }

            log.warn("获取Dify知识库列表失败，响应状态: {}", response.getStatusCode());
            return null;
        } catch (Exception e) {
            log.error("获取Dify知识库列表异常", e);
            return null;
        }
    }

    @Override
    public String getDatasetDetail(String datasetId) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId;

            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }

            log.warn("获取Dify知识库详情失败，响应状态: {}", response.getStatusCode());
            return null;
        } catch (Exception e) {
            log.error("获取Dify知识库详情异常", e);
            return null;
        }
    }

    @Override
    public String getDocumentList(String datasetId) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/documents";

            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }

            log.warn("获取Dify文档列表失败，响应状态: {}", response.getStatusCode());
            return null;
        } catch (Exception e) {
            log.error("获取Dify文档列表异常", e);
            return null;
        }
    }

    @Override
    public String getDocumentDetail(String datasetId, String documentId) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/documents/" + documentId;

            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }

            log.warn("获取Dify文档详情失败，响应状态: {}", response.getStatusCode());
            return null;
        } catch (Exception e) {
            log.error("获取Dify文档详情异常", e);
            return null;
        }
    }

    @Override
    public String searchDataset(String datasetId, String query) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/retrieve";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("query", query);

            // 添加检索模型配置
            Map<String, Object> retrievalModel = new HashMap<>();
            retrievalModel.put("search_method", "keyword_search");
            retrievalModel.put("reranking_enable", false);
            retrievalModel.put("top_k", 10);
            retrievalModel.put("score_threshold_enabled", false);
            requestBody.put("retrieval_model", retrievalModel);

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }

            log.warn("搜索Dify知识库失败，响应状态: {}", response.getStatusCode());
            return null;
        } catch (Exception e) {
            log.error("搜索Dify知识库异常", e);
            return null;
        }
    }

    /**
     * 创建请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if (Func.isNotBlank(difyApiKey)) {
            headers.set("Authorization", "Bearer " + difyApiKey);
        }
        return headers;
    }

    /**
     * 创建多部分请求头
     */
    private HttpHeaders createMultipartHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        if (Func.isNotBlank(difyApiKey)) {
            headers.set("Authorization", "Bearer " + difyApiKey);
        }
        return headers;
    }

    @Override
    public String createDocumentByText(String datasetId, String name, String text) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/document/create_by_text";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("name", name);
            requestBody.put("text", text);
            requestBody.put("indexing_technique", "high_quality");
            requestBody.put("process_rule", Map.of("mode", "automatic"));

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                if (jsonNode.has("document") && jsonNode.get("document").has("id")) {
                    return jsonNode.get("document").get("id").asText();
                }
            }

            log.error("通过文本创建Dify文档失败，响应状态: {}, 响应内容: {}", response.getStatusCode(), response.getBody());
            return null;
        } catch (Exception e) {
            log.error("通过文本创建Dify文档异常", e);
            return null;
        }
    }

    @Override
    public String getDocumentIndexingStatus(String datasetId, String batch) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/documents/" + batch + "/indexing-status";

            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }

            log.warn("获取Dify文档索引状态失败，响应状态: {}", response.getStatusCode());
            return null;
        } catch (Exception e) {
            log.error("获取Dify文档索引状态异常", e);
            return null;
        }
    }

    @Override
    public boolean updateDocumentByText(String datasetId, String documentId, String name, String text) {
        try {
            String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/documents/" + documentId + "/update_by_text";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("name", name);
            requestBody.put("text", text);

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            return response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED;
        } catch (Exception e) {
            log.error("通过文本更新Dify文档异常", e);
            return false;
        }
    }

    // 阻塞执行接口已在控制器精简后移除

    @Override
    public String getWorkflowRunDetail(String workflowRunId, String apiKey) {
        try {
            String url = difyBaseUrl + "/v1/workflows/run/" + workflowRunId;

            // 选择有效的API Key：入参 -> 配置的agent key -> 配置的默认key -> 环境变量/系统属性
            String effectiveKey = Func.isNotBlank(apiKey) ? apiKey
                    : (Func.isNotBlank(difyAgentKey) ? difyAgentKey
                            : (Func.isNotBlank(difyApiKey) ? difyApiKey
                                    : System.getProperty("dify.api.key", System.getenv("DIFY_API_KEY"))));

            HttpHeaders headers = createHeaders();
            if (Func.isNotBlank(effectiveKey)) {
                headers.set("Authorization", "Bearer " + effectiveKey);
            }
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            }
            log.error("查询Dify工作流执行详情失败，响应状态: {}, 响应内容: {}", response.getStatusCode(), response.getBody());
            return null;
        } catch (Exception e) {
            log.error("查询Dify工作流执行详情异常", e);
            return null;
        }
    }

    @Override
    public String startWorkflowStreaming(Map<String, Object> inputs, String user, String apiKey) {
        try {
            String url = difyBaseUrl + "/v1/workflows/run";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("inputs", inputs == null ? Map.of() : inputs);
            requestBody.put("response_mode", "streaming");
            if (Func.isNotBlank(user)) {
                requestBody.put("user", user);
            }

            // 选择有效的API Key：入参 -> 配置的agent key -> 配置的默认key -> 环境变量/系统属性
            String effectiveKey = Func.isNotBlank(apiKey) ? apiKey
                    : (Func.isNotBlank(difyAgentKey) ? difyAgentKey
                            : (Func.isNotBlank(difyApiKey) ? difyApiKey
                                    : System.getProperty("dify.api.key", System.getenv("DIFY_API_KEY"))));

            HttpHeaders headers = createHeaders();
            headers.setAccept(List.of(MediaType.TEXT_EVENT_STREAM, MediaType.ALL));
            if (Func.isNotBlank(effectiveKey)) {
                headers.set("Authorization", "Bearer " + effectiveKey);
            }

            CountDownLatch firstIdLatch = new CountDownLatch(1);
            final String[] idHolder = new String[1];

            CompletableFuture.runAsync(() -> {
                try {
                    // 使用更长读超时的 RestTemplate，确保不中途断开
                    SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
                    factory.setConnectTimeout(Math.min(difyStreamTimeoutMs, 60_000));
                    factory.setReadTimeout(difyStreamTimeoutMs);
                    RestTemplate longStreamTemplate = new RestTemplate(factory);

                    longStreamTemplate.execute(
                            url,
                            HttpMethod.POST,
                            request -> {
                                request.getHeaders().putAll(headers);
                                byte[] bodyBytes = objectMapper.writeValueAsBytes(requestBody);
                                request.getBody().write(bodyBytes);
                            },
                            response -> {
                                if (!response.getStatusCode().is2xxSuccessful()) {
                                    log.error("启动Dify工作流(streaming)失败，HTTP状态: {}", response.getStatusCode());
                                    if (idHolder[0] == null) {
                                        idHolder[0] = null;
                                        firstIdLatch.countDown();
                                    }
                                    return null;
                                }
                                try (InputStream is = response.getBody();
                                        InputStreamReader isr = new InputStreamReader(is);
                                        BufferedReader reader = new BufferedReader(isr)) {
                                    StringBuilder dataBuffer = new StringBuilder();
                                    String line;
                                    int lineCount = 0;
                                    while ((line = reader.readLine()) != null) {
                                        if (line.startsWith("data:")) {
                                            String data = line.substring(5).trim();
                                            if (!data.isEmpty()) {
                                                // 立即尝试解析当前行（有些实现不会以空行分隔）
                                                try {
                                                    JsonNode node = objectMapper.readTree(data);
                                                    String found = null;
                                                    if (node.has("workflow_run_id")) {
                                                        found = node.get("workflow_run_id").asText();
                                                    }
                                                    if (found == null && node.has("data")
                                                            && node.get("data").has("workflow_run_id")) {
                                                        found = node.get("data").get("workflow_run_id").asText();
                                                    }
                                                    if (found == null && node.has("event")
                                                            && node.get("event").asText().contains("workflow")) {
                                                        JsonNode dataNode = node.get("data");
                                                        if (dataNode != null) {
                                                            if (dataNode.has("workflow_run_id")) {
                                                                found = dataNode.get("workflow_run_id").asText();
                                                            } else if (dataNode.has("id")) {
                                                                found = dataNode.get("id").asText();
                                                            }
                                                        }
                                                    }
                                                    if (found == null && node.has("id")) {
                                                        found = node.get("id").asText();
                                                    }
                                                    if (found != null && idHolder[0] == null) {
                                                        idHolder[0] = found;
                                                        firstIdLatch.countDown();
                                                    }
                                                } catch (Exception ignore) {
                                                    // 累加以便按块解析
                                                    dataBuffer.append(data);
                                                }
                                            }
                                        }
                                        // 兼容按块解析：某些实现会以空行分隔事件
                                        if (line.isEmpty() && dataBuffer.length() > 0) {
                                            String jsonText = dataBuffer.toString();
                                            dataBuffer.setLength(0);
                                            try {
                                                JsonNode node = objectMapper.readTree(jsonText);
                                                String found = null;
                                                if (node.has("workflow_run_id")) {
                                                    found = node.get("workflow_run_id").asText();
                                                }
                                                if (found == null && node.has("data")
                                                        && node.get("data").has("workflow_run_id")) {
                                                    found = node.get("data").get("workflow_run_id").asText();
                                                }
                                                if (found == null && node.has("event")
                                                        && node.get("event").asText().contains("workflow")) {
                                                    JsonNode dataNode = node.get("data");
                                                    if (dataNode != null) {
                                                        if (dataNode.has("workflow_run_id")) {
                                                            found = dataNode.get("workflow_run_id").asText();
                                                        } else if (dataNode.has("id")) {
                                                            found = dataNode.get("id").asText();
                                                        }
                                                    }
                                                }
                                                if (found == null && node.has("id")) {
                                                    found = node.get("id").asText();
                                                }
                                                if (found != null && idHolder[0] == null) {
                                                    idHolder[0] = found;
                                                    firstIdLatch.countDown();
                                                }
                                            } catch (Exception ignore) {
                                            }
                                        }
                                        if (++lineCount <= 3 && idHolder[0] == null) {
                                            // 打印前3行，辅助排障
                                            log.debug("Dify SSE line: {}", line);
                                        }
                                    }
                                }
                                return null;
                            });
                } catch (Exception ex) {
                    log.error("Dify streaming 后台读取异常", ex);
                    if (idHolder[0] == null) {
                        idHolder[0] = null;
                        firstIdLatch.countDown();
                    }
                }
            });

            boolean got = firstIdLatch.await(Math.max(5, difyFirstIdTimeoutMs / 1000), TimeUnit.SECONDS);
            if (!got) {
                log.error("启动Dify工作流(streaming)超时未获得workflow_run_id");
                return null;
            }
            return idHolder[0];
        } catch (Exception e) {
            log.error("启动Dify工作流(streaming)异常", e);
            return null;
        }
    }

    @Override
    public String startWorkflowBlocking(Map<String, Object> inputs, String user, String apiKey) {
        try {
            String url = difyBaseUrl + "/v1/workflows/run";

            Map<String, Object> requestBody = new HashMap<>();
            Map<String, Object> safeInputs = (inputs == null) ? new HashMap<>() : new HashMap<>(inputs);
            requestBody.put("inputs", safeInputs);
            requestBody.put("response_mode", "blocking");
            if (Func.isNotBlank(user)) {
                requestBody.put("user", user);
            }

            // 选择有效的API Key
            String effectiveKey = Func.isNotBlank(apiKey) ? apiKey
                    : (Func.isNotBlank(difyAgentKey) ? difyAgentKey
                            : (Func.isNotBlank(difyApiKey) ? difyApiKey
                                    : System.getProperty("dify.api.key", System.getenv("DIFY_API_KEY"))));

            HttpHeaders headers = createHeaders();
            if (Func.isNotBlank(effectiveKey)) {
                headers.set("Authorization", "Bearer " + effectiveKey);
            }
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            }
            log.error("启动Dify工作流(blocking)失败，HTTP状态: {}, 响应: {}", response.getStatusCode(), response.getBody());
            return null;
        } catch (Exception e) {
            log.error("启动Dify工作流(blocking)异常", e);
            return null;
        }
    }

    /**
     * 自定义Resource实现，用于包装从Minio获取的文件流
     */
    private static class MinioFileResource extends AbstractResource {
        private final InputStream inputStream;
        private final String filename;

        public MinioFileResource(InputStream inputStream, String filename) {
            this.inputStream = inputStream;
            this.filename = filename;
        }

        @Override
        public String getDescription() {
            return "Minio file resource [" + filename + "]";
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return inputStream;
        }

        @Override
        public String getFilename() {
            return filename;
        }

        @Override
        public boolean exists() {
            return inputStream != null;
        }

        @Override
        public boolean isReadable() {
            return true;
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (Func.isBlank(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 获取MinioClient实例
     */
    private MinioClient getMinioClient() {
        try {
            // 通过OssBuilder获取当前的OSS模板，然后提取MinioClient
            // 这里我们需要通过反射或其他方式获取MinioClient
            // 由于MinioTemplate通常会包含MinioClient，我们可以尝试获取它

            // 简化方案：直接创建MinioClient
            // 从配置中获取Minio连接信息
            return MinioClient.builder()
                    .endpoint(minioEndpoint)
                    .credentials(minioAccessKey, minioSecretKey)
                    .build();
        } catch (Exception e) {
            log.error("创建MinioClient失败", e);
            throw new RuntimeException("无法创建MinioClient", e);
        }
    }

}
