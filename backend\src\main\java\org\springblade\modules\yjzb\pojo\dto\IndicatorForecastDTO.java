package org.springblade.modules.yjzb.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(description = "指标预测结果 数据传输对象")
public class IndicatorForecastDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "指标ID（关联指标表）")
    private Long indicatorId;

    @Schema(description = "指标名称（为历史保留冗余）")
    private String indicatorName;

    @Schema(description = "年份（YYYY）")
    private Integer year;

    @Schema(description = "2月预测值")
    private BigDecimal forecastM02;
    @Schema(description = "3月预测值")
    private BigDecimal forecastM03;
    @Schema(description = "4月预测值")
    private BigDecimal forecastM04;
    @Schema(description = "5月预测值")
    private BigDecimal forecastM05;
    @Schema(description = "6月预测值")
    private BigDecimal forecastM06;
    @Schema(description = "7月预测值")
    private BigDecimal forecastM07;
    @Schema(description = "8月预测值")
    private BigDecimal forecastM08;
    @Schema(description = "9月预测值")
    private BigDecimal forecastM09;
    @Schema(description = "10月预测值")
    private BigDecimal forecastM10;
    @Schema(description = "11月预测值")
    private BigDecimal forecastM11;
    @Schema(description = "12月预测值")
    private BigDecimal forecastM12;
}
