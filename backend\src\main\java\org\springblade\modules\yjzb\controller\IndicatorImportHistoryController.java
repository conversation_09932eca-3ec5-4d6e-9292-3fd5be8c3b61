/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.IndicatorImportHistoryEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorImportHistoryVO;
import org.springblade.modules.yjzb.wrapper.IndicatorImportHistoryWrapper;
import org.springblade.modules.yjzb.service.IIndicatorImportHistoryService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.Map;

/**
 * 指标导入历史记录 控制器
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/indicatorImportHistory")
@Tag(name = "指标导入历史记录", description = "指标导入历史记录接口")
public class IndicatorImportHistoryController extends BladeController {

    private final IIndicatorImportHistoryService indicatorImportHistoryService;

    /**
     * 指标导入历史记录 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入indicatorImportHistory")
    public R<IndicatorImportHistoryVO> detail(IndicatorImportHistoryEntity indicatorImportHistory) {
        IndicatorImportHistoryEntity detail = indicatorImportHistoryService.getOne(Condition.getQueryWrapper(indicatorImportHistory));
        return R.data(IndicatorImportHistoryWrapper.build().entityVO(detail));
    }

    /**
     * 指标导入历史记录 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入indicatorImportHistory")
    public R<IPage<IndicatorImportHistoryVO>> list(
            @Parameter(hidden = true) @RequestParam Map<String, Object> indicatorImportHistory, Query query) {
        IPage<IndicatorImportHistoryEntity> pages = indicatorImportHistoryService.page(Condition.getPage(query),
                Condition.getQueryWrapper(indicatorImportHistory, IndicatorImportHistoryEntity.class));
        return R.data(IndicatorImportHistoryWrapper.build().pageVO(pages));
    }

    /**
     * 指标导入历史记录 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入indicatorImportHistory")
    public R<IPage<IndicatorImportHistoryVO>> page(IndicatorImportHistoryVO indicatorImportHistory, Query query) {
        IPage<IndicatorImportHistoryVO> pages = indicatorImportHistoryService
                .selectIndicatorImportHistoryPage(Condition.getPage(query), indicatorImportHistory);
        return R.data(pages);
    }

    /**
     * 根据搜索条件分页查询导入历史记录
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "搜索分页", description = "根据搜索条件分页查询导入历史记录")
    public R<IPage<IndicatorImportHistoryVO>> search(
            @Parameter(description = "搜索关键词（文件名、模板类型等）") @RequestParam(required = false) String searchKeyword,
            @Parameter(description = "导入类型") @RequestParam(required = false) String importType,
            @Parameter(description = "模板类型") @RequestParam(required = false) String templateType,
            @Parameter(description = "导入状态") @RequestParam(required = false) String importStatus,
            @Parameter(description = "数据期间") @RequestParam(required = false) String period,
            Query query) {
        
        IPage<IndicatorImportHistoryVO> pages = indicatorImportHistoryService
                .selectIndicatorImportHistoryPageByCondition(
                    Condition.getPage(query), 
                    searchKeyword, 
                    importType, 
                    templateType, 
                    importStatus, 
                    period
                );
        return R.data(pages);
    }

    /**
     * 指标导入历史记录 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "新增", description = "传入indicatorImportHistory")
    public R save(@Valid @RequestBody IndicatorImportHistoryEntity indicatorImportHistory) {
        return R.status(indicatorImportHistoryService.save(indicatorImportHistory));
    }

    /**
     * 指标导入历史记录 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "修改", description = "传入indicatorImportHistory")
    public R update(@Valid @RequestBody IndicatorImportHistoryEntity indicatorImportHistory) {
        return R.status(indicatorImportHistoryService.updateById(indicatorImportHistory));
    }

    /**
     * 指标导入历史记录 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "新增或修改", description = "传入indicatorImportHistory")
    public R submit(@Valid @RequestBody IndicatorImportHistoryEntity indicatorImportHistory) {
        return R.status(indicatorImportHistoryService.saveOrUpdate(indicatorImportHistory));
    }

    /**
     * 指标导入历史记录 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(indicatorImportHistoryService.deleteLogic(Func.toLongList(ids)));
    }

}
