package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.pojo.entity.IndicatorEntity;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity;
import org.springblade.modules.yjzb.service.IIndicatorImportService;
import org.springblade.modules.yjzb.service.IIndicatorService;
import org.springblade.modules.yjzb.service.IIndicatorValuesService;
import org.springblade.modules.yjzb.service.IIndicatorImportHistoryService;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 指标数据导入服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class IndicatorImportServiceImpl implements IIndicatorImportService {

    private final IIndicatorService indicatorService;
    private final IIndicatorValuesService indicatorValuesService;
    private final IIndicatorImportHistoryService indicatorImportHistoryService;

    private static final Pattern INDICATOR_NAME_PATTERN = Pattern.compile("\\{([^}]+)\\}");

    // 批量处理配置
    private static final int DEFAULT_BATCH_SIZE = 1000; // 默认每批处理1000条记录
    private static final int MAX_BATCH_SIZE = 5000;     // 最大批量大小

    @Override
    public Map<String, Object> importExcelData(MultipartFile file, String templateType, String period) {
        long startTime = System.currentTimeMillis();
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> notFoundIndicators = new ArrayList<>();
        int successCount = 0;
        int totalCount = 0;

        try {
            // 验证期间格式
            if (Func.isBlank(period) || !period.matches("\\d{4}-\\d{2}")) {
                errors.add("数据期间格式错误，应为YYYY-MM格式");
                result.put("success", false);
                result.put("errors", errors);
                return result;
            }

            // 加载模板映射
            Map<String, TemplatePosition> templateMapping = loadTemplateMapping(templateType);
            if (templateMapping.isEmpty()) {
                errors.add("无法加载模板映射信息");
                result.put("success", false);
                result.put("errors", errors);
                return result;
            }

            // 预加载指标名称到ID的映射
            Map<String, Long> indicatorNameToIdMap = buildIndicatorNameToIdMap();

            // 解析Excel文件 - 参考FeeExcelImportController的成功实现
            String fileName = file.getOriginalFilename();
            if (fileName != null && fileName.toLowerCase().endsWith(".xlsx")) {
                // 处理.xlsx文件
                try (InputStream in = file.getInputStream(); XSSFWorkbook wb = new XSSFWorkbook(in)) {
                    Sheet sheet = wb.getSheetAt(0);
                    int[] counts = processSheet(sheet, templateMapping, indicatorNameToIdMap, period, notFoundIndicators);
                    totalCount = counts[0];
                    successCount = counts[1];
                }
            } else if (fileName != null && fileName.toLowerCase().endsWith(".xls")) {
                // 处理.xls文件
                try (InputStream in = file.getInputStream(); HSSFWorkbook wb = new HSSFWorkbook(in)) {
                    Sheet sheet = wb.getSheetAt(0);
                    int[] counts = processSheet(sheet, templateMapping, indicatorNameToIdMap, period, notFoundIndicators);
                    totalCount = counts[0];
                    successCount = counts[1];
                }
            } else {
                errors.add("不支持的文件格式，请上传.xlsx或.xls文件");
                result.put("success", false);
                result.put("errors", errors);
                return result;
            }

        } catch (Exception e) {
            log.error("导入Excel数据失败，文件名: {}, 模板类型: {}, 期间: {}",
                    file.getOriginalFilename(), templateType, period, e);

            String errorMessage = "导入失败: ";
            if (e instanceof IOException) {
                errorMessage += "文件读取错误，请检查文件格式是否正确";
            } else if (e.getMessage() != null && e.getMessage().contains("NoSuchMethodError")) {
                errorMessage += "系统组件版本不兼容，请联系管理员";
            } else {
                errorMessage += e.getMessage();
            }
            errors.add(errorMessage);
        }

        result.put("success", errors.isEmpty());
        result.put("totalCount", totalCount);
        result.put("successCount", successCount);
        result.put("failCount", totalCount - successCount);
        result.put("errors", errors);
        result.put("notFoundIndicators", notFoundIndicators);

        // 记录导入历史
        long endTime = System.currentTimeMillis();
        long importDuration = endTime - startTime;

        try {
            String fileName = file.getOriginalFilename();
            Long fileSize = file.getSize();

            indicatorImportHistoryService.recordImportHistory(
                "excel",
                templateType,
                fileName,
                fileSize,
                period,
                result,
                importDuration
            );
        } catch (Exception e) {
            log.warn("记录导入历史失败", e);
        }

        return result;
    }

    /**
     * 加载模板映射关系，返回指标名称和其在模板中的位置信息
     */
    private Map<String, TemplatePosition> loadTemplateMapping(String templateType) {
        Map<String, TemplatePosition> mapping = new HashMap<>();
        String templateFileName = getTemplateFileName(templateType);

        if (templateFileName == null) {
            return mapping;
        }

        try {
            ClassPathResource resource = new ClassPathResource("templates/" + templateFileName);
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {

                String line;
                int rowIndex = 0;

                while ((line = reader.readLine()) != null) {
                    if (rowIndex == 0) {
                        rowIndex++;
                        continue; // 跳过标题行
                    }

                    String[] columns = line.split(",");
                    // 查找包含指标名称的列（通常在"公式列"）
                    for (int colIndex = 0; colIndex < columns.length; colIndex++) {
                        String cellValue = columns[colIndex].trim();
                        Matcher matcher = INDICATOR_NAME_PATTERN.matcher(cellValue);
                        if (matcher.find()) {
                            String indicatorName = matcher.group(1);
                            // 根据模板类型确定数值列的位置
                            TemplatePosition position = new TemplatePosition(rowIndex, colIndex, indicatorName);
                            mapping.put(indicatorName, position);
                        }
                    }
                    rowIndex++;
                }
            }
        } catch (IOException e) {
            log.error("加载模板文件失败: " + templateFileName, e);
        }

        return mapping;
    }

    /**
     * 模板位置信息类
     */
    private static class TemplatePosition {
        private final int row;
        private final int column;
        private final String indicatorName;

        public TemplatePosition(int row, int column, String indicatorName) {
            this.row = row;
            this.column = column;
            this.indicatorName = indicatorName;
        }

        public int getRow() { return row; }
        public int getColumn() { return column; }
        public String getIndicatorName() { return indicatorName; }
    }

    /**
     * 根据模板类型获取模板文件名
     */
    private String getTemplateFileName(String templateType) {
        switch (templateType) {
            case "profit":
                return "利润表数据模板.csv";
            case "balance":
                return "资产负债表数据模板.csv";
            case "expense":
                return "三项费用数据模板.csv";
            case "tax":
                return "税利指标明细表数据模板.csv";
            default:
                return null;
        }
    }

    /**
     * 处理工作表数据
     * @return int数组，[0]为总数，[1]为成功数
     */
    private int[] processSheet(Sheet sheet, Map<String, TemplatePosition> templateMapping,
                              Map<String, Long> indicatorNameToIdMap, String period,
                              List<String> notFoundIndicators) {
        int totalCount = 0;
        List<IndicatorValueData> indicatorValueDataList = new ArrayList<>();

        // 根据模板映射中的位置信息提取数据
        for (Map.Entry<String, TemplatePosition> entry : templateMapping.entrySet()) {
            String indicatorName = entry.getKey();
            TemplatePosition position = entry.getValue();

            totalCount++;

            // 获取指标ID
            Long indicatorId = indicatorNameToIdMap.get(indicatorName);
            if (indicatorId == null) {
                notFoundIndicators.add("指标未找到: " + indicatorName);
                continue;
            }

            // 根据模板中的位置获取Excel文件中相同位置的数值
            Row row = sheet.getRow(position.getRow());
            if (row != null) {
                BigDecimal value = getNumericCellValue(row, position.getColumn());
                if (value == null) {
                    value = BigDecimal.valueOf(0);
                }
                // 收集数据，准备批量处理
                indicatorValueDataList.add(new IndicatorValueData(indicatorId, period, value));
            } else {
                log.warn("Excel文件中第{}行不存在，指标: {}", position.getRow() + 1, indicatorName);
            }
        }

        // 批量保存或更新指标值
        long startTime = System.currentTimeMillis();
        int successCount = batchUpsertIndicatorValues(indicatorValueDataList, period);
        long endTime = System.currentTimeMillis();

        log.info("批量处理完成 - 总数: {}, 成功: {}, 耗时: {}ms",
                indicatorValueDataList.size(), successCount, (endTime - startTime));

        return new int[]{totalCount, successCount};
    }



    /**
     * 获取数值单元格的值
     */
    private BigDecimal getNumericCellValue(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            return null;
        }

        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    return BigDecimal.valueOf(cell.getNumericCellValue());
                case STRING:
                    String stringValue = cell.getStringCellValue().trim();
                    if (Func.isBlank(stringValue)) {
                        return null;
                    }
                    // 移除千分位分隔符
                    stringValue = stringValue.replace(",", "");
                    return new BigDecimal(stringValue);
                case FORMULA:
                    try {
                        return BigDecimal.valueOf(cell.getNumericCellValue());
                    } catch (Exception e) {
                        String formulaStringValue = cell.getStringCellValue().trim();
                        if (Func.isBlank(formulaStringValue)) {
                            return null;
                        }
                        formulaStringValue = formulaStringValue.replace(",", "");
                        return new BigDecimal(formulaStringValue);
                    }
                default:
                    return null;
            }
        } catch (Exception e) {
            log.warn("解析单元格数值失败，行: {}, 列: {}", row.getRowNum(), columnIndex, e);
            return null;
        }
    }

    /**
     * 构建指标名称到ID的映射
     */
    private Map<String, Long> buildIndicatorNameToIdMap() {
        Map<String, Long> map = new HashMap<>();
        List<IndicatorEntity> indicators = indicatorService.list(new QueryWrapper<>());
        for (IndicatorEntity indicator : indicators) {
            if (Func.isNotBlank(indicator.getName()) && indicator.getId() != null) {
                map.put(indicator.getName(), indicator.getId());
            }
        }
        return map;
    }

    /**
     * 批量保存或更新指标值
     */
    private int batchUpsertIndicatorValues(List<IndicatorValueData> dataList, String period) {
        if (dataList.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        final int BATCH_SIZE = Math.min(DEFAULT_BATCH_SIZE, MAX_BATCH_SIZE); // 使用配置的批量大小

        // 分批处理，避免一次性处理过多数据
        for (int i = 0; i < dataList.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, dataList.size());
            List<IndicatorValueData> batchData = dataList.subList(i, endIndex);
            successCount += processBatch(batchData, period);
            log.debug("已处理批次 {}/{}, 当前成功数: {}",
                     (i / BATCH_SIZE + 1), (dataList.size() + BATCH_SIZE - 1) / BATCH_SIZE, successCount);
        }

        return successCount;
    }

    /**
     * 处理单个批次的数据
     */
    @Transactional(rollbackFor = Exception.class)
    private int processBatch(List<IndicatorValueData> batchData, String period) {
        int batchSuccessCount = 0;

        try {
            // 1. 批量查询已存在的记录
            List<Long> indicatorIds = batchData.stream()
                    .map(IndicatorValueData::getIndicatorId)
                    .distinct()
                    .collect(Collectors.toList());

            QueryWrapper<IndicatorValuesEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("indicator_id", indicatorIds)
                       .eq("period", period);
            List<IndicatorValuesEntity> existingEntities = indicatorValuesService.list(queryWrapper);

            // 2. 构建已存在记录的映射 (indicatorId -> entity)
            Map<Long, IndicatorValuesEntity> existingMap = existingEntities.stream()
                    .collect(Collectors.toMap(IndicatorValuesEntity::getIndicatorId, entity -> entity));

            // 3. 分离新增和更新的数据
            List<IndicatorValuesEntity> toInsert = new ArrayList<>();
            List<IndicatorValuesEntity> toUpdate = new ArrayList<>();

            for (IndicatorValueData data : batchData) {
                IndicatorValuesEntity existing = existingMap.get(data.getIndicatorId());

                if (existing == null) {
                    // 新增
                    IndicatorValuesEntity newEntity = new IndicatorValuesEntity();
                    newEntity.setIndicatorId(data.getIndicatorId());
                    newEntity.setPeriod(period);
                    newEntity.setValue(data.getValue());
                    newEntity.setDataSource("excel-import");
                    newEntity.setCalculationStatus("COMPLETED");
                    toInsert.add(newEntity);
                } else {
                    // 更新
                    existing.setValue(data.getValue());
                    existing.setDataSource("excel-import");
                    existing.setCalculationStatus("COMPLETED");
                    toUpdate.add(existing);
                }
            }

            // 4. 批量执行新增
            if (!toInsert.isEmpty()) {
                boolean insertResult = indicatorValuesService.saveBatch(toInsert);
                if (insertResult) {
                    batchSuccessCount += toInsert.size();
                    log.debug("批量新增指标值成功，数量: {}", toInsert.size());
                } else {
                    log.error("批量新增指标值失败");
                }
            }

            // 5. 批量执行更新
            if (!toUpdate.isEmpty()) {
                boolean updateResult = indicatorValuesService.updateBatchById(toUpdate);
                if (updateResult) {
                    batchSuccessCount += toUpdate.size();
                    log.debug("批量更新指标值成功，数量: {}", toUpdate.size());
                } else {
                    log.error("批量更新指标值失败");
                }
            }

        } catch (Exception e) {
            log.error("批量处理指标值失败", e);
        }

        return batchSuccessCount;
    }

    /**
     * 指标值数据传输对象
     */
    private static class IndicatorValueData {
        private final Long indicatorId;
        private final String period;
        private final BigDecimal value;

        public IndicatorValueData(Long indicatorId, String period, BigDecimal value) {
            this.indicatorId = indicatorId;
            this.period = period;
            this.value = value;
        }

        public Long getIndicatorId() { return indicatorId; }
        public String getPeriod() { return period; }
        public BigDecimal getValue() { return value; }
    }

    /**
     * 保存或更新指标值 (单条记录，已废弃，改用批量处理)
     * @deprecated 使用 batchUpsertIndicatorValues 替代
     */
    @Deprecated
    private void upsertIndicatorValue(Long indicatorId, String period, BigDecimal value) {
        IndicatorValuesEntity query = new IndicatorValuesEntity();
        query.setIndicatorId(indicatorId);
        query.setPeriod(period);

        IndicatorValuesEntity existing = indicatorValuesService.getOne(new QueryWrapper<>(query));

        if (existing == null) {
            // 新增
            IndicatorValuesEntity newEntity = new IndicatorValuesEntity();
            newEntity.setIndicatorId(indicatorId);
            newEntity.setPeriod(period);
            newEntity.setValue(value);
            newEntity.setDataSource("excel-import");
            newEntity.setCalculationStatus("COMPLETED");
            indicatorValuesService.save(newEntity);
        } else {
            // 更新
            existing.setValue(value);
            existing.setDataSource("excel-import");
            existing.setCalculationStatus("COMPLETED");
            indicatorValuesService.updateById(existing);
        }
    }
}
