package org.springblade.modules.yjzb.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

import org.springblade.modules.yjzb.service.IDashboardMetricsService;
import org.springblade.modules.yjzb.service.IIndicatorValuesService;
import org.springblade.modules.yjzb.service.IIndicatorService;
import org.springblade.modules.yjzb.pojo.vo.DashboardMetricsResponseVO;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity;
import org.springblade.modules.yjzb.pojo.entity.IndicatorEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

@Service
@RequiredArgsConstructor
public class DashboardMetricsServiceImpl implements IDashboardMetricsService {

    private final IIndicatorValuesService indicatorValuesService;
    private final IIndicatorService indicatorService;

    // 指标名称常量（需与库中指标名称一致）
    private static final String NAME_MANAGEMENT_EXPENSE = "管理费用";
    private static final String NAME_SELLING_EXPENSE = "销售费用";
    private static final String NAME_MAIN_REVENUE = "主营业务收入";
    private static final String NAME_TOTAL_LIABILITIES = "负债合计";
    private static final String NAME_CURRENT_ASSETS = "流动资产合计";
    private static final String NAME_NONCURRENT_ASSETS = "非流动资产合计";
    private static final String NAME_PROFIT_TOTAL = "利润总额";
    private static final String NAME_NET_PROFIT = "净利润";
    private static final String NAME_EQUITY = "归属于母公司所有者权益（或股东权益）合计";
    private static final String NAME_CIG_COGS = "主营业务成本"; // 卷烟口径需按导入时维度拆分
    private static final String NAME_FINANCIAL_EXPENSE = "财务费用";
    private static final String NAME_INVENTORY = "库存商品（产成品）"; // 或“存货”中的卷烟口径

    @Override
    public DashboardMetricsResponseVO computeMetrics(String period) {
        String month = StringUtils.hasText(period) ? period : currentMonth();
        DashboardMetricsResponseVO rsp = new DashboardMetricsResponseVO();
        rsp.setPeriod(month);

        // 两项费用率
        Map<String, BigDecimal> expFactors = new HashMap<>();
        // 当年累计到目标月份
        BigDecimal management = sumYearToMonth(NAME_MANAGEMENT_EXPENSE, month);
        BigDecimal selling = sumYearToMonth(NAME_SELLING_EXPENSE, month);
        BigDecimal revenue = sumYearToMonth(NAME_MAIN_REVENUE, month);
        expFactors.put(NAME_MANAGEMENT_EXPENSE, nz(management));
        expFactors.put(NAME_SELLING_EXPENSE, nz(selling));
        expFactors.put(NAME_MAIN_REVENUE, nz(revenue));
        BigDecimal expValue = pct(div(safeAdd(management, selling), revenue));
        rsp.setExpenseRate(metric(expValue, expFactors, "(管理费用 + 销售费用) / 主营业务收入 × 100%", "%"));

        // 资产负债率
        Map<String, BigDecimal> debtFactors = new HashMap<>();
        BigDecimal liabilities = findValueByName(month, NAME_TOTAL_LIABILITIES);
        BigDecimal curAssets = findValueByName(month, NAME_CURRENT_ASSETS);
        BigDecimal noncurAssets = findValueByName(month, NAME_NONCURRENT_ASSETS);
        BigDecimal assetSum = safeAdd(curAssets, noncurAssets);
        debtFactors.put(NAME_TOTAL_LIABILITIES, nz(liabilities));
        debtFactors.put(NAME_CURRENT_ASSETS, nz(curAssets));
        debtFactors.put(NAME_NONCURRENT_ASSETS, nz(noncurAssets));
        debtFactors.put("资产合计(计算)", nz(assetSum));
        BigDecimal debtValue = pct(div(liabilities, assetSum));
        rsp.setDebtAssetRatio(metric(debtValue, debtFactors, "负债合计 / (流动资产合计 + 非流动资产合计) × 100%", "%"));

        // 当年利润增长率（以YYYY年度截至目标月份的累计）
        Map<String, BigDecimal> profitFactors = new HashMap<>();
        String year = month.substring(0, 4);
        String lastYear = String.valueOf(Integer.parseInt(year) - 1);
        BigDecimal curYearProfitTotal = sumYearToMonth(NAME_PROFIT_TOTAL, month);
        // 上年同期（累计到同一月份）
        String lastYearSameMonth = lastYear + "-" + month.substring(5, 7);
        BigDecimal lastYearProfitTotal = sumYearToMonth(NAME_PROFIT_TOTAL, lastYearSameMonth);
        profitFactors.put("本年 利润总额(累计)", nz(curYearProfitTotal));
        profitFactors.put("上年 利润总额(累计)", nz(lastYearProfitTotal));
        BigDecimal growthValue = pct(div(curYearProfitTotal.subtract(nz(lastYearProfitTotal)), lastYearProfitTotal));
        rsp.setProfitGrowthRate(metric(growthValue, profitFactors, "(本年利润总额−上年利润总额)/上年利润总额 × 100%", "%"));

        // 国有资产保值增值率（当年累计净利润口径）
        Map<String, BigDecimal> preserveFactors = new HashMap<>();
        BigDecimal openingEquity = findYearOpeningEquity(year);
        BigDecimal curYearNetProfit = sumYearToMonth(NAME_NET_PROFIT, month);
        preserveFactors.put("年初 所有者权益", nz(openingEquity));
        preserveFactors.put("当年 净利润(累计)", nz(curYearNetProfit));
        BigDecimal preserveValue = pct(div(nz(openingEquity).add(nz(curYearNetProfit)), openingEquity));
        rsp.setStateAssetPreservationRate(
                metric(preserveValue, preserveFactors, "(年初所有者权益 + 当年净利润) / 年初所有者权益 × 100%", "%"));

        // 卷烟存货周转率（以百分比展示：周转次数×100）
        Map<String, BigDecimal> invFactors = new HashMap<>();
        // 当年累计的主营业务成本（卷烟口径）
        BigDecimal cogs = sumYearToMonth(NAME_CIG_COGS, month); // 实际上需要维度口径：卷烟
        BigDecimal invEnd = findInventory(month);
        // 期初=上年12月期末（年初）
        String yearBeginPrev = String.valueOf(Integer.parseInt(year) - 1) + "-12";
        BigDecimal invBegin = findInventory(yearBeginPrev);
        BigDecimal avgInv = div(nz(invEnd).add(nz(invBegin)), new BigDecimal("2"));
        invFactors.put("卷烟 主营业务成本", nz(cogs));
        invFactors.put("期初 卷烟存货余额", nz(invBegin));
        invFactors.put("期末 卷烟存货余额", nz(invEnd));
        invFactors.put("平均卷烟库存成本(计算)", nz(avgInv));
        BigDecimal turnoverTimes = div(cogs, avgInv);
        BigDecimal turnoverPct = pct(turnoverTimes); // 百分比化
        rsp.setCigaretteInventoryTurnover(metric(turnoverPct, invFactors, "卷烟主营业务成本 / 平均卷烟库存成本 × 100%", "%"));

        return rsp;
    }

    @Override
    public org.springblade.modules.yjzb.pojo.vo.ExpenseOverviewVO expenseOverview(String period) {
        String month = StringUtils.hasText(period) ? period : currentMonth();
        String year = month.substring(0, 4);
        String mm = month.substring(5, 7);
        String lastMonth = prevMonth(month);
        String lastYearSameMonth = String.valueOf(Integer.parseInt(year) - 1) + "-" + mm;

        BigDecimal cur = sumThreeExpenses(month);
        BigDecimal prev = sumThreeExpenses(lastMonth);
        BigDecimal lastYearSame = sumThreeExpenses(lastYearSameMonth);
        BigDecimal ytd = sumThreeExpensesYearToMonth(month);

        org.springblade.modules.yjzb.pojo.vo.ExpenseOverviewVO vo = new org.springblade.modules.yjzb.pojo.vo.ExpenseOverviewVO();
        vo.setPeriod(month);
        vo.setCurrentMonthTotal(scale(cur));
        vo.setLastMonthTotal(scale(prev));
        vo.setLastYearSameMonthTotal(scale(lastYearSame));
        vo.setYearToDateTotal(scale(ytd));
        return vo;
    }

    private DashboardMetricsResponseVO.Metric metric(BigDecimal value, Map<String, BigDecimal> factors, String formula,
            String unit) {
        DashboardMetricsResponseVO.Metric m = new DashboardMetricsResponseVO.Metric();
        m.setValue(scale(value));
        m.setFactors(factors);
        m.setFormula(formula);
        m.setUnit(unit);
        return m;
    }

    private BigDecimal findValueByName(String period, String indicatorName) {
        // 先通过指标名称精确匹配找到指标ID
        QueryWrapper<IndicatorEntity> indicatorQuery = new QueryWrapper<>();
        indicatorQuery.lambda().eq(IndicatorEntity::getName, indicatorName);
        IndicatorEntity indicator = indicatorService.getOne(indicatorQuery);

        if (indicator == null) {
            return BigDecimal.ZERO;
        }

        // 再通过指标ID和期间查询指标值
        QueryWrapper<IndicatorValuesEntity> valueQuery = new QueryWrapper<>();
        valueQuery.lambda().eq(IndicatorValuesEntity::getIndicatorId, indicator.getId())
                .eq(IndicatorValuesEntity::getPeriod, period);
        IndicatorValuesEntity value = indicatorValuesService.getOne(valueQuery);

        return value == null ? BigDecimal.ZERO : nz(value.getValue());
    }

    private BigDecimal findInventory(String period) {
        // 优先库存商品(产成品)，否则退化到存货，维度中包含“卷烟”更佳
        BigDecimal inv = findValueByName(period, NAME_INVENTORY);
        if (isZero(inv)) {
            inv = findValueByName(period, "存货");
        }
        return inv;
    }

    // 已改为使用 sumYearToMonth，保留全年的方法已删除以避免未使用告警

    /**
     * 累计到指定月份(同年1..M)。入参 period 形如 yyyy-MM
     */
    private BigDecimal sumYearToMonth(String indicatorName, String period) {
        String y = period.substring(0, 4);
        int mm = Integer.parseInt(period.substring(5, 7));
        BigDecimal sum = BigDecimal.ZERO;
        for (int m = 1; m <= mm; m++) {
            String cur = String.format("%s-%02d", y, m);
            sum = sum.add(nz(findValueByName(cur, indicatorName)));
        }
        return sum;
    }

    private BigDecimal findYearOpeningEquity(String year) {
        // 年初=上年12月期末
        String lastYear = String.valueOf(Integer.parseInt(year) - 1);
        String period = lastYear + "-12";
        return findValueByName(period, NAME_EQUITY);
    }

    private BigDecimal sumThreeExpenses(String period) {
        BigDecimal selling = findValueByName(period, NAME_SELLING_EXPENSE);
        BigDecimal management = findValueByName(period, NAME_MANAGEMENT_EXPENSE);
        BigDecimal financial = findValueByName(period, NAME_FINANCIAL_EXPENSE);
        return nz(selling).add(nz(management)).add(nz(financial));
    }

    private BigDecimal sumThreeExpensesYearToMonth(String period) {
        String y = period.substring(0, 4);
        int mm = Integer.parseInt(period.substring(5, 7));
        BigDecimal sum = BigDecimal.ZERO;
        for (int m = 1; m <= mm; m++) {
            String cur = String.format("%s-%02d", y, m);
            sum = sum.add(sumThreeExpenses(cur));
        }
        return sum;
    }

    private BigDecimal nz(BigDecimal v) {
        return v == null ? BigDecimal.ZERO : v;
    }

    private boolean isZero(BigDecimal v) {
        return v == null || v.compareTo(BigDecimal.ZERO) == 0;
    }

    private BigDecimal div(BigDecimal a, BigDecimal b) {
        if (b == null || b.compareTo(BigDecimal.ZERO) == 0)
            return BigDecimal.ZERO;
        if (a == null)
            return BigDecimal.ZERO;
        return a.divide(b, 8, RoundingMode.HALF_UP);
    }

    private BigDecimal pct(BigDecimal v) {
        return v == null ? BigDecimal.ZERO : v.multiply(new BigDecimal("100"));
    }

    private BigDecimal safeAdd(BigDecimal a, BigDecimal b) {
        return nz(a).add(nz(b));
    }

    private BigDecimal scale(BigDecimal v) {
        return v == null ? BigDecimal.ZERO : v.setScale(2, RoundingMode.HALF_UP);
    }

    private String currentMonth() {
        java.time.LocalDate now = java.time.LocalDate.now();
        // 使用公历年，不用周基年，避免年初跨年问题
        return now.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    private String prevMonth(String period) {
        java.time.YearMonth ym = java.time.YearMonth.parse(period);
        java.time.YearMonth prev = ym.minusMonths(1);
        return prev.toString();
    }
}
