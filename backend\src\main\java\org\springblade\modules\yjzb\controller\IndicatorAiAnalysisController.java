/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.IndicatorAiAnalysisEntity;
import org.springblade.modules.yjzb.service.IIndicatorAiAnalysisService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.Func;

import java.util.Map;

/**
 * 指标AI解读分析 控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-08-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/indicatorAiAnalysis")
@Tag(name = "指标AI解读分析", description = "指标AI解读分析接口")
public class IndicatorAiAnalysisController extends BladeController {

    private final IIndicatorAiAnalysisService indicatorAiAnalysisService;

    /**
     * 指标AI解读分析 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入indicatorAiAnalysis")
    public R<IndicatorAiAnalysisEntity> detail(IndicatorAiAnalysisEntity indicatorAiAnalysis) {
        IndicatorAiAnalysisEntity detail = indicatorAiAnalysisService
                .getOne(Condition.getQueryWrapper(indicatorAiAnalysis));
        return R.data(detail);
    }

    /**
     * 指标AI解读分析 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入indicatorAiAnalysis")
    public R<IPage<IndicatorAiAnalysisEntity>> list(@RequestParam Map<String, Object> indicatorAiAnalysis,
            Query query) {
        IPage<IndicatorAiAnalysisEntity> pages = indicatorAiAnalysisService.page(
                Condition.getPage(query),
                Condition.getQueryWrapper(indicatorAiAnalysis, IndicatorAiAnalysisEntity.class));
        return R.data(pages);
    }

    /**
     * 指标AI解读分析 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "新增", description = "传入indicatorAiAnalysis")
    public R<Boolean> save(@Valid @RequestBody IndicatorAiAnalysisEntity indicatorAiAnalysis) {
        return R.status(indicatorAiAnalysisService.save(indicatorAiAnalysis));
    }

    /**
     * 指标AI解读分析 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "修改", description = "传入indicatorAiAnalysis")
    public R<Boolean> update(@Valid @RequestBody IndicatorAiAnalysisEntity indicatorAiAnalysis) {
        return R.status(indicatorAiAnalysisService.updateById(indicatorAiAnalysis));
    }

    /**
     * 指标AI解读分析 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "新增或修改", description = "传入indicatorAiAnalysis")
    public R<Boolean> submit(@Valid @RequestBody IndicatorAiAnalysisEntity indicatorAiAnalysis) {
        return R.status(indicatorAiAnalysisService.saveOrUpdate(indicatorAiAnalysis));
    }

    /**
     * 指标AI解读分析 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R<Boolean> remove(@RequestParam String ids) {
        return R.status(indicatorAiAnalysisService.deleteLogic(Func.toLongList(ids)));
    }
}
