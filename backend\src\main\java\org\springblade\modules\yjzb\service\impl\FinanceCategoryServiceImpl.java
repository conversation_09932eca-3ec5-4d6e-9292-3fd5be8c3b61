/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.mapper.FinanceCategoryMapper;
import org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity;
import org.springblade.modules.yjzb.pojo.dto.FinanceCategoryDTO;
import org.springblade.modules.yjzb.pojo.vo.FinanceCategoryVO;
import org.springblade.modules.yjzb.service.IFinanceCategoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 知识分类 服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceCategoryServiceImpl extends BaseServiceImpl<FinanceCategoryMapper, FinanceCategoryEntity> implements IFinanceCategoryService {

    private final ObjectMapper objectMapper;

    @Override
    public IPage<FinanceCategoryVO> selectFinanceCategoryPage(IPage<FinanceCategoryVO> page, FinanceCategoryVO financeCategory) {
        return baseMapper.selectFinanceCategoryPage(page, financeCategory);
    }

    @Override
    public FinanceCategoryVO getFinanceCategoryById(Long id) {
        FinanceCategoryVO vo = baseMapper.selectFinanceCategoryById(id);
        if (vo != null && Func.isNotBlank(vo.getCategoryTags())) {
            try {
                List<String> tagList = objectMapper.readValue(vo.getCategoryTags(), new TypeReference<List<String>>() {});
                vo.setTagList(tagList);
            } catch (Exception e) {
                log.error("解析分类标签失败", e);
            }
        }
        return vo;
    }

    @Override
    public List<FinanceCategoryVO> getCategoryTree(Long knowledgeId) {
        List<FinanceCategoryVO> allCategories = baseMapper.selectCategoryTree(knowledgeId);
        return buildTree(allCategories);
    }

    @Override
    public List<FinanceCategoryVO> getChildrenByParentId(Long parentId) {
        return baseMapper.selectChildrenByParentId(parentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFinanceCategory(FinanceCategoryDTO financeCategoryDTO) {
        FinanceCategoryEntity entity = new FinanceCategoryEntity();
        BeanUtils.copyProperties(financeCategoryDTO, entity);
        
        // 处理标签
        if (financeCategoryDTO.getTagList() != null && !financeCategoryDTO.getTagList().isEmpty()) {
            try {
                entity.setCategoryTags(objectMapper.writeValueAsString(financeCategoryDTO.getTagList()));
            } catch (Exception e) {
                log.error("序列化分类标签失败", e);
            }
        }
        
        // 构建完整路径
        String fullPath = buildFullPath(entity.getParentId(), entity.getCategoryName());
        entity.setFullPath(fullPath);
        
        // 设置层级深度
        if (entity.getParentId() == null || entity.getParentId() == 0) {
            entity.setLevelNum(1);
        } else {
            FinanceCategoryEntity parent = getById(entity.getParentId());
            if (parent != null) {
                entity.setLevelNum(parent.getLevelNum() + 1);
            }
        }
        
        return saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFinanceCategory(FinanceCategoryDTO financeCategoryDTO) {
        FinanceCategoryEntity entity = new FinanceCategoryEntity();
        BeanUtils.copyProperties(financeCategoryDTO, entity);
        
        // 处理标签
        if (financeCategoryDTO.getTagList() != null && !financeCategoryDTO.getTagList().isEmpty()) {
            try {
                entity.setCategoryTags(objectMapper.writeValueAsString(financeCategoryDTO.getTagList()));
            } catch (Exception e) {
                log.error("序列化分类标签失败", e);
            }
        }
        
        // 构建完整路径
        String fullPath = buildFullPath(entity.getParentId(), entity.getCategoryName());
        entity.setFullPath(fullPath);
        
        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFinanceCategory(String ids) {
        return removeByIds(Func.toLongList(ids));
    }

    @Override
    public String buildFullPath(Long parentId, String categoryName) {
        if (parentId == null || parentId == 0) {
            return "/" + categoryName;
        }
        
        FinanceCategoryEntity parent = getById(parentId);
        if (parent != null) {
            return parent.getFullPath() + "/" + categoryName;
        }
        
        return "/" + categoryName;
    }

    @Override
    public Integer countDocumentsByCategory(Long categoryId) {
        return baseMapper.countDocumentsByCategory(categoryId);
    }

    /**
     * 构建树形结构
     */
    private List<FinanceCategoryVO> buildTree(List<FinanceCategoryVO> allCategories) {
        Map<Long, List<FinanceCategoryVO>> parentMap = allCategories.stream()
            .collect(Collectors.groupingBy(FinanceCategoryVO::getParentId));
        
        List<FinanceCategoryVO> rootNodes = parentMap.getOrDefault(0L, new ArrayList<>());
        
        for (FinanceCategoryVO node : rootNodes) {
            buildChildren(node, parentMap);
        }
        
        return rootNodes;
    }

    /**
     * 递归构建子节点
     */
    private void buildChildren(FinanceCategoryVO parent, Map<Long, List<FinanceCategoryVO>> parentMap) {
        List<FinanceCategoryVO> children = parentMap.get(parent.getId());
        if (children != null && !children.isEmpty()) {
            parent.setChildren(children);
            for (FinanceCategoryVO child : children) {
                buildChildren(child, parentMap);
            }
        }
    }
}
