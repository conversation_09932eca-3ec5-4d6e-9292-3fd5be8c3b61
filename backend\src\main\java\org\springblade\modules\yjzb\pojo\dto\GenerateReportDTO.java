/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 生成报告请求对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "生成报告请求对象")
public class GenerateReportDTO {

    /**
     * 报告标题
     */
    @NotBlank(message = "报告标题不能为空")
    @Schema(description = "报告标题", required = true)
    private String title;

    /**
     * 报告类型
     */
    @NotBlank(message = "报告类型不能为空")
    @Schema(description = "报告类型", required = true)
    private String type;

    /**
     * 查询年份
     */
    @NotNull(message = "查询年份不能为空")
    @Schema(description = "查询年份", required = true)
    private Integer queryYear;

    /**
     * 对比年份
     */
    @NotNull(message = "对比年份不能为空")
    @Schema(description = "对比年份", required = true)
    private Integer compareYear;

    /**
     * 开始月份
     */
    @NotNull(message = "开始月份不能为空")
    @Schema(description = "开始月份", required = true)
    private Integer startMonth;

    /**
     * 结束月份
     */
    @NotNull(message = "结束月份不能为空")
    @Schema(description = "结束月份", required = true)
    private Integer endMonth;
}
