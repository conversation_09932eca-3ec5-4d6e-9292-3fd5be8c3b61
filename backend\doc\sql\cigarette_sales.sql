-- 创建卷烟销量表
CREATE TABLE IF NOT EXISTS "yjzb_cigarette_sales" (
  "id" BIGINT NOT NULL,
  "period" VARCHAR(20) DEFAULT NULL,
  "sales" NUMERIC(10,2) DEFAULT NULL,
  "create_user" BIGINT DEFAULT NULL,
  "create_dept" BIGINT DEFAULT NULL,
  "create_time" TIMESTAMP DEFAULT NULL,
  "update_user" BIGINT DEFAULT NULL,
  "update_time" TIMESTAMP DEFAULT NULL,
  "status" INTEGER DEFAULT NULL,
  "is_deleted" INTEGER DEFAULT 0,
  "remark" VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY ("id")
);

-- 添加表注释
COMMENT ON TABLE "yjzb_cigarette_sales" IS '卷烟销量表';

-- 添加字段注释
COMMENT ON COLUMN "yjzb_cigarette_sales"."id" IS '主键';
COMMENT ON COLUMN "yjzb_cigarette_sales"."period" IS '数据期间（格式：YYYY-MM）';
COMMENT ON COLUMN "yjzb_cigarette_sales"."sales" IS '销量（箱）';
COMMENT ON COLUMN "yjzb_cigarette_sales"."create_user" IS '创建人';
COMMENT ON COLUMN "yjzb_cigarette_sales"."create_dept" IS '创建部门';
COMMENT ON COLUMN "yjzb_cigarette_sales"."create_time" IS '创建时间';
COMMENT ON COLUMN "yjzb_cigarette_sales"."update_user" IS '修改人';
COMMENT ON COLUMN "yjzb_cigarette_sales"."update_time" IS '修改时间';
COMMENT ON COLUMN "yjzb_cigarette_sales"."status" IS '状态';
COMMENT ON COLUMN "yjzb_cigarette_sales"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "yjzb_cigarette_sales"."remark" IS '备注';



INSERT INTO "public"."yjzb_cigarette_sales" (
  "id", "period", "sales", "create_user", "create_dept",
  "create_time", "update_user", "update_time", "status",
  "is_deleted", "remark"
) VALUES
(1953000000000000001, '2025-01', 13659, 1123598821738675201, 1123598813738675201, '2025-01-31 00:00:00', 1123598821738675201, '2025-01-31 00:00:00', 1, 0, NULL),
(1953000000000000002, '2025-02', 5195, 1123598821738675201, 1123598813738675201, '2025-02-28 00:00:00', 1123598821738675201, '2025-02-28 00:00:00', 1, 0, NULL),
(1953000000000000003, '2025-03', 7799, 1123598821738675201, 1123598813738675201, '2025-03-31 00:00:00', 1123598821738675201, '2025-03-31 00:00:00', 1, 0, NULL),
(1953000000000000004, '2025-04', 6151, 1123598821738675201, 1123598813738675201, '2025-04-30 00:00:00', 1123598821738675201, '2025-04-30 00:00:00', 1, 0, NULL),
(1953000000000000005, '2025-05', 6644, 1123598821738675201, 1123598813738675201, '2025-05-31 00:00:00', 1123598821738675201, '2025-05-31 00:00:00', 1, 0, NULL),
(1953000000000000006, '2025-06', 6940.84, 1123598821738675201, 1123598813738675201, '2025-06-30 00:00:00', 1123598821738675201, '2025-06-30 00:00:00', 1, 0, NULL),
(1953000000000000007, '2025-07', 7968.61, 1123598821738675201, 1123598813738675201, '2025-07-31 00:00:00', 1123598821738675201, '2025-07-31 00:00:00', 1, 0, NULL);