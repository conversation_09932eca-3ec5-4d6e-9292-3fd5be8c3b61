import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/yjzb/indicator/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/yjzb/indicator/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/yjzb/indicator/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/yjzb/indicator/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/yjzb/indicator/submit',
    method: 'post',
    data: row
  })
}

// 下载数据导入模板
export const downloadTemplate = (templateType) => {
  return request({
    url: `/yjzb/indicator/download-template/${templateType}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 获取指标列表（用于选择器）
export const getIndicatorList = (current, size, params) => {
  return request({
    url: '/yjzb/indicator/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

