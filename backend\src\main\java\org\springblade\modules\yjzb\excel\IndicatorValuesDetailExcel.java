/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.excel;

import lombok.Data;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnore;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 指标数据明细 Excel 导入导出
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
public class IndicatorValuesDetailExcel {

    /**
     * 指标类型ID
     */
    @ExcelProperty("指标类型ID")
    private Long indicatorId;

    /**
     * 数据期间
     */
    @ExcelProperty("数据期间")
    private String period;

    /**
     * 凭证号
     */
    @ExcelProperty("凭证号")
    private String voucherNo;

    /**
     * 数据摘要
     */
    @ExcelProperty("数据摘要")
    private String dataSummary;

    /**
     * 金额
     */
    @ExcelProperty("金额")
    private BigDecimal amount;

    /**
     * 分类
     */
    @ExcelProperty("分类")
    private String category;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty("更新时间")
    private Date updateTime;

}
