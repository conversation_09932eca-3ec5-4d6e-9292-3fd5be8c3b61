#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查询yjzb_indicator_values表中period大于等于2025-01的数据，并遍历调用restartAnalysisForIndicatorValue方法
"""

import os
import psycopg2
import pandas as pd
import requests
import json
from datetime import datetime
import csv
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'gzyc-nlp-2024.rwlb.rds.aliyuncs.com'),
    'port': os.getenv('DB_PORT', '1921'),
    'database': os.getenv('DB_NAME', 'yjyc'),
    'user': os.getenv('DB_USER', 'gzyc'),
    'password': os.getenv('DB_PASSWORD', 'gzyc1234')
}

# API配置
API_BASE_URL = os.getenv('API_BASE_URL', 'http://localhost:8088')


def connect_to_db():
    """
    连接到PostgreSQL数据库
    """
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            database=DB_CONFIG['database'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        return conn
    except Exception as e:
        print(f"数据库连接错误: {e}")
        return None


def get_indicator_values(min_period="2025-01"):
    """
    查询yjzb_indicator_values表中period大于等于指定值的数据
    """
    conn = connect_to_db()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        query = """
        SELECT id, indicator_id, period, value, data_source, calculation_status 
        FROM yjzb_indicator_values 
        WHERE period >= %s AND is_deleted = 0
        ORDER BY period, indicator_id
        """
        cursor.execute(query, (min_period,))
        rows = cursor.fetchall()
        
        # 转换为DataFrame
        df = pd.DataFrame(rows, columns=['id', 'indicator_id', 'period', 'value', 'data_source', 'calculation_status'])
        return df
    except Exception as e:
        print(f"查询指标数据错误: {e}")
        return None
    finally:
        conn.close()


def call_restart_analysis_api(indicator_value_id):
    """
    调用restartAnalysisForIndicatorValue接口
    """
    url = f"{API_BASE_URL}/yjzb/indicatorValues/restart-ai-analysis"
    
    params = {
        'indicatorValueId': indicator_value_id
    }
    
    try:
        response = requests.post(url, params=params)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                return result.get('data')
            else:
                print(f"API调用失败: {result.get('msg')}")
                return None
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"API调用异常: {e}")
        return None


def main():
    # 获取指标数据
    min_period = os.getenv('MIN_PERIOD', '2025-01')
    indicator_values_df = get_indicator_values(min_period)
    
    if indicator_values_df is None or indicator_values_df.empty:
        print(f"未找到period大于等于{min_period}的指标数据")
        return
    
    print(f"找到{len(indicator_values_df)}条指标数据，开始调用AI分析接口...")
    
    # 创建结果CSV文件
    results_file = 'indicator_analysis_results.csv'
    with open(results_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['indicator_value_id', 'indicator_id', 'period', 'value', 'ai_record_id', 'status']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        # 遍历调用API
        for index, row in indicator_values_df.iterrows():
            indicator_value_id = row['id']
            print(f"处理指标数据ID: {indicator_value_id}, 指标ID: {row['indicator_id']}, 期间: {row['period']}")
            
            ai_record_id = call_restart_analysis_api(indicator_value_id)
            status = "成功" if ai_record_id else "失败"
            
            # 写入结果
            writer.writerow({
                'indicator_value_id': indicator_value_id,
                'indicator_id': row['indicator_id'],
                'period': row['period'],
                'value': row['value'],
                'ai_record_id': ai_record_id if ai_record_id else '',
                'status': status
            })
            
            # 刷新文件，确保实时写入
            csvfile.flush()
    
    print(f"处理完成，结果已保存到{results_file}")


if __name__ == "__main__":
    main()