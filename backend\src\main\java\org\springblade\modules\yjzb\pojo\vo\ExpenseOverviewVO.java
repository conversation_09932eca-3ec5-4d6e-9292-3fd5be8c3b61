package org.springblade.modules.yjzb.pojo.vo;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class ExpenseOverviewVO {
    /** 查询的基准期间，格式 yyyy-MM */
    private String period;
    /** 当月合计（销售费用+管理费用+财务费用） */
    private BigDecimal currentMonthTotal;
    /** 上月合计（销售费用+管理费用+财务费用） */
    private BigDecimal lastMonthTotal;
    /** 去年同期合计（销售费用+管理费用+财务费用） */
    private BigDecimal lastYearSameMonthTotal;
    /** 当年累计合计（1..M 销售费用+管理费用+财务费用） */
    private BigDecimal yearToDateTotal;
}
