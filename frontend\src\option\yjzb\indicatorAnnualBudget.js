export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键ID",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "指标ID（关联指标表）",
      prop: "indicatorId",
      type: "input",
      search: true,
    },
    {
      label: "指标名称（为历史保留冗余）",
      prop: "indicatorName",
      type: "input",
    },
    {
      label: "年份（YYYY）",
      prop: "year",
      type: "input",
      search: true,
    },
    {
      label: "年初预算数",
      prop: "initialBudget",
      type: "input",
    },
    {
      label: "年初报备数",
      prop: "initialReported",
      type: "input",
    },
    {
      label: "中期调整预算数",
      prop: "midyearBudget",
      type: "input",
    },
    {
      label: "中期调整报备数",
      prop: "midyearReported",
      type: "input",
    },
    {
      label: "当前使用数",
      prop: "currentUsed",
      type: "input",
    },
    {
      label: "创建人ID",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门ID",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人ID",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "数据状态（1-正常，0-禁用）",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "删除标记（0-未删除，1-已删除）",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
