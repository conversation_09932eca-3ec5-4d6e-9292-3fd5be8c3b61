package org.springblade.modules.yjzb.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * 办公费用AI分析配置类
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
@Configuration
public class ExpenseAiAnalysisConfig {

    /**
     * 提供ScheduledExecutorService Bean用于AI分析轮询
     */
    @Bean("expenseAiAnalysisScheduler")
    public ScheduledExecutorService expenseAiAnalysisScheduler() {
        return Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "expense-ai-analysis-scheduler");
            t.setDaemon(true);
            return t;
        });
    }
}
