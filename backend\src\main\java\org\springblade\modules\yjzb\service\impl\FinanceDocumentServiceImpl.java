/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.oss.OssTemplate;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.mapper.FinanceDocumentMapper;
import org.springblade.modules.yjzb.pojo.dto.FinanceDocumentDTO;
import org.springblade.modules.yjzb.pojo.entity.FinanceDocumentEntity;
import org.springblade.modules.yjzb.pojo.entity.FinanceKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceDocumentVO;
import org.springblade.modules.yjzb.service.IDifyService;
import org.springblade.modules.yjzb.service.IFinanceDocumentService;
import org.springblade.modules.yjzb.service.IFinanceKnowledgeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

/**
 * 知识库文件 服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceDocumentServiceImpl extends BaseServiceImpl<FinanceDocumentMapper, FinanceDocumentEntity> implements IFinanceDocumentService {

    private final ObjectMapper objectMapper;
    private final OssTemplate ossTemplate;
    private final IDifyService difyService;
    private final IFinanceKnowledgeService financeKnowledgeService;
    private final RestTemplate restTemplate;

    @Value("${oss.bucket-name:}")
    private String bucketName;

    @Value("${oss.endpoint:}")
    private String minioEndpoint;

    @Value("${oss.access-key:}")
    private String minioAccessKey;

    @Value("${oss.secret-key:}")
    private String minioSecretKey;

    @Override
    public IPage<FinanceDocumentVO> selectFinanceDocumentPage(IPage<FinanceDocumentVO> page, FinanceDocumentVO financeDocument) {
        IPage<FinanceDocumentVO> result = baseMapper.selectFinanceDocumentPage(page, financeDocument);
        // 处理标签和文件大小格式化
        result.getRecords().forEach(this::processDocumentVO);
        return result;
    }

    @Override
    public FinanceDocumentVO getFinanceDocumentById(Long id) {
        FinanceDocumentVO vo = baseMapper.selectFinanceDocumentById(id);
        if (vo != null) {
            processDocumentVO(vo);
        }
        return vo;
    }

    @Override
    public List<FinanceDocumentVO> getDocumentsByCategory(Long categoryId) {
        List<FinanceDocumentVO> documents = baseMapper.selectDocumentsByCategory(categoryId);
        documents.forEach(this::processDocumentVO);
        return documents;
    }

    @Override
    public List<FinanceDocumentVO> getDocumentsByKnowledge(Long knowledgeId) {
        List<FinanceDocumentVO> documents = baseMapper.selectDocumentsByKnowledge(knowledgeId);
        documents.forEach(this::processDocumentVO);
        return documents;
    }

    @Override
    public List<FinanceDocumentVO> searchDocuments(String keyword, Long knowledgeId, Long categoryId, List<String> tags) {
        List<FinanceDocumentVO> documents = baseMapper.searchDocuments(keyword, knowledgeId, categoryId, tags);
        documents.forEach(this::processDocumentVO);
        return documents;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean uploadDocument(FinanceDocumentDTO financeDocumentDTO) {
        try {
            FinanceDocumentEntity entity = new FinanceDocumentEntity();
            BeanUtils.copyProperties(financeDocumentDTO, entity);
            
            // 处理文件上传
            MultipartFile file = financeDocumentDTO.getFile();
            if (file != null && !file.isEmpty()) {
                String filePath = uploadFile(file);
                entity.setFilePath(filePath);
                entity.setFileSize(file.getSize());
                entity.setFileType(file.getContentType());
                entity.setFileExtension(getFileExtension(file.getOriginalFilename()));
                entity.setFileOriginalName(file.getOriginalFilename());
                
                // 如果文件名为空，使用原始文件名
                if (Func.isBlank(entity.getFileName())) {
                    entity.setFileName(file.getOriginalFilename());
                }
            }
            
            // 处理标签
            if (financeDocumentDTO.getTagList() != null && !financeDocumentDTO.getTagList().isEmpty()) {
                try {
                    entity.setFileTags(objectMapper.writeValueAsString(financeDocumentDTO.getTagList()));
                } catch (Exception e) {
                    log.error("序列化文件标签失败", e);
                }
            }
            
            // 设置初始状态
            entity.setUploadStatus(1); // 上传成功
            entity.setSyncStatus(0); // 未同步
            entity.setViewCount(0);
            entity.setDownloadCount(0);

            boolean result = saveOrUpdate(entity);
            
            // 异步同步到Dify
            if (result && Boolean.TRUE.equals(financeDocumentDTO.getSyncDify())) {
                asyncSyncToDify(entity.getId());
            }
            
            return result;
        } catch (Exception e) {
            log.error("上传文档失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDocument(FinanceDocumentDTO financeDocumentDTO) {
        FinanceDocumentEntity entity = new FinanceDocumentEntity();
        BeanUtils.copyProperties(financeDocumentDTO, entity);
        
        // 处理标签
        if (financeDocumentDTO.getTagList() != null && !financeDocumentDTO.getTagList().isEmpty()) {
            try {
                entity.setFileTags(objectMapper.writeValueAsString(financeDocumentDTO.getTagList()));
            } catch (Exception e) {
                log.error("序列化文件标签失败", e);
            }
        }
        
        boolean result = updateById(entity);
        
        // 异步同步到Dify
        if (result && Boolean.TRUE.equals(financeDocumentDTO.getSyncDify())) {
            asyncSyncToDify(entity.getId());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDocument(String ids) {
        List<Long> idList = Func.toLongList(ids);
        if (idList.isEmpty()) {
            return false;
        }

        // 获取要删除的文档列表
        List<FinanceDocumentEntity> documentsToDelete = listByIds(idList);
        if (documentsToDelete.isEmpty()) {
            return false;
        }

        // 先从Dify删除已同步的文档
        for (FinanceDocumentEntity document : documentsToDelete) {
            if (Func.isNotBlank(document.getDocumentId())) {
                try {
                    // 获取知识库信息
                    FinanceKnowledgeEntity knowledge = financeKnowledgeService.getById(document.getKnowledgeId());
                    if (knowledge != null && Func.isNotBlank(knowledge.getDatasetId())) {
                        // 从Dify删除文档
                        boolean difyDeleted = difyService.deleteDocument(knowledge.getDatasetId(), document.getDocumentId());
                        if (difyDeleted) {
                            log.info("成功从Dify删除文档，datasetId: {}, documentId: {}",
                                    knowledge.getDatasetId(), document.getDocumentId());
                        } else {
                            log.warn("从Dify删除文档失败，datasetId: {}, documentId: {}",
                                    knowledge.getDatasetId(), document.getDocumentId());
                        }
                    }
                } catch (Exception e) {
                    log.error("从Dify删除文档异常，documentId: {}, error: {}",
                            document.getDocumentId(), e.getMessage(), e);
                    // 不抛出异常，继续删除本地记录
                }
            }
        }

        // 删除本地数据库记录
        return removeByIds(idList);
    }

    @Override
    public String downloadDocument(Long id) {
        FinanceDocumentEntity document = getById(id);
        if (document != null && Func.isNotBlank(document.getFilePath())) {
            // 增加下载次数
            incrementDownloadCount(id);
            return document.getFilePath();
        }
        return null;
    }

    @Override
    public String getDocumentDownloadUrl(Long id) {
        try {
            FinanceDocumentEntity document = getById(id);
            if (document == null) {
                log.error("文档不存在，ID: {}", id);
                return null;
            }

            // 如果文档已同步到Dify，优先从Dify获取下载链接
            if (Func.isNotBlank(document.getDocumentId())) {
                // 获取知识库信息
                FinanceKnowledgeEntity knowledge = financeKnowledgeService.getById(document.getKnowledgeId());
                if (knowledge != null && Func.isNotBlank(knowledge.getDatasetId())) {
                    // 调用Dify接口获取文件信息
                    String fileInfo = difyService.getDocumentUploadFile(knowledge.getDatasetId(), document.getDocumentId());
                    if (Func.isNotBlank(fileInfo)) {
                        try {
                            JsonNode jsonNode = objectMapper.readTree(fileInfo);
                            if (jsonNode.has("download_url")) {
                                // 增加下载次数
                                incrementDownloadCount(id);
                                return jsonNode.get("download_url").asText();
                            }
                        } catch (Exception e) {
                            log.error("解析Dify文件信息失败", e);
                        }
                    }
                }
            }

            // 如果从Dify获取失败，返回本地文件路径
            if (Func.isNotBlank(document.getFilePath())) {
                // 增加下载次数
                incrementDownloadCount(id);
                return document.getFilePath();
            }

            return null;
        } catch (Exception e) {
            log.error("获取文档下载URL失败，ID: {}", id, e);
            return null;
        }
    }

    @Override
    public void incrementViewCount(Long id) {
        baseMapper.incrementViewCount(id);
    }

    @Override
    public void incrementDownloadCount(Long id) {
        baseMapper.incrementDownloadCount(id);
    }

    @Override
    public boolean syncToDify(Long id) {
        try {
            FinanceDocumentEntity document = getById(id);
            if (document == null) {
                log.error("文档不存在，ID: {}", id);
                return false;
            }

            // 获取知识库信息
            FinanceKnowledgeEntity knowledge = financeKnowledgeService.getById(document.getKnowledgeId());
            if (knowledge == null || Func.isBlank(knowledge.getDatasetId())) {
                log.error("知识库不存在或未同步到Dify，知识库ID: {}", document.getKnowledgeId());
                return false;
            }

            String documentId;
            if (Func.isBlank(document.getDocumentId())) {
                // 上传新文档
                documentId = difyService.uploadDocumentByPath(
                    knowledge.getDatasetId(),
                    document.getFilePath(),
                    document.getFileName()
                );
                if (Func.isNotBlank(documentId)) {
                    document.setDocumentId(documentId);
                    document.setSyncStatus(1); // 同步成功
                    updateById(document);
                }
            } else {
                // 更新现有文档
                documentId = document.getDocumentId();
                boolean success = difyService.updateDocument(
                        knowledge.getDatasetId(),
                        documentId,
                        document.getFileName()
                );
                if (success) {
                    document.setSyncStatus(1); // 同步成功
                    updateById(document);
                }
            }

            return Func.isNotBlank(documentId);
        } catch (Exception e) {
            log.error("同步文档到Dify失败，ID: {}", id, e);
            // 更新同步状态为失败
            FinanceDocumentEntity document = getById(id);
            if (document != null) {
                document.setSyncStatus(2); // 同步失败
                document.setSyncErrorMsg(e.getMessage());
                updateById(document);
            }
            return false;
        }
    }

    @Async
    public void asyncSyncToDify(Long id) {
        try {
            syncToDify(id);
        } catch (Exception e) {
            log.error("异步同步文档到Dify失败，ID: {}", id, e);
        }
    }

//    /**
//     * 上传文件
//     */
//    private String uploadFile(MultipartFile file) throws IOException {
//        String originalFilename = file.getOriginalFilename();
//        String extension = getFileExtension(originalFilename);
//        String fileName = UUID.randomUUID().toString() + "." + extension;
//
//        // 使用OSS上传文件
//        String filePath = "finance/documents/" + fileName;
//        ossTemplate.putFile(filePath, file.getInputStream());
//
//        return filePath;
//    }

    private String uploadFile(MultipartFile file) throws Exception {
        String extension = getFileExtension(file.getOriginalFilename());
        String fileName = UUID.randomUUID().toString() + "." + extension;

        // 自定义路径
        String objectName = "finance/documents/" + fileName;

        MinioClient minioClient = getMinioClient();
        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName) // 替换为你的 bucket
                        .object(objectName)
                        .stream(file.getInputStream(), file.getSize(), -1)
                        .contentType(file.getContentType())
                        .build()
        );

        return objectName; // 返回你指定的路径
    }



    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (Func.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 处理文档VO对象
     */
    private void processDocumentVO(FinanceDocumentVO vo) {
        // 处理标签
        if (Func.isNotBlank(vo.getFileTags())) {
            try {
                List<String> tagList = objectMapper.readValue(vo.getFileTags(), new TypeReference<List<String>>() {});
                vo.setTagList(tagList);
            } catch (Exception e) {
                log.error("解析文件标签失败", e);
            }
        }
        
        // 格式化文件大小
        if (vo.getFileSize() != null) {
            vo.setFileSizeFormat(formatFileSize(vo.getFileSize()));
        }
        
        // 设置状态描述
        vo.setUploadStatusDesc(getUploadStatusDesc(vo.getUploadStatus()));
        vo.setSyncStatusDesc(getSyncStatusDesc(vo.getSyncStatus()));
        
        // 设置下载链接
        if (Func.isNotBlank(vo.getFilePath())) {
            vo.setDownloadUrl("/api/finance/document/download/" + vo.getId());
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(Long size) {
        if (size == null || size == 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double fileSize = size.doubleValue();
        
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", fileSize, units[unitIndex]);
    }

    /**
     * 获取上传状态描述
     */
    private String getUploadStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0: return "上传中";
            case 1: return "上传成功";
            case 2: return "上传失败";
            default: return "未知";
        }
    }

    /**
     * 获取同步状态描述
     */
    private String getSyncStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0: return "未同步";
            case 1: return "同步成功";
            case 2: return "同步失败";
            default: return "未知";
        }
    }

    /**
     * 获取MinioClient实例
     */
    private MinioClient getMinioClient() {
        try {
            // 通过OssBuilder获取当前的OSS模板，然后提取MinioClient
            // 这里我们需要通过反射或其他方式获取MinioClient
            // 由于MinioTemplate通常会包含MinioClient，我们可以尝试获取它

            // 简化方案：直接创建MinioClient
            // 从配置中获取Minio连接信息
            return MinioClient.builder()
                    .endpoint(minioEndpoint)
                    .credentials(minioAccessKey, minioSecretKey)
                    .build();
        } catch (Exception e) {
            log.error("创建MinioClient失败", e);
            throw new RuntimeException("无法创建MinioClient", e);
        }
    }

    @Override
    public byte[] downloadDocumentData(Long id) {
        try {
            FinanceDocumentEntity document = getById(id);
            if (document == null) {
                log.error("文档不存在，ID: {}", id);
                return null;
            }

            // 如果文档已同步到Dify，优先从Dify下载
            if (Func.isNotBlank(document.getDocumentId())) {
                // 获取知识库信息
                FinanceKnowledgeEntity knowledge = financeKnowledgeService.getById(document.getKnowledgeId());
                if (knowledge != null && Func.isNotBlank(knowledge.getDatasetId())) {
                    // 先获取Dify文件信息
                    String download_url = difyService.getDocumentUploadFile(knowledge.getDatasetId(), document.getDocumentId());
                    if (Func.isNotBlank(download_url)) {
                        // 从Dify URL下载文件数据
                        byte[] fileData = downloadFromUrl(download_url);
                        if (fileData != null) {
                            // 增加下载次数
                            incrementDownloadCount(id);
                            return fileData;
                        }
                    }
                }
            }

            // 如果从Dify下载失败，尝试从本地存储下载
            if (Func.isNotBlank(document.getFilePath())) {
                byte[] fileData = downloadFromLocalStorage(document.getFilePath());
                if (fileData != null) {
                    // 增加下载次数
                    incrementDownloadCount(id);
                    return fileData;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("下载文档数据失败，ID: {}", id, e);
            return null;
        }
    }

    /**
     * 从URL下载文件数据
     */
    private byte[] downloadFromUrl(String url) {
        try {
            HttpHeaders headers = new HttpHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.GET, entity, byte[].class);

            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            }

            log.warn("从URL下载文件失败，响应状态: {}, URL: {}", response.getStatusCode(), url);
            return null;
        } catch (Exception e) {
            log.error("从URL下载文件异常, URL: {}", url, e);
            return null;
        }
    }

    /**
     * 从本地存储下载文件数据
     */
    private byte[] downloadFromLocalStorage(String filePath) {
        try {
            // 这里需要根据实际的存储方式实现
            // 如果使用MinIO
            if (Func.isNotBlank(minioEndpoint)) {
                return downloadFromMinio(filePath);
            }

            // 如果使用OSS
            if (ossTemplate != null) {
                return downloadFromOss(filePath);
            }

            log.warn("未配置有效的文件存储方式");
            return null;
        } catch (Exception e) {
            log.error("从本地存储下载文件失败，路径: {}", filePath, e);
            return null;
        }
    }

    /**
     * 从MinIO下载文件
     */
    private byte[] downloadFromMinio(String objectName) {
        try {
            MinioClient minioClient = getMinioClient();
            try (var inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build())) {
                return inputStream.readAllBytes();
            }
        } catch (Exception e) {
            log.error("从MinIO下载文件失败，对象名: {}", objectName, e);
            return null;
        }
    }

    /**
     * 从OSS下载文件
     */
    private byte[] downloadFromOss(String filePath) {
        try {
            // 使用OSS模板下载文件
            // 由于OssTemplate可能没有直接返回byte[]的方法，我们使用文件链接方式
            String fileLink = ossTemplate.fileLink(filePath);
            if (Func.isNotBlank(fileLink)) {
                // 通过HTTP下载文件
                return downloadFromUrl(fileLink);
            } else {
                log.warn("无法获取OSS文件链接，路径: {}", filePath);
                return null;
            }
        } catch (Exception e) {
            log.error("从OSS下载文件失败，路径: {}", filePath, e);
            return null;
        }
    }
}
