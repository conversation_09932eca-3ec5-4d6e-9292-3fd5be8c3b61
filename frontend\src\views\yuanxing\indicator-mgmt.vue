<template>
  <!-- 指标管理 - 指标管理页面 -->
  <basic-container>
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <h3>指标管理</h3>
      <div class="header-actions">
        <el-button type="primary" @click="createIndicator">
          <el-icon><plus /></el-icon> 新增指标
        </el-button>
        <el-button @click="importIndicators">
          <el-icon><upload /></el-icon> 批量导入
        </el-button>
        <el-button @click="exportIndicators">
          <el-icon><download /></el-icon> 导出
        </el-button>
        <el-button @click="refreshData">
          <el-icon><refresh /></el-icon> 刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-panel">
      <el-form inline label-width="80px">
        <el-form-item label="指标名称">
          <el-input
            v-model="searchKeyword"
            placeholder="输入指标名称搜索"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="指标类型">
          <el-cascader
            v-model="typeFilter"
            :options="typeOptions"
            :props="{ checkStrictly: true, emitPath: false }"
            placeholder="选择指标类型"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="数据类型">
          <el-select v-model="dataTypeFilter" placeholder="选择数据类型" clearable style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="数值" value="number" />
            <el-option label="百分比" value="percentage" />
            <el-option label="金额" value="amount" />
            <el-option label="比率" value="ratio" />
          </el-select>
        </el-form-item>
        <el-form-item label="计算频率">
          <el-select v-model="frequencyFilter" placeholder="计算频率" clearable style="width: 120px;">
            <el-option label="全部" value="" />
            <el-option label="实时" value="realtime" />
            <el-option label="日" value="daily" />
            <el-option label="周" value="weekly" />
            <el-option label="月" value="monthly" />
            <el-option label="季" value="quarterly" />
            <el-option label="年" value="yearly" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="statusFilter" placeholder="选择状态" clearable style="width: 100px;">
            <el-option label="全部" value="" />
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 指标表格 -->
    <div class="table-container">
      <el-table
        :data="filteredIndicatorList"
        v-loading="loading"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" />
        
        <el-table-column prop="name" label="指标名称" min-width="150" show-overflow-tooltip />
        
        <el-table-column prop="code" label="指标编码" width="140" />
        
        <el-table-column prop="type" label="指标类型" width="120" />
        
        <el-table-column prop="dataType" label="数据类型" width="100">
          <template #default="scope">
            <el-tag :type="getDataTypeTag(scope.row.dataType)" size="small">
              {{ getDataTypeName(scope.row.dataType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="frequency" label="计算频率" width="100">
          <template #default="scope">
            <el-tag :type="getFrequencyTag(scope.row.frequency)" size="small">
              {{ getFrequencyName(scope.row.frequency) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="threshold" label="阈值配置" width="120">
          <template #default="scope">
            <el-link type="primary" @click="configThreshold(scope.row)" v-if="scope.row.hasThreshold">
              已配置
            </el-link>
            <el-link type="info" @click="configThreshold(scope.row)" v-else>
              未配置
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastValue" label="最新值" width="100" align="right">
          <template #default="scope">
            <span v-if="scope.row.lastValue !== null">
              {{ formatValue(scope.row.lastValue, scope.row.dataType, scope.row.unit) }}
            </span>
            <span v-else style="color: #c0c4cc;">-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="toggleStatus(scope.row)"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewIndicator(scope.row)">
              <el-icon><view /></el-icon> 详情
            </el-button>
            <el-button type="text" size="small" @click="editIndicator(scope.row)">
              <el-icon><edit /></el-icon> 编辑
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="deleteIndicator(scope.row)"
              style="color: #f56c6c;"
            >
              <el-icon><delete /></el-icon> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 批量操作栏 -->
      <div class="batch-actions" v-if="selectedIndicators.length > 0">
        <span>已选择 {{ selectedIndicators.length }} 项</span>
        <el-button size="small" @click="batchEnable">批量启用</el-button>
        <el-button size="small" @click="batchDisable">批量禁用</el-button>
        <el-button size="small" @click="batchDelete" type="danger">批量删除</el-button>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑指标对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      destroy-on-close
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-tabs v-model="activeTab" type="card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="指标名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入指标名称" />
            </el-form-item>
            
            <el-form-item label="指标编码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入指标编码" />
              <div class="form-tip">编码必须唯一，建议使用英文字母和数字</div>
            </el-form-item>
            
            <el-form-item label="指标类型" prop="typeId">
              <el-cascader
                v-model="formData.typeId"
                :options="typeOptions"
                :props="{ checkStrictly: true, emitPath: false }"
                placeholder="选择指标类型"
              />
            </el-form-item>
            
            <el-form-item label="数据类型" prop="dataType">
              <el-select v-model="formData.dataType" placeholder="选择数据类型">
                <el-option label="数值" value="number" />
                <el-option label="百分比" value="percentage" />
                <el-option label="金额" value="amount" />
                <el-option label="比率" value="ratio" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="单位" prop="unit">
              <el-input v-model="formData.unit" placeholder="如：元、%、个" style="width: 150px;" />
            </el-form-item>
            
            <el-form-item label="计算频率" prop="frequency">
              <el-select v-model="formData.frequency" placeholder="选择计算频率">
                <el-option label="实时" value="realtime" />
                <el-option label="日" value="daily" />
                <el-option label="周" value="weekly" />
                <el-option label="月" value="monthly" />
                <el-option label="季" value="quarterly" />
                <el-option label="年" value="yearly" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="描述说明" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入指标描述"
              />
            </el-form-item>
            
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-tab-pane>
          
          <!-- 计算配置 -->
          <el-tab-pane label="计算配置" name="calculation">
            <el-form-item label="计算方式" prop="calculationType">
              <el-radio-group v-model="formData.calculationType">
                <el-radio label="formula">公式计算</el-radio>
                <el-radio label="sql">SQL查询</el-radio>
                <el-radio label="api">API接口</el-radio>
                <el-radio label="manual">手工录入</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <!-- 公式计算 -->
            <div v-if="formData.calculationType === 'formula'">
              <el-form-item label="计算公式" prop="formula">
                <el-input
                  v-model="formData.formula"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入计算公式，如：(A + B) / C * 100"
                />
                <div class="form-tip">支持四则运算，可使用其他指标编码作为变量</div>
              </el-form-item>
              
              <el-form-item label="依赖指标">
                <el-select v-model="formData.dependencies" multiple placeholder="选择依赖的指标">
                  <el-option
                    v-for="item in dependencyOptions"
                    :key="item.id"
                    :label="`${item.name}(${item.code})`"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </div>
            
            <!-- SQL查询 -->
            <div v-if="formData.calculationType === 'sql'">
              <el-form-item label="数据源" prop="dataSource">
                <el-select v-model="formData.dataSource" placeholder="选择数据源">
                  <el-option label="财务数据库" value="finance_db" />
                  <el-option label="业务数据库" value="business_db" />
                  <el-option label="数据仓库" value="data_warehouse" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="SQL语句" prop="sqlQuery">
                <el-input
                  v-model="formData.sqlQuery"
                  type="textarea"
                  :rows="6"
                  placeholder="请输入SQL查询语句"
                />
              </el-form-item>
            </div>
            
            <!-- API接口 -->
            <div v-if="formData.calculationType === 'api'">
              <el-form-item label="接口地址" prop="apiUrl">
                <el-input v-model="formData.apiUrl" placeholder="请输入API接口地址" />
              </el-form-item>
              
              <el-form-item label="请求方式" prop="apiMethod">
                <el-select v-model="formData.apiMethod">
                  <el-option label="GET" value="GET" />
                  <el-option label="POST" value="POST" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="请求参数" prop="apiParams">
                <el-input
                  v-model="formData.apiParams"
                  type="textarea"
                  :rows="3"
                  placeholder="JSON格式的请求参数"
                />
              </el-form-item>
            </div>
          </el-tab-pane>
          
          <!-- 阈值配置 -->
          <el-tab-pane label="阈值配置" name="threshold">
            <el-form-item>
              <el-checkbox v-model="formData.hasThreshold">启用阈值预警</el-checkbox>
            </el-form-item>
            
            <div v-if="formData.hasThreshold">
              <el-form-item label="优秀阈值">
                <el-input-number v-model="formData.excellentThreshold" :precision="2" />
                <span class="threshold-tip">达到此值时为优秀状态</span>
              </el-form-item>
              
              <el-form-item label="良好阈值">
                <el-input-number v-model="formData.goodThreshold" :precision="2" />
                <span class="threshold-tip">达到此值时为良好状态</span>
              </el-form-item>
              
              <el-form-item label="警告阈值">
                <el-input-number v-model="formData.warningThreshold" :precision="2" />
                <span class="threshold-tip">超过此值时发出警告</span>
              </el-form-item>
              
              <el-form-item label="危险阈值">
                <el-input-number v-model="formData.dangerThreshold" :precision="2" />
                <span class="threshold-tip">超过此值时为危险状态</span>
              </el-form-item>
              
              <el-form-item label="预警方式">
                <el-checkbox-group v-model="formData.alertMethods">
                  <el-checkbox label="email">邮件通知</el-checkbox>
                  <el-checkbox label="sms">短信通知</el-checkbox>
                  <el-checkbox label="system">系统通知</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </template>
    </el-dialog>

    <!-- 指标详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="`${currentIndicator?.name} - 详情`"
      width="800px"
      destroy-on-close
    >
      <div v-if="currentIndicator" class="indicator-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="指标名称">{{ currentIndicator.name }}</el-descriptions-item>
          <el-descriptions-item label="指标编码">{{ currentIndicator.code }}</el-descriptions-item>
          <el-descriptions-item label="指标类型">{{ currentIndicator.type }}</el-descriptions-item>
          <el-descriptions-item label="数据类型">{{ getDataTypeName(currentIndicator.dataType) }}</el-descriptions-item>
          <el-descriptions-item label="单位">{{ currentIndicator.unit || '-' }}</el-descriptions-item>
          <el-descriptions-item label="计算频率">{{ getFrequencyName(currentIndicator.frequency) }}</el-descriptions-item>
          <el-descriptions-item label="最新值">
            {{ currentIndicator.lastValue ? formatValue(currentIndicator.lastValue, currentIndicator.dataType, currentIndicator.unit) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentIndicator.status === 1 ? 'success' : 'danger'">
              {{ currentIndicator.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="描述说明" :span="2">{{ currentIndicator.description || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 历史趋势图 -->
        <div class="trend-chart" style="margin-top: 20px;">
          <h4>历史趋势</h4>
          <div id="trendChart" style="width: 100%; height: 300px; border: 1px dashed #ddd;"></div>
        </div>
      </div>
    </el-dialog>

    <!-- 阈值配置对话框 -->
    <el-dialog
      v-model="thresholdDialogVisible"
      :title="`${currentIndicator?.name} - 阈值配置`"
      width="600px"
      destroy-on-close
    >
      <div v-if="currentIndicator">
        <el-form label-width="120px">
          <el-form-item>
            <el-checkbox v-model="thresholdConfig.hasThreshold">启用阈值预警</el-checkbox>
          </el-form-item>
          
          <div v-if="thresholdConfig.hasThreshold">
            <el-form-item label="优秀阈值">
              <el-input-number v-model="thresholdConfig.excellentThreshold" :precision="2" />
            </el-form-item>
            
            <el-form-item label="良好阈值">
              <el-input-number v-model="thresholdConfig.goodThreshold" :precision="2" />
            </el-form-item>
            
            <el-form-item label="警告阈值">
              <el-input-number v-model="thresholdConfig.warningThreshold" :precision="2" />
            </el-form-item>
            
            <el-form-item label="危险阈值">
              <el-input-number v-model="thresholdConfig.dangerThreshold" :precision="2" />
            </el-form-item>
            
            <el-form-item label="预警方式">
              <el-checkbox-group v-model="thresholdConfig.alertMethods">
                <el-checkbox label="email">邮件通知</el-checkbox>
                <el-checkbox label="sms">短信通知</el-checkbox>
                <el-checkbox label="system">系统通知</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="thresholdDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveThreshold">保存</el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import { Plus, Upload, Download, Refresh, View, Edit, CopyDocument, Delete } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

export default {
  name: 'IndicatorMgmt',
  components: {
    Plus, Upload, Download, Refresh, View, Edit, CopyDocument, Delete
  },
  data() {
    return {
      loading: false,
      searchKeyword: '',
      typeFilter: '',
      dataTypeFilter: '',
      frequencyFilter: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 20,
      total: 0,
      selectedIndicators: [],
      
      dialogVisible: false,
      detailDialogVisible: false,
      thresholdDialogVisible: false,
      currentIndicator: null,
      activeTab: 'basic',
      
      // 表单数据
      formData: {
        id: null,
        name: '',
        code: '',
        typeId: '',
        dataType: '',
        unit: '',
        frequency: '',
        description: '',
        status: 1,
        calculationType: 'formula',
        formula: '',
        dependencies: [],
        dataSource: '',
        sqlQuery: '',
        apiUrl: '',
        apiMethod: 'GET',
        apiParams: '',
        hasThreshold: false,
        excellentThreshold: null,
        goodThreshold: null,
        warningThreshold: null,
        dangerThreshold: null,
        alertMethods: []
      },
      
      // 阈值配置
      thresholdConfig: {
        hasThreshold: false,
        excellentThreshold: null,
        goodThreshold: null,
        warningThreshold: null,
        dangerThreshold: null,
        alertMethods: []
      },
      
      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入指标名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入指标编码', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_]+$/, message: '编码只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        typeId: [
          { required: true, message: '请选择指标类型', trigger: 'change' }
        ],
        dataType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ],
        frequency: [
          { required: true, message: '请选择计算频率', trigger: 'change' }
        ]
      },
      
      // 指标类型选项
      typeOptions: [
        {
          value: 1,
          label: '费用指标',
          children: [
            { value: 11, label: '管理费用率' },
            { value: 12, label: '销售费用率' }
          ]
        },
        {
          value: 2,
          label: '税款指标',
          children: [
            { value: 21, label: '税金及附加' },
            { value: 22, label: '所得税费用' }
          ]
        },
        {
          value: 3,
          label: '资产负债指标',
          children: [
            { value: 31, label: '资产总额' },
            { value: 32, label: '负债总额' },
            { value: 33, label: '资产负债率' }
          ]
        },
        {
          value: 4,
          label: '利润指标',
          children: [
            { value: 41, label: '净利润' },
            { value: 42, label: '营业利润' },
            { value: 43, label: '净利润率' }
          ]
        }
      ],
      
      // 依赖指标选项
      dependencyOptions: [
        { id: 1, name: '营业收入', code: 'REVENUE' },
        { id: 2, name: '净利润', code: 'NET_PROFIT' },
        { id: 3, name: '总资产', code: 'TOTAL_ASSETS' },
        { id: 4, name: '销售成本', code: 'COST_OF_SALES' }
      ],
      
      // 指标列表数据
      indicatorList: [
        { id: 1, name: '管理费用率', code: 'ADMIN_EXPENSE_RATIO', type: '费用指标', dataType: '百分比', frequency: '月', status: 1 },
        { id: 2, name: '销售费用率', code: 'SALES_EXPENSE_RATIO', type: '费用指标', dataType: '百分比', frequency: '月', status: 1 },
        { id: 3, name: '税金及附加', code: 'TAXES_AND_SURCHARGES', type: '税款指标', dataType: '金额', frequency: '月', status: 1 },
        { id: 4, name: '所得税费用', code: 'INCOME_TAX_EXPENSE', type: '税款指标', dataType: '金额', frequency: '季度', status: 1 },
        { id: 5, name: '资产总额', code: 'TOTAL_ASSETS', type: '资产负债指标', dataType: '金额', frequency: '月', status: 1 },
        { id: 6, name: '负债总额', code: 'TOTAL_LIABILITIES', type: '资产负债指标', dataType: '金额', frequency: '月', status: 1 },
        { id: 7, name: '资产负债率', code: 'ASSET_LIABILITY_RATIO', type: '资产负债指标', dataType: '百分比', frequency: '季度', status: 1 },
        { id: 8, name: '净利润', code: 'NET_PROFIT', type: '利润指标', dataType: '金额', frequency: '月', status: 1 },
        { id: 9, name: '营业利润', code: 'OPERATING_PROFIT', type: '利润指标', dataType: '金额', frequency: '月', status: 1 },
        { id: 10, name: '净利润率', code: 'NET_PROFIT_MARGIN', type: '利润指标', dataType: '百分比', frequency: '月', status: 1 },
        // 补充40条虚拟指标
        ...Array.from({ length: 40 }, (_, i) => ({
          id: i + 11,
          name: `模拟指标${i + 11}`,
          code: `MOCK_METRIC_${i + 11}`,
          type: ['费用指标', '税款指标', '资产负债指标', '利润指标'][i % 4],
          dataType: ['金额', '百分比', '金额', '百分比'][i % 4],
          frequency: ['月', '季度', '月', '月'][i % 4],
          status: (i % 2) ? 1 : 0
        }))
      ],
    }
  },
  computed: {
    dialogTitle() {
      return this.formData.id ? '编辑指标' : '新增指标'
    },
    filteredIndicatorList() {
      // 分页切片
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.indicatorList.slice(start, end)
    }
  },
  methods: {
    // 工具方法
    getDataTypeTag(dataType) {
      const tagMap = {
        number: 'primary',
        percentage: 'success',
        amount: 'warning',
        ratio: 'info'
      }
      return tagMap[dataType] || 'info'
    },
    
    getDataTypeName(dataType) {
      const nameMap = {
        number: '数值',
        percentage: '百分比',
        amount: '金额',
        ratio: '比率'
      }
      return nameMap[dataType] || dataType
    },
    
    getFrequencyTag(frequency) {
      const tagMap = {
        realtime: 'danger',
        daily: 'warning',
        weekly: 'primary',
        monthly: 'success',
        quarterly: 'info',
        yearly: 'info'
      }
      return tagMap[frequency] || 'info'
    },
    
    getFrequencyName(frequency) {
      const nameMap = {
        realtime: '实时',
        daily: '日',
        weekly: '周',
        monthly: '月',
        quarterly: '季',
        yearly: '年'
      }
      return nameMap[frequency] || frequency
    },
    
    formatValue(value, dataType, unit) {
      if (value === null || value === undefined) return '-'
      
      let formatted = value.toString()
      if (dataType === 'percentage') {
        formatted = value.toFixed(1) + '%'
      } else if (dataType === 'amount') {
        formatted = value.toLocaleString() + (unit || '元')
      } else if (unit) {
        formatted = value + unit
      }
      
      return formatted
    },
    
    // 搜索和筛选
    handleSearch() {
      // 实际项目中这里会调用API
      console.log('搜索条件:', {
        keyword: this.searchKeyword,
        type: this.typeFilter,
        dataType: this.dataTypeFilter,
        frequency: this.frequencyFilter,
        status: this.statusFilter
      })
    },
    
    resetFilter() {
      this.searchKeyword = ''
      this.typeFilter = ''
      this.dataTypeFilter = ''
      this.frequencyFilter = ''
      this.statusFilter = ''
      this.handleSearch()
    },
    
    // 数据操作
    refreshData() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('刷新成功')
      }, 1000)
    },
    
    // 批量操作
    handleSelectionChange(selection) {
      this.selectedIndicators = selection
    },
    
    batchEnable() {
      this.$message.success(`已启用 ${this.selectedIndicators.length} 个指标`)
      this.selectedIndicators = []
    },
    
    batchDisable() {
      this.$message.success(`已禁用 ${this.selectedIndicators.length} 个指标`)
      this.selectedIndicators = []
    },
    
    batchDelete() {
      this.$confirm(`确认删除选中的 ${this.selectedIndicators.length} 个指标？`, '批量删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('批量删除成功')
        this.selectedIndicators = []
      })
    },
    
    // 导入导出
    importIndicators() {
      this.$message.info('批量导入功能')
    },
    
    exportIndicators() {
      this.$message.info('导出指标数据')
    },
    
    // 状态切换
    toggleStatus(row) {
      this.$message.success(`${row.name} 状态已${row.status === 1 ? '启用' : '禁用'}`)
    },
    
    // 指标操作
    createIndicator() {
      this.resetForm()
      this.dialogVisible = true
    },
    
    viewIndicator(row) {
      this.currentIndicator = row
      this.detailDialogVisible = true
    },
    
    editIndicator(row) {
      this.formData = { ...row }
      this.dialogVisible = true
    },
    
    deleteIndicator(row) {
      this.$confirm(`确认删除指标"${row.name}"？删除后无法恢复。`, '确认删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.indicatorList.findIndex(item => item.id === row.id)
        if (index > -1) {
          this.indicatorList.splice(index, 1)
          this.$message.success('删除成功')
        }
      })
    },
    
    // 阈值配置
    configThreshold(row) {
      this.currentIndicator = row
      this.thresholdConfig = {
        hasThreshold: row.hasThreshold || false,
        excellentThreshold: row.excellentThreshold || null,
        goodThreshold: row.goodThreshold || null,
        warningThreshold: row.warningThreshold || null,
        dangerThreshold: row.dangerThreshold || null,
        alertMethods: row.alertMethods || []
      }
      this.thresholdDialogVisible = true
    },
    
    saveThreshold() {
      // 保存阈值配置
      Object.assign(this.currentIndicator, this.thresholdConfig)
      this.$message.success('阈值配置保存成功')
      this.thresholdDialogVisible = false
    },
    
    // 表单操作
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.formData.id) {
            // 编辑
            const index = this.indicatorList.findIndex(item => item.id === this.formData.id)
            if (index > -1) {
              Object.assign(this.indicatorList[index], this.formData)
            }
            this.$message.success('编辑成功')
          } else {
            // 新增
            const newIndicator = {
              ...this.formData,
              id: Date.now(),
              updateTime: new Date().toLocaleString(),
              lastValue: null
            }
            this.indicatorList.push(newIndicator)
            this.$message.success('新增成功')
          }
          this.dialogVisible = false
        }
      })
    },
    
    resetForm() {
      this.formData = {
        id: null,
        name: '',
        code: '',
        typeId: '',
        dataType: '',
        unit: '',
        frequency: '',
        description: '',
        status: 1,
        calculationType: 'formula',
        formula: '',
        dependencies: [],
        dataSource: '',
        sqlQuery: '',
        apiUrl: '',
        apiMethod: 'GET',
        apiParams: '',
        hasThreshold: false,
        excellentThreshold: null,
        goodThreshold: null,
        warningThreshold: null,
        dangerThreshold: null,
        alertMethods: []
      }
      this.activeTab = 'basic'
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }
    },
    
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.total = this.indicatorList.length
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      this.total = this.indicatorList.length
    },
    renderTrendChart() {
      const chartDom = document.getElementById('trendChart')
      if (!chartDom) return
      const myChart = echarts.init(chartDom)
      const months = ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']
      // 模拟数据
      const thisYear = [12.5, 13.2, 14.1, 13.8, 15.0, 14.7, 15.3, 15.8, 16.1, 16.5, 16.8, 17.0]
      const lastYear = [11.8, 12.0, 12.7, 13.0, 13.5, 13.8, 14.0, 14.2, 14.5, 14.8, 15.0, 15.2]
      const option = {
        tooltip: { trigger: 'axis' },
        legend: { data: ['今年', '去年'] },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: { type: 'category', boundaryGap: false, data: months },
        yAxis: { type: 'value' },
        series: [
          { name: '今年', type: 'line', data: thisYear, smooth: true },
          { name: '去年', type: 'line', data: lastYear, smooth: true }
        ]
      }
      myChart.setOption(option)
      window.addEventListener('resize', () => { myChart.resize() })
    }
  },
  
  mounted() {
    this.total = this.indicatorList.length
  },
  watch: {
    detailDialogVisible(val) {
      if (val && this.currentIndicator) {
        this.$nextTick(() => {
          this.renderTrendChart()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.filter-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  margin-bottom: 20px;
}

.table-container {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  margin-bottom: 20px;
  
  .batch-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: #f5f7fa;
    border-radius: 4px;
    margin-top: 15px;
    
    span {
      color: #606266;
      font-size: 14px;
    }
  }
}

.pagination-wrapper {
  text-align: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.threshold-tip {
  color: #909399;
  font-size: 12px;
  margin-left: 10px;
}

.indicator-detail {
  .trend-chart {
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
    }
  }
}
</style>