import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/yjzb/indicatorValues/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/yjzb/indicatorValues/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/yjzb/indicatorValues/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/yjzb/indicatorValues/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/yjzb/indicatorValues/submit',
    method: 'post',
    data: row
  })
}

export const getListByType = (current, size, indicatorTypeId, params = {}) => {
  return request({
    url: '/yjzb/indicatorValues/listByType',
    method: 'get',
    params: {
      indicatorTypeId,
      ...params,
      current,
      size,
    }
  })
}

export const getStatistics = (period, indicatorTypeId, params = {}) => {
  return request({
    url: '/yjzb/indicatorValues/statistics',
    method: 'get',
    params: {
      period,
      indicatorTypeId,
      ...params
    }
  })
}

// 按指标ID查询（支持透传 period_like 等筛选），用于折线图趋势数据
export const getListByIndicator = (current, size, indicatorId, params = {}) => {
  return request({
    url: '/yjzb/indicatorValues/list',
    method: 'get',
    params: {
      indicatorId_equal: indicatorId,
      ...params,
      current,
      size,
    }
  })
}

// 重新开始AI分析
export const restartAiAnalysis = (indicatorValueId) => {
  return request({
    url: '/yjzb/indicatorValues/restart-ai-analysis',
    method: 'post',
    params: { indicatorValueId }
  })
}

// 查询最新AI分析结果
export const getLatestAiAnalysis = (indicatorValueId) => {
  return request({
    url: '/yjzb/indicatorValues/latest-ai-analysis',
    method: 'get',
    params: { indicatorValueId }
  })
}

// 当月某类型AI指标解读 - 重启并记录
export const restartMonthlyTypeAnalysis = (indicatorTypeId, period) => {
  return request({
    url: '/yjzb/indicatorValues/monthlyTypeAnalysis/restart',
    method: 'post',
    params: { indicatorTypeId, period }
  })
}

// 当月某类型AI指标解读 - 查询最新
export const getLatestMonthlyTypeAnalysis = (indicatorTypeId, period) => {
  return request({
    url: '/yjzb/indicatorValues/monthlyTypeAnalysis/latest',
    method: 'get',
    params: { indicatorTypeId, period }
  })
}

// 总览AI分析 - 费用管理
export const restartExpenseOverviewAnalysis = (indicatorTypeId, period) => {
  return request({
    url: '/yjzb/indicatorValues/overview/expense/restart',
    method: 'post',
    params: { indicatorTypeId, period }
  })
}

// 总览AI分析 - 资产负债
export const restartBalanceOverviewAnalysis = (indicatorTypeId, period) => {
  return request({
    url: '/yjzb/indicatorValues/overview/balance/restart',
    method: 'post',
    params: { indicatorTypeId, period }
  })
}

// 总览AI分析 - 利润
export const restartProfitOverviewAnalysis = (indicatorTypeId, period) => {
  return request({
    url: '/yjzb/indicatorValues/overview/profit/restart',
    method: 'post',
    params: { indicatorTypeId, period }
  })
}

// Excel数据导入
export const importExcelData = (formData) => {
  return request({
    url: '/yjzb/indicatorValues/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

