/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import java.io.Serial;

/**
 * 指标类型 实体类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@TableName("yjzb_indicator_types")
@Schema(description = "IndicatorTypes对象")
@EqualsAndHashCode(callSuper = true)
public class IndicatorTypesEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 指标类型名称
     */
    @Schema(description = "指标类型名称")
    private String typeName;
    /**
     * 指标类型编码（唯一）
     */
    @Schema(description = "指标类型编码（唯一）")
    private String typeCode;
    /**
     * 指标类型描述
     */
    @Schema(description = "指标类型描述")
    private String description;
    /**
     * 数据类型（NUMERIC-数值型、PERCENTAGE-百分比、ENUM-枚举、COMPOSITE-复合型）
     */
    @Schema(description = "数据类型（NUMERIC-数值型、PERCENTAGE-百分比、ENUM-枚举、COMPOSITE-复合型）")
    private String dataType;
    /**
     * 计量单位
     */
    @Schema(description = "计量单位")
    private String unit;
    /**
     * 计算公式（用于复合指标）
     */
    @Schema(description = "计算公式（用于复合指标）")
    private String calculationFormula;
    /**
     * 数据源配置（JSON格式，包含数据源类型、连接信息等）
     */
    @Schema(description = "数据源配置（JSON格式，包含数据源类型、连接信息等）")
    private String dataSourceConfig;

}
