export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键ID",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "指标类型ID",
      prop: "indicatorId",
      type: "input",
      search: false,
      hide: true,
    },
    {
      label: "数据期间",
      prop: "period",
      type: "input",
    },
    {
      label: "指标数值",
      prop: "value",
      type: "input",
    },
    {
      label: "维度信息",
      prop: "dimensions",
      type: "input",
      hide: true,
    },
    {
      label: "数据来源",
      prop: "dataSource",
      type: "input",
    },
    {
      label: "计算状态",
      prop: "calculationStatus",
      type: "input",
    },
    {
      label: "创建人ID",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门ID",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人ID",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "数据状态（1-正常，0-禁用）",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "删除标记（0-未删除，1-已删除）",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    }
  ]
}
