/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.excel;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 指标年度预算 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class IndicatorAnnualBudgetExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键ID")
	private Long id;
	/**
	 * 指标ID（关联指标表）
	 */
	@ColumnWidth(20)
	@ExcelProperty("指标ID（关联指标表）")
	private Long indicatorId;
	/**
	 * 指标名称（为历史保留冗余）
	 */
	@ColumnWidth(20)
	@ExcelProperty("指标名称（为历史保留冗余）")
	private String indicatorName;
	/**
	 * 年份（YYYY）
	 */
	@ColumnWidth(20)
	@ExcelProperty("年份（YYYY）")
	private Integer year;
	/**
	 * 年初预算数
	 */
	@ColumnWidth(20)
	@ExcelProperty("年初预算数")
	private BigDecimal initialBudget;
	/**
	 * 年初报备数
	 */
	@ColumnWidth(20)
	@ExcelProperty("年初报备数")
	private BigDecimal initialReported;
	/**
	 * 中期调整预算数
	 */
	@ColumnWidth(20)
	@ExcelProperty("中期调整预算数")
	private BigDecimal midyearBudget;
	/**
	 * 中期调整报备数
	 */
	@ColumnWidth(20)
	@ExcelProperty("中期调整报备数")
	private BigDecimal midyearReported;
	/**
	 * 当前使用数
	 */
	@ColumnWidth(20)
	@ExcelProperty("当前使用数")
	private BigDecimal currentUsed;
	/**
	 * 删除标记（0-未删除，1-已删除）
	 */
	@ColumnWidth(20)
	@ExcelProperty("删除标记（0-未删除，1-已删除）")
	private Integer isDeleted;

}
