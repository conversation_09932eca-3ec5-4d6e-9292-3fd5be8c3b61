package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.modules.yjzb.service.IIndicatorDeepAnalysisService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 指标深度分析 控制器
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/yjzb/indicator-deep-analysis")
@Tag(name = "指标深度分析", description = "指标深度分析接口")
public class IndicatorDeepAnalysisController extends BladeController {

    private final IIndicatorDeepAnalysisService indicatorDeepAnalysisService;

    /**
     * 生成“费用明细与结构占比”表格所需的 JSON 数据
     */
    @GetMapping("/category-structure")
    @Operation(summary = "费用结构占比", description = "传入 indicatorId 与 period，返回表格 JSON 结构")
    public R<Map<String, Object>> categoryStructure(
            @RequestParam Long indicatorId,
            @RequestParam String period) {
        try {
            // 参数验证
            if (indicatorId == null) {
                return R.fail("指标ID不能为空");
            }
            if (period == null || period.trim().isEmpty()) {
                return R.fail("期间不能为空");
            }

            // 验证期间格式 (YYYY-MM)
            if (!period.matches("\\d{4}-\\d{2}")) {
                return R.fail("期间格式错误，应为YYYY-MM格式");
            }

            Map<String, Object> result = indicatorDeepAnalysisService.generateCategoryStructureChartData(indicatorId,
                    period);
            return R.data(result);

        } catch (IllegalArgumentException e) {
            return R.fail("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return R.fail("获取分类结构数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成"费用趋势对比"图表所需的 JSON 数据
     */
    @GetMapping("/trend-comparison")
    @Operation(summary = "费用趋势对比", description = "传入 indicatorId 与 period，返回趋势对比 JSON 结构")
    public R<Map<String, Object>> trendComparison(
            @RequestParam Long indicatorId,
            @RequestParam String period) {
        try {
            // 参数验证
            if (indicatorId == null) {
                return R.fail("指标ID不能为空");
            }
            if (period == null || period.trim().isEmpty()) {
                return R.fail("期间不能为空");
            }

            // 验证期间格式 (YYYY-MM)
            if (!period.matches("\\d{4}-\\d{2}")) {
                return R.fail("期间格式错误，应为YYYY-MM格式");
            }

            Map<String, Object> result = indicatorDeepAnalysisService.generateTrendComparisonChartData(indicatorId,
                    period);
            return R.data(result);

        } catch (IllegalArgumentException e) {
            return R.fail("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return R.fail("获取趋势对比数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成"费用波动性分析"图表所需的 JSON 数据
     */
    @GetMapping("/volatility-analysis")
    @Operation(summary = "费用波动性分析", description = "传入 indicatorId 与 period，返回波动性分析 JSON 结构")
    public R<Map<String, Object>> volatilityAnalysis(
            @RequestParam Long indicatorId,
            @RequestParam String period) {
        try {
            // 参数验证
            if (indicatorId == null) {
                return R.fail("指标ID不能为空");
            }
            if (period == null || period.trim().isEmpty()) {
                return R.fail("期间不能为空");
            }

            // 验证期间格式 (YYYY-MM)
            if (!period.matches("\\d{4}-\\d{2}")) {
                return R.fail("期间格式错误，应为YYYY-MM格式");
            }

            Map<String, Object> result = indicatorDeepAnalysisService.generateVolatilityAnalysisChartData(indicatorId,
                    period);
            return R.data(result);

        } catch (IllegalArgumentException e) {
            return R.fail("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return R.fail("获取波动性分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成"费用增长率分析"图表所需的 JSON 数据
     */
    @GetMapping("/growth-analysis")
    @Operation(summary = "费用增长率分析", description = "传入 indicatorId 与 period，返回增长率分析 JSON 结构")
    public R<Map<String, Object>> growthAnalysis(
            @RequestParam Long indicatorId,
            @RequestParam String period) {
        try {
            // 参数验证
            if (indicatorId == null) {
                return R.fail("指标ID不能为空");
            }
            if (period == null || period.trim().isEmpty()) {
                return R.fail("期间不能为空");
            }

            // 验证期间格式 (YYYY-MM)
            if (!period.matches("\\d{4}-\\d{2}")) {
                return R.fail("期间格式错误，应为YYYY-MM格式");
            }

            Map<String, Object> result = indicatorDeepAnalysisService.generateGrowthAnalysisChartData(indicatorId,
                    period);
            return R.data(result);

        } catch (IllegalArgumentException e) {
            return R.fail("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return R.fail("获取增长率分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成"费用预算分析"图表所需的 JSON 数据
     */
    @GetMapping("/budget-analysis")
    @Operation(summary = "费用预算分析", description = "传入 indicatorId 与 period，返回预算分析 JSON 结构")
    public R<Map<String, Object>> budgetAnalysis(
            @RequestParam Long indicatorId,
            @RequestParam String period) {
        try {
            // 参数验证
            if (indicatorId == null) {
                return R.fail("指标ID不能为空");
            }
            if (period == null || period.trim().isEmpty()) {
                return R.fail("期间不能为空");
            }

            // 验证期间格式 (YYYY-MM)
            if (!period.matches("\\d{4}-\\d{2}")) {
                return R.fail("期间格式错误，应为YYYY-MM格式");
            }

            Map<String, Object> result = indicatorDeepAnalysisService.generateBudgetAnalysisChartData(indicatorId,
                    period);
            return R.data(result);

        } catch (IllegalArgumentException e) {
            return R.fail("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return R.fail("获取预算分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成"费用预测分析"图表所需的 JSON 数据
     */
    @GetMapping("/forecast-analysis")
    @Operation(summary = "费用预测分析", description = "传入 indicatorId 与 period，返回预测分析 JSON 结构")
    public R<Map<String, Object>> forecastAnalysis(
            @RequestParam Long indicatorId,
            @RequestParam String period) {
        try {
            // 参数验证
            if (indicatorId == null) {
                return R.fail("指标ID不能为空");
            }
            if (period == null || period.trim().isEmpty()) {
                return R.fail("期间不能为空");
            }

            // 验证期间格式 (YYYY-MM)
            if (!period.matches("\\d{4}-\\d{2}")) {
                return R.fail("期间格式错误，应为YYYY-MM格式");
            }

            Map<String, Object> result = indicatorDeepAnalysisService.generateForecastAnalysisChartData(indicatorId,
                    period);
            return R.data(result);

        } catch (IllegalArgumentException e) {
            return R.fail("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return R.fail("获取预测分析数据失败: " + e.getMessage());
        }
    }
}
