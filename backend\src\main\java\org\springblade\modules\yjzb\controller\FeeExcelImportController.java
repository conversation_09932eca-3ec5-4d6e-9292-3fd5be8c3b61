package org.springblade.modules.yjzb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.pojo.entity.IndicatorEntity;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity;
import org.springblade.modules.yjzb.service.IIndicatorService;
import org.springblade.modules.yjzb.service.IIndicatorValuesService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 费用Excel导入控制器
 * 接收费用数据.xlsx 和 年月(YYYY-MM)，按模板规则将本月数(D列)写入对应指标在该月份的值。
 */
@RestController
@AllArgsConstructor
@RequestMapping("/yjzb/fee-import")
@Tag(name = "费用导入", description = "费用Excel导入接口")
public class FeeExcelImportController extends BladeController {

    private final IIndicatorService indicatorService;
    private final IIndicatorValuesService indicatorValuesService;

    @PostMapping(value = "/excel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "导入费用数据Excel", description = "上传费用数据.xlsx与period(YYYY-MM)，按模板坐标写入指标值")
    public R<Map<String, Object>> importFeeExcel(
            @Parameter(description = "费用数据.xlsx文件", required = true) @RequestPart("file") MultipartFile file,
            @Parameter(description = "期间，格式YYYY-MM", required = true) @RequestParam("period") String period) {
        Map<String, Object> result = new HashMap<>(4);
        List<String> notFoundIndicators = new ArrayList<>();
        int written = 0;

        if (Func.isBlank(period) || !period.matches("\\d{4}-\\d{2}")) {
            return R.fail("period格式应为YYYY-MM");
        }

        try (InputStream in = file.getInputStream(); XSSFWorkbook wb = new XSSFWorkbook(in)) {
            Sheet sheet = wb.getSheetAt(0);

            // A列是科目名（含缩进与“其中：”），D列为本月数；按我们CSV模板的命名规则转换为指标全名
            // 映射规则：
            // - 删除左侧空格，识别“其中：”分组，不参与名称本体，层级用“-”连接
            // - 在末尾加“（销售费用/管理费用/财务费用）”根据当前大类行
            // - 指标名与 yjzb_indicator.name 完全匹配

            String currentCategory = null; // 销售费用/管理费用/财务费用
            Deque<String> nonZhongStack = new ArrayDeque<>();
            Deque<String> zhongStack = new ArrayDeque<>();
            int lastNonZhongIndent = -1;
            int lastZhongIndent = -1;

            // 预加载全部指标名->ID
            Map<String, Long> nameToId = buildIndicatorNameToIdMap();

            for (int r = sheet.getFirstRowNum(); r <= sheet.getLastRowNum(); r++) {
                Row row = sheet.getRow(r);
                if (row == null)
                    continue;

                String subjectRaw = getStringCell(row, 0); // A列
                if (Func.isBlank(subjectRaw))
                    continue;

                String formula = getStringCell(row, 2); // C列 公式列
                int indent = calcIndent(subjectRaw);
                String subjectStripped = subjectRaw.strip();
                boolean isZhong = subjectStripped.startsWith("其中：");
                String subjectCore = isZhong ? subjectStripped.substring(3).trim() : subjectStripped;

                // 识别大类
                if ("0.00".equalsIgnoreCase(Optional.ofNullable(formula).orElse("").trim())) {
                    if (subjectStripped.equals("销售费用") || subjectStripped.equals("管理费用")
                            || subjectStripped.equals("财务费用")) {
                        currentCategory = subjectStripped;
                        nonZhongStack.clear();
                        zhongStack.clear();
                        lastNonZhongIndent = -1;
                        lastZhongIndent = -1;
                    }
                    continue;
                }

                if (currentCategory == null)
                    continue;

                // 回退层级
                if (isZhong) {
                    while (!zhongStack.isEmpty() && indent <= lastZhongIndent) {
                        zhongStack.pollLast();
                        lastZhongIndent = lastZhongIndent - 2; // 以2空格为缩进步长的粗略回退
                    }
                } else {
                    while (!nonZhongStack.isEmpty() && indent <= lastNonZhongIndent) {
                        nonZhongStack.pollLast();
                        lastNonZhongIndent = lastNonZhongIndent - 2;
                    }
                    while (!zhongStack.isEmpty() && indent <= lastZhongIndent) {
                        zhongStack.pollLast();
                        lastZhongIndent = lastZhongIndent - 2;
                    }
                }

                // 构造候选指标名（与模板一致的 “父-子（类别）”）
                String basePath = String.join("-", nonZhongStack);
                String zpath = String.join("-", zhongStack);
                String parentWithZ = joinNonEmpty(basePath, zpath);
                List<String> candidates = new ArrayList<>();

                if (isZhong) {
                    // 分组头或叶子
                    String testGroup = joinNonEmpty(parentWithZ, subjectCore);
                    candidates.add(testGroup + "（" + currentCategory + "）");
                    candidates.add(subjectCore + "（" + currentCategory + "）");
                } else {
                    candidates.add(joinNonEmpty(parentWithZ, subjectCore) + "（" + currentCategory + "）");
                    candidates.add(joinNonEmpty(basePath, subjectCore) + "（" + currentCategory + "）");
                    candidates.add(subjectCore + "（" + currentCategory + "）");
                }

                String chosen = null;
                for (String fullName : candidates) {
                    if (nameToId.containsKey(fullName)) {
                        chosen = fullName;
                        break;
                    }
                }

                if (chosen == null) {
                    notFoundIndicators.add(subjectStripped + " -> " + candidates);
                } else {
                    // 读取 D 列（本月数）并写库
                    BigDecimal value = getNumericCell(row, 3);
                    if (value != null) {
                        Long indicatorId = nameToId.get(chosen);
                        upsertIndicatorValue(indicatorId, period, value);
                        written++;
                    }
                }

                // 入栈（非其中作为父，"其中："作为分组头时也入栈）
                if (isZhong) {
                    zhongStack.addLast(subjectCore);
                    lastZhongIndent = indent;
                } else {
                    nonZhongStack.addLast(subjectCore);
                    lastNonZhongIndent = indent;
                }
            }

        } catch (Exception e) {
            return R.fail("导入失败:" + e.getMessage());
        }

        result.put("written", written);
        result.put("notFound", notFoundIndicators);
        return R.data(result);
    }

    private Map<String, Long> buildIndicatorNameToIdMap() {
        Map<String, Long> map = new HashMap<>();
        List<IndicatorEntity> list = indicatorService.list(new QueryWrapper<>());
        for (IndicatorEntity it : list) {
            if (it.getName() != null && it.getId() != null) {
                map.put(it.getName(), it.getId());
            }
        }
        return map;
    }

    private static int calcIndent(String s) {
        int i = 0;
        while (i < s.length() && s.charAt(i) == ' ')
            i++;
        return i;
    }

    private static String getStringCell(Row row, int idx) {
        Cell c = row.getCell(idx);
        if (c == null)
            return null;
        if (c.getCellType() != CellType.STRING) {
            c.setCellType(CellType.STRING);
        }
        String v = c.getStringCellValue();
        return v != null ? v.trim() : null;
    }

    private static BigDecimal getNumericCell(Row row, int idx) {
        Cell c = row.getCell(idx);
        if (c == null)
            return null;
        try {
            switch (c.getCellType()) {
                case NUMERIC:
                    return BigDecimal.valueOf(c.getNumericCellValue());
                case STRING:
                    String s = c.getStringCellValue();
                    if (s == null || s.isBlank())
                        return null;
                    s = s.replace(",", "").trim();
                    return new BigDecimal(s);
                case FORMULA:
                    try {
                        return BigDecimal.valueOf(c.getNumericCellValue());
                    } catch (Exception ex) {
                        String s2 = c.getStringCellValue();
                        if (s2 == null || s2.isBlank())
                            return null;
                        s2 = s2.replace(",", "").trim();
                        return new BigDecimal(s2);
                    }
                default:
                    return null;
            }
        } catch (Exception ex) {
            return null;
        }
    }

    private static String joinNonEmpty(String a, String b) {
        a = a == null ? "" : a.trim();
        b = b == null ? "" : b.trim();
        if (a.isEmpty())
            return b;
        if (b.isEmpty())
            return a;
        return a + '-' + b;
    }

    private void upsertIndicatorValue(Long indicatorId, String period, BigDecimal value) {
        // 先查是否已存在该indicatorId+period
        IndicatorValuesEntity q = new IndicatorValuesEntity();
        q.setIndicatorId(indicatorId);
        q.setPeriod(period);
        IndicatorValuesEntity exist = indicatorValuesService.getOne(new QueryWrapper<>(q));
        if (exist == null) {
            IndicatorValuesEntity e = new IndicatorValuesEntity();
            e.setIndicatorId(indicatorId);
            e.setPeriod(period);
            e.setValue(value);
            e.setDataSource("excel-import");
            indicatorValuesService.save(e);
        } else {
            exist.setValue(value);
            exist.setDataSource("excel-import");
            indicatorValuesService.updateById(exist);
        }
    }
}
