/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.pojo.entity.IndicatorTypesEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorTypesVO;

import java.util.Map;
import java.util.Objects;

/**
 * IndicatorTypes包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public class IndicatorTypesWrapper extends BaseEntityWrapper<IndicatorTypesEntity, IndicatorTypesVO> {

    public static IndicatorTypesWrapper build() {
        return new IndicatorTypesWrapper();
    }

    @Override
    public IndicatorTypesVO entityVO(IndicatorTypesEntity indicatorTypes) {
        return Objects.requireNonNull(BeanUtil.copyProperties(indicatorTypes, IndicatorTypesVO.class));
    }

    /**
     * 查询条件处理 - 针对PostgreSQL数据库的特殊处理
     * 解决bigint字段LIKE查询类型不匹配的问题
     */
    public void indicatorTypesQuery(Map<String, Object> indicatorTypes) {
        // 针对PostgreSQL数据库bigint类型字段查询需要强转的处理

        // 处理id字段 - 数据库字段为bigserial/bigint类型
        String idKey = "id";
        if (Func.isNotEmpty(indicatorTypes.get(idKey))) {
            // 设置"="查询，避免bigint字段使用LIKE查询导致的类型不匹配错误
            indicatorTypes.put(idKey.concat("_equal"), Func.toLong(indicatorTypes.get(idKey)));
            // 删除默认的LIKE查询参数，防止PostgreSQL报错
            indicatorTypes.remove(idKey);
        }

        // 处理create_user字段 - 数据库字段为bigint类型
        String createUserKey = "createUser";
        if (Func.isNotEmpty(indicatorTypes.get(createUserKey))) {
            indicatorTypes.put(createUserKey.concat("_equal"), Func.toLong(indicatorTypes.get(createUserKey)));
            indicatorTypes.remove(createUserKey);
        }

        // 处理create_dept字段 - 数据库字段为bigint类型
        String createDeptKey = "createDept";
        if (Func.isNotEmpty(indicatorTypes.get(createDeptKey))) {
            indicatorTypes.put(createDeptKey.concat("_equal"), Func.toLong(indicatorTypes.get(createDeptKey)));
            indicatorTypes.remove(createDeptKey);
        }

        // 处理update_user字段 - 数据库字段为bigint类型
        String updateUserKey = "updateUser";
        if (Func.isNotEmpty(indicatorTypes.get(updateUserKey))) {
            indicatorTypes.put(updateUserKey.concat("_equal"), Func.toLong(indicatorTypes.get(updateUserKey)));
            indicatorTypes.remove(updateUserKey);
        }

        // 处理status字段 - 数据库字段为int类型
        String statusKey = "status";
        if (Func.isNotEmpty(indicatorTypes.get(statusKey))) {
            indicatorTypes.put(statusKey.concat("_equal"), Func.toInt(indicatorTypes.get(statusKey)));
            indicatorTypes.remove(statusKey);
        }

        // 处理is_deleted字段 - 数据库字段为int类型
        String isDeletedKey = "isDeleted";
        if (Func.isNotEmpty(indicatorTypes.get(isDeletedKey))) {
            indicatorTypes.put(isDeletedKey.concat("_equal"), Func.toInt(indicatorTypes.get(isDeletedKey)));
            indicatorTypes.remove(isDeletedKey);
        }
    }

}