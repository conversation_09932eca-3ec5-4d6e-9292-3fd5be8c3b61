package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.modules.yjzb.entity.ExpenseAiAnalysis;
import org.springblade.modules.yjzb.entity.ExpenseForecast;
import org.springblade.modules.yjzb.service.ExpenseAiAnalysisService;
import org.springblade.modules.yjzb.service.ExpenseForecastService;
import org.springframework.web.bind.annotation.*;

/**
 * 办公费用AI解读分析控制器
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/yjzb/expense-ai-analysis")
@Tag(name = "办公费用AI解读分析", description = "办公费用AI解读分析相关接口")
public class ExpenseAiAnalysisController {

    private final ExpenseAiAnalysisService expenseAiAnalysisService;
    private final ExpenseForecastService expenseForecastService;

    /**
     * 执行AI分析
     */
    @PostMapping("/execute")
    @Operation(summary = "执行AI分析", description = "执行办公费用AI解读分析")
    public R<String> executeAnalysis(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "期间") @RequestParam String period,
            @Parameter(description = "分析类型") @RequestParam String analysisType,
            @Parameter(description = "输入参数") @RequestParam String inputParams,
            @Parameter(description = "是否强制重新计算，跳过缓存") @RequestParam(required = false, defaultValue = "false") boolean force) {

        try {
            String result = expenseAiAnalysisService.executeAnalysis(indicatorId, period, analysisType, inputParams,
                    force);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("AI分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取AI分析结果
     */
    @GetMapping("/result")
    @Operation(summary = "获取AI分析结果", description = "获取办公费用AI解读分析结果")
    public R<ExpenseAiAnalysis> getAnalysisResult(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "期间") @RequestParam String period,
            @Parameter(description = "分析类型") @RequestParam String analysisType) {

        ExpenseAiAnalysis result = expenseAiAnalysisService.getAnalysisResult(indicatorId, period, analysisType);
        return R.data(result);
    }

    /**
     * 检查是否有缓存结果
     */
    @GetMapping("/has-cache")
    @Operation(summary = "检查缓存", description = "检查是否有缓存的AI分析结果")
    public R<Boolean> hasCachedResult(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "期间") @RequestParam String period,
            @Parameter(description = "分析类型") @RequestParam String analysisType) {

        boolean hasCache = expenseAiAnalysisService.hasCachedResult(indicatorId, period, analysisType);
        return R.data(hasCache);
    }

    /**
     * 执行费用预测
     */
    @PostMapping("/forecast")
    @Operation(summary = "执行费用预测", description = "执行办公费用预测")
    public R<ExpenseForecast> executeForecast(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "预测期间") @RequestParam String forecastPeriod,
            @Parameter(description = "预测类型") @RequestParam String forecastType,
            @Parameter(description = "输入数据") @RequestParam String inputData) {

        try {
            ExpenseForecast result = expenseForecastService.executeForecast(indicatorId, forecastPeriod, forecastType,
                    inputData);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("费用预测失败: " + e.getMessage());
        }
    }

    /**
     * 获取预测结果
     */
    @GetMapping("/forecast-result")
    @Operation(summary = "获取预测结果", description = "获取办公费用预测结果")
    public R<ExpenseForecast> getForecastResult(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "预测期间") @RequestParam String forecastPeriod,
            @Parameter(description = "预测类型") @RequestParam String forecastType) {

        ExpenseForecast result = expenseForecastService.getForecastResult(indicatorId, forecastPeriod, forecastType);
        return R.data(result);
    }
}
