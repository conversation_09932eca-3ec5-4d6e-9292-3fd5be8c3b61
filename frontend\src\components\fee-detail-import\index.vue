<template>
  <div class="fee-detail-import">
    <el-dialog
      :model-value="visible"
      title="费用明细数据导入"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleClose"
      @update:model-value="$emit('update:visible', $event)"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <!-- 指标选择 -->
        <el-form-item label="选择指标" prop="indicatorId">
          <indicator-selector
            v-model="formData.indicatorId"
            placeholder="请输入指标名称搜索"
            @select="handleIndicatorSelect"
          />
          <div class="form-tip">请选择要导入数据的指标</div>
        </el-form-item>

        

        <!-- 文件上传 -->
        <el-form-item label="数据文件" prop="file">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept=".xlsx,.xls,.csv"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
                         <template #tip>
               <div class="el-upload__tip">
                 支持 .xlsx、.xls、.csv 格式，文件大小不超过 10MB<br/>
                 <strong>数据格式要求：</strong>日期,凭证号,摘要,金额,类目<br/>
                 <strong>注意：</strong>系统会自动从日期列提取年月期间，无效日期的行将被跳过
               </div>
             </template>
          </el-upload>
        </el-form-item>


      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            type="primary"
            :loading="importing"
            :disabled="!canImport"
            @click="handleImport"
          >
            {{ importing ? '导入中...' : '开始导入' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入进度 -->
    <el-dialog
      :model-value="progressVisible"
      title="导入进度"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      @update:model-value="progressVisible = $event"
    >
      <div class="progress-content">
        <el-progress
          :percentage="importProgress.percentage"
          :status="importProgress.status"
          :stroke-width="8"
        />
        <div class="progress-text">{{ importProgress.text }}</div>
        
        <div v-if="importProgress.total > 0" class="progress-stats">
          <div class="stat-item">
            <span class="stat-label">总记录数：</span>
            <span class="stat-value">{{ importProgress.total }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">成功导入：</span>
            <span class="stat-value success">{{ importProgress.success }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">失败记录：</span>
            <span class="stat-value error">{{ importProgress.failed }}</span>
          </div>
        </div>

        <div v-if="importErrors.length > 0" class="error-list">
          <h4>错误信息：</h4>
          <el-alert
            v-for="(error, index) in importErrors.slice(0, 5)"
            :key="index"
            :title="error"
            type="error"
            :closable="false"
            show-icon
            style="margin-bottom: 8px"
          />
          <div v-if="importErrors.length > 5" class="more-errors">
            还有 {{ importErrors.length - 5 }} 条错误信息...
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button
            v-if="importProgress.status === 'success' || importProgress.status === 'exception'"
            @click="handleProgressClose"
          >
            关闭
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import IndicatorSelector from '../indicator-selector/index.vue'
import { importFeeDetailData } from '@/api/yjzb/indicatorValuesDetail'

export default {
  name: 'FeeDetailImport',
  components: {
    IndicatorSelector
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
             formData: {
         indicatorId: null,
         file: null
       },
             formRules: {
         indicatorId: [
           { required: true, message: '请选择指标', trigger: 'change' }
         ],
         file: [
           { required: true, message: '请选择数据文件', trigger: 'change' }
         ]
       },
      selectedIndicator: null,
      importing: false,
      progressVisible: false,
      importProgress: {
        percentage: 0,
        status: 'active',
        text: '准备导入...',
        total: 0,
        success: 0,
        failed: 0
      },
      importErrors: []
    }
  },
  computed: {
         canImport() {
       return this.formData.indicatorId && 
              this.formData.file
     }
  },
  methods: {
    handleIndicatorSelect(indicator) {
      this.selectedIndicator = indicator
    },

    handleFileChange(file) {
      this.formData.file = file.raw
    },

    handleFileRemove() {
      this.formData.file = null
    },

    beforeUpload(file) {
      const isValidType = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
                          'application/vnd.ms-excel', 
                          'text/csv'].includes(file.type)
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isValidType) {
        this.$message.error('只能上传 Excel 或 CSV 文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }
      return false // 阻止自动上传
    },



    async handleImport() {
      try {
        await this.$refs.formRef.validate()
        
        this.importing = true
        this.progressVisible = true
        this.importProgress = {
          percentage: 0,
          status: 'active',
          text: '正在上传文件...',
          total: 0,
          success: 0,
          failed: 0
        }
        this.importErrors = []

        this.importProgress.percentage = 30
        this.importProgress.text = '正在导入数据...'

                 // 调用导入API - 直接上传文件
         const formData = new FormData()
         formData.append('indicatorId', this.formData.indicatorId)
         formData.append('file', this.formData.file)

        const response = await importFeeDetailData(formData)

        this.importProgress.percentage = 100
        this.importProgress.status = 'success'
        this.importProgress.text = '导入完成'
        this.importProgress.success = response.data.data?.successCount || 0
        this.importProgress.failed = response.data.data?.failCount || 0

        if (response.data.data?.errors) {
          this.importErrors = response.data.data.errors
        }

        this.$message.success('费用明细数据导入成功！')
                 this.$emit('success', {
           indicatorId: this.formData.indicatorId,
           totalCount: response.data.data?.totalCount || 0,
           successCount: this.importProgress.success,
           failCount: this.importProgress.failed
         })

      } catch (error) {
        console.error('导入失败:', error)
        this.importProgress.status = 'exception'
        this.importProgress.text = '导入失败'
        this.importErrors = [error.message || '导入过程中发生错误']
        this.$message.error('导入失败：' + (error.message || '未知错误'))
      } finally {
        this.importing = false
      }
    },

    handleClose() {
      this.$refs.formRef?.resetFields()
             this.formData = {
         indicatorId: null,
         file: null
       }
      this.selectedIndicator = null
      this.$emit('close')
    },

    handleProgressClose() {
      this.progressVisible = false
      this.handleClose()
    }
  }
}
</script>

<style scoped>
.fee-detail-import {
  width: 100%;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}



.progress-content {
  padding: 20px 0;
}

.progress-text {
  text-align: center;
  margin: 16px 0;
  font-size: 14px;
  color: #606266;
}

.progress-stats {
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  display: block;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.error {
  color: #f56c6c;
}

.error-list {
  margin-top: 20px;
}

.error-list h4 {
  margin: 0 0 12px 0;
  color: #f56c6c;
  font-size: 14px;
}

.more-errors {
  text-align: center;
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}
</style>
