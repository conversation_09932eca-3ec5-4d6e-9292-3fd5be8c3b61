/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service;

import org.springframework.web.multipart.MultipartFile;
import java.util.Map;

/**
 * Dify接口对接服务
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
public interface IDifyService {

    /**
     * 创建知识库
     *
     * @param name        知识库名称
     * @param description 知识库描述
     * @return 知识库ID
     */
    String createDataset(String name, String description);

    /**
     * 更新知识库
     *
     * @param datasetId   知识库ID
     * @param name        知识库名称
     * @param description 知识库描述
     * @return 是否成功
     */
    boolean updateDataset(String datasetId, String name, String description);

    /**
     * 删除知识库
     *
     * @param datasetId 知识库ID
     * @return 是否成功
     */
    boolean deleteDataset(String datasetId);

    /**
     * 上传文档到知识库
     *
     * @param datasetId 知识库ID
     * @param file      文件
     * @param fileName  文件名
     * @return 文档ID
     */
    String uploadDocument(String datasetId, MultipartFile file, String fileName);

    /**
     * 上传文档到知识库（通过文件路径）
     *
     * @param datasetId 知识库ID
     * @param filePath  文件路径
     * @param fileName  文件名
     * @return 文档ID
     */
    String uploadDocumentByPath(String datasetId, String filePath, String fileName);

    /**
     * 更新文档
     *
     * @param datasetId  知识库ID
     * @param documentId 文档ID
     * @param fileName   文件名
     * @return 是否成功
     */
    boolean updateDocument(String datasetId, String documentId, String fileName);

    /**
     * 删除文档
     *
     * @param datasetId  知识库ID
     * @param documentId 文档ID
     * @return 是否成功
     */
    boolean deleteDocument(String datasetId, String documentId);

    /**
     * 下载文档
     *
     * @param datasetId  知识库ID
     * @param documentId 文档ID
     * @return 文件内容字节数组
     */
    byte[] downloadDocument(String datasetId, String documentId);

    /**
     * 获取文档上传文件信息
     *
     * @param datasetId  知识库ID
     * @param documentId 文档ID
     * @return 文件信息JSON字符串
     */
    String getDocumentUploadFile(String datasetId, String documentId);

    /**
     * 获取知识库列表
     *
     * @return 知识库列表JSON字符串
     */
    String getDatasetList();

    /**
     * 获取知识库详情
     *
     * @param datasetId 知识库ID
     * @return 知识库详情JSON字符串
     */
    String getDatasetDetail(String datasetId);

    /**
     * 获取文档列表
     *
     * @param datasetId 知识库ID
     * @return 文档列表JSON字符串
     */
    String getDocumentList(String datasetId);

    /**
     * 获取文档详情
     *
     * @param datasetId  知识库ID
     * @param documentId 文档ID
     * @return 文档详情JSON字符串
     */
    String getDocumentDetail(String datasetId, String documentId);

    /**
     * 搜索知识库
     *
     * @param datasetId 知识库ID
     * @param query     搜索关键词
     * @return 搜索结果JSON字符串
     */
    String searchDataset(String datasetId, String query);

    /**
     * 通过文本创建文档
     *
     * @param datasetId 知识库ID
     * @param name      文档名称
     * @param text      文档内容
     * @return 文档ID
     */
    String createDocumentByText(String datasetId, String name, String text);

    /**
     * 获取文档索引状态
     *
     * @param datasetId 知识库ID
     * @param batch     批次ID
     * @return 索引状态JSON字符串
     */
    String getDocumentIndexingStatus(String datasetId, String batch);

    /**
     * 通过文本更新文档
     *
     * @param datasetId  知识库ID
     * @param documentId 文档ID
     * @param name       文档名称
     * @param text       文档内容
     * @return 是否成功
     */
    boolean updateDocumentByText(String datasetId, String documentId, String name, String text);

    /**
     * 执行 Dify Workflow（遵循 POST /v1/workflows/run）
     *
     * 注意：
     * - 通过 API-Key 鉴权，无需 workflowId；由 Dify 后台绑定具体工作流。
     * - responseMode 可选：blocking | streaming。若为 streaming，将返回 SSE 文本；否则返回 JSON。
     * - timeoutMs 将影响阻塞等待的 HTTP 读超时（仅当前调用）。
     *
     * @param inputs       工作流输入参数（可为 null）
     * @param responseMode 响应模式：blocking 或 streaming（可为 null，默认 blocking）
     * @param user         用户标识字符串（可为 null）
     * @param timeoutMs    最大等待时长，毫秒（默认5分钟）。注意：Dify 端也有自身超时限制
     * @return 执行结果的原始字符串；blocking 返回 JSON，streaming 返回 SSE 文本；失败或异常返回 null
     */
    // 已废弃的阻塞执行接口，配合控制器精简后移除

    /**
     * 获取 workflow 执行详情（GET /v1/workflows/run/{workflow_run_id}）
     *
     * @param workflowRunId 执行ID（workflow_run_id）
     * @return 执行详情原始JSON；失败返回 null
     */
    String getWorkflowRunDetail(String workflowRunId, String apiKey);

    /**
     * 启动 workflow（streaming 模式），只读取首个事件以获取 workflow_run_id 并立即返回。
     * 非 200 视为启动失败。
     *
     * @param inputs 工作流输入参数
     * @param user   用户标识（可为空）
     * @return workflow_run_id；失败返回 null
     */
    String startWorkflowStreaming(Map<String, Object> inputs, String user, String apiKey);

    /**
     * 启动 workflow（blocking 模式），直接返回完整JSON结果字符串。
     * 
     * @param inputs 工作流输入参数
     * @param user   用户标识（可为空）
     * @param apiKey API Key（可为空，内部将回退到配置的key）
     * @return 执行结果原始JSON；失败返回 null
     */
    String startWorkflowBlocking(Map<String, Object> inputs, String user, String apiKey);
}
