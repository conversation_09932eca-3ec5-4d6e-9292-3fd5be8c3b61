<template>
  <!-- 财务税费管控 - 税款管理页面 -->
  <basic-container>
    <!-- 顶部筛选条件 -->
    <div class="filter-panel">
      <el-form :model="filterForm" inline label-width="80px">
        <el-form-item label="选择月份">
          <el-date-picker
            v-model="filterForm.selectedMonth"
            type="month"
            placeholder="选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item label="税款名称">
          <el-input v-model="filterForm.taxName" placeholder="请输入税款名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：纳税申报数据表 -->
      <el-col :span="16">
        <!-- 税务概览卡片 -->
        <el-row :gutter="15" style="margin-bottom: 20px;">
          <el-col :span="8">
            <div class="tax-card">
              <div class="card-header">
                <i class="el-icon-coin" style="color: #409eff;"></i>
                <span>本月应纳税额</span>
              </div>
              <div class="card-value">¥ 456,789</div>
              <div class="card-trend up">较上月增长 8.5%</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="tax-card">
              <div class="card-header">
                <i class="el-icon-coin" style="color: #67c23a;"></i>
                <span>上月应纳税额</span>
              </div>
              <div class="card-value">¥ 420,000</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="tax-card">
              <div class="card-header">
                <i class="el-icon-coin" style="color: #e6a23c;"></i>
                <span>去年同期应纳税额</span>
              </div>
              <div class="card-value">¥ 390,000</div>
            </div>
          </el-col>
        </el-row>

        <!-- 纳税申报数据表 -->
        <div class="tax-table-panel">
          <div class="panel-header">
            <h4>税款明细</h4>
            <div class="panel-actions">
              <el-button size="small" type="primary" @click="handleAdd">
                <i class="el-icon-plus"></i> 新增
              </el-button>
              <el-button size="small" @click="handleExport">
                <i class="el-icon-download"></i> 导出
              </el-button>
            </div>
          </div>
          
          <el-table :data="taxList" stripe border>
            <el-table-column label="当前月份" width="100">
              <template #default="{ row }">
                {{ getCurrentMonth() }}
              </template>
            </el-table-column>
            <el-table-column prop="taxTypeName" label="税款指标" width="120">
              <template #default="{ row }">
                <el-tag :type="getTaxTypeTag(row.taxType)" size="small">
                  {{ row.taxTypeName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="taxAmount" label="金额(元)" width="120" align="right">
              <template #default="{ row }">
                <span :class="{ 'tax-overdue': row.status === 'overdue' }">
                  {{ row.taxAmount.toLocaleString() }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="mini" type="text" @click="handleEdit(row)">编辑</el-button>
                <el-button size="mini" type="text" @click="handleAnalysis(row)">分析</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div style="margin-top: 15px; text-align: right;">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next"
              :total="taxTotal"
              :page-size="pageSize"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-col>

      <!-- 右侧：异常税款预警 -->
      <el-col :span="8">
        <div class="warning-panel">
          <div class="panel-header">
            <h4>异常税款预警</h4>
            <el-badge :value="abnormalTaxes.length" class="warning-badge">
              <i class="el-icon-warning"></i>
            </el-badge>
          </div>
          <div class="warning-list">
            <div
              v-for="item in abnormalTaxes"
              :key="item.id"
              class="warning-item"
              @click="handleWarningClick(item)"
            >
              <div class="warning-content">
                <div class="warning-title">{{ item.title }}</div>
                <div class="warning-desc">{{ item.description }}</div>
              </div>
              <div class="warning-level" :class="`level-${item.level}`">
                {{ item.levelName }}
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 税款分析弹窗 -->
    <el-dialog title="税款分析" v-model="analysisDialogVisible" width="900px" destroy-on-close @open="onAnalysisDialogOpen" @close="onAnalysisDialogClose">
      <div v-show="currentAnalysisTax">
        <div class="analysis-content">
          <!-- 税款基本信息 -->
          <div class="tax-info">
            <h4>{{ currentAnalysisTax.taxTypeName }} - {{ getCurrentMonth() }}</h4>
            <p class="tax-amount">金额：¥{{ currentAnalysisTax.taxAmount.toLocaleString() }}</p>
            <p class="tax-desc">说明：{{ currentAnalysisTax.description }}</p>
          </div>
          <!-- 税款趋势分析图 -->
          <div class="trend-chart-section">
            <h4>税款趋势分析</h4>
            <div class="mock-chart">
              <div class="chart-placeholder">
                <div id="taxLineChart" style="width: 100%; height: 300px;"></div>
              </div>
            </div>
          </div>
          <!-- AI解读 -->
          <div class="ai-analysis-section">
            <h4>🤖 AI智能解读</h4>
            <div class="ai-content">
              <div class="analysis-item">
                <div class="analysis-label">趋势分析：</div>
                <div class="analysis-text">
                  该税款项目在过去5个月呈现波动上升趋势，4月份达到峰值200K，相比1月份增长了28%。
                  整体趋势显示企业经营规模扩大，需关注税负变化。
                </div>
              </div>
              <div class="analysis-item">
                <div class="analysis-label">异常检测：</div>
                <div class="analysis-text">
                  4月份税款异常偏高，建议核查是否存在一次性大额纳税或政策调整。
                  5月份有所回落，属于正常波动范围。
                </div>
              </div>
              <div class="analysis-item">
                <div class="analysis-label">优化建议：</div>
                <div class="analysis-text">
                  1. 建议制定月度税款预算上限，避免单月税负过重；<br>
                  2. 优化{{ currentAnalysisTax.taxTypeName }}的纳税筹划，合理利用税收优惠政策；<br>
                  3. 建立税款预警机制，当月度税款超过均值20%时及时预警。
                </div>
              </div>
              <div class="analysis-item">
                <div class="analysis-label">风险评估：</div>
                <div class="analysis-text risk-low">
                  <el-tag type="success" size="small">低风险</el-tag>
                  当前税款水平在可控范围内，但需持续监控趋势变化。
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="analysisDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="exportAnalysis">导出分析报告</el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'FinanceTax',
  data() {
    return {
      // 筛选表单
      filterForm: {
        selectedMonth: '',
        taxName: ''
      },
      monthOptions: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05'],
      // 分页
      pageSize: 10,
      taxTotal: 86,
      // 分析弹窗
      analysisDialogVisible: false,
      currentAnalysisTax: null,
      // Mock 税款列表数据
      taxList: [
        {
          id: 1,
          taxType: 'vat',
          taxTypeName: '增值税',
          taxAmount: 162500,
          description: '增值税',
          status: 'paid',
          statusName: '已缴税'
        },
        {
          id: 2,
          taxType: 'corporate',
          taxTypeName: '企业所得税',
          taxAmount: 212500,
          description: '企业所得税',
          status: 'declared',
          statusName: '已申报'
        },
        {
          id: 3,
          taxType: 'urban',
          taxTypeName: '城市维护建设税',
          taxAmount: 11375,
          description: '城市维护建设税',
          status: 'pending',
          statusName: '未申报'
        },
        {
          id: 4,
          taxType: 'excise',
          taxTypeName: '消费税',
          taxAmount: 90000,
          description: '消费税',
          status: 'overdue',
          statusName: '逾期'
        }
      ],
      // Mock 异常税款预警
      abnormalTaxes: [
        {
          id: 1,
          title: '增值税异常',
          description: '本月增值税金额超预算20%',
          level: 'high',
          levelName: '高风险'
        },
        {
          id: 2,
          title: '企业所得税偏高',
          description: '企业所得税比往月增长50%',
          level: 'medium',
          levelName: '中风险'
        },
        {
          id: 3,
          title: '消费税异常',
          description: '消费税连续3个月增长',
          level: 'low',
          levelName: '低风险'
        }
      ],
      taxLineChartInstance: null, // echarts实例
    };
  },
  watch: {
    analysisDialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.renderTaxLineChart();
        });
      } else {
        if (this.taxLineChartInstance) {
          this.taxLineChartInstance.dispose();
          this.taxLineChartInstance = null;
        }
      }
    }
  },
  mounted() {
    // 渲染税款分析折线图（模拟数据）
    this.$nextTick(() => {
      if (document.getElementById('taxLineChart')) {
        const chart = echarts.init(document.getElementById('taxLineChart'));
        chart.setOption({
          title: { show: false },
          tooltip: { trigger: 'axis' },
          legend: { data: ['今年', '去年'] },
          grid: { left: 40, right: 20, bottom: 30, top: 30 },
          xAxis: { type: 'category', data: ['1月','2月','3月','4月','5月'] },
          yAxis: { type: 'value' },
          series: [
            { name: '今年', type: 'line', data: [156, 180, 125, 200, 165], smooth: false, label: { show: true, position: 'top' } },
            { name: '去年', type: 'line', data: [140, 160, 110, 170, 150], smooth: false, label: { show: true, position: 'top' } }
          ]
        });
      }
    });
  },
  methods: {
    // 查询筛选
    handleFilter() {
      this.$message.success('查询成功');
    },
    // 重置筛选
    resetFilter() {
      this.filterForm = {
        selectedMonth: '',
        taxName: ''
      };
    },
    // 新增税款
    handleAdd() {
      this.$message.info('打开新增税款表单');
    },
    // 导出数据
    handleExport() {
      this.$message.success('导出中...');
    },
    // 编辑税款
    handleEdit(row) {
      this.$message.info(`编辑税款：${row.id}`);
    },
    // 分析税款
    handleAnalysis(row) {
      this.currentAnalysisTax = row;
      this.analysisDialogVisible = true;
    },
    // 分页
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(page) {
      this.$message.info(`切换到第${page}页`);
    },
    // 点击预警
    handleWarningClick(item) {
      this.$message.warning(`查看预警详情：${item.title}`);
    },
    // 获取税种标签
    getTaxTypeTag(type) {
      // 已对齐指标说明.txt
      const tags = {
        vat: 'primary',
        excise: 'success',
        urban: 'info',
        education: 'info',
        local_education: 'info',
        corporate: 'success',
        stamp: 'info',
        land: 'info',
        property: 'info',
        vehicle: 'info',
        govfund: 'info',
        other: 'info'
      };
      return tags[type] || '';
    },
    // 获取当前月份
    getCurrentMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      return `${year}-${month}`;
    },
    // 导出分析报告
    exportAnalysis() {
      this.$message.success('分析报告导出中...');
    },
    onAnalysisDialogOpen() {
      this.$nextTick(() => {
        this.renderTaxLineChart();
        if (this.taxLineChartInstance) {
          this.taxLineChartInstance.resize();
        }
      });
    },
    onAnalysisDialogClose() {
      if (this.taxLineChartInstance) {
        this.taxLineChartInstance.dispose();
        this.taxLineChartInstance = null;
      }
    },
    renderTaxLineChart() {
      const chartDom = document.getElementById('taxLineChart');
      if (!chartDom) return;
      if (this.taxLineChartInstance) {
        this.taxLineChartInstance.dispose();
      }
      this.taxLineChartInstance = echarts.init(chartDom);
      this.taxLineChartInstance.setOption({
        title: { show: false },
        tooltip: { trigger: 'axis' },
        legend: { data: ['今年', '去年'] },
        grid: { left: 40, right: 20, bottom: 30, top: 30 },
        xAxis: { type: 'category', data: ['1月','2月','3月','4月','5月'] },
        yAxis: { type: 'value' },
        series: [
          { name: '今年', type: 'line', data: [156, 180, 125, 200, 165], smooth: false, label: { show: true, position: 'top' } },
          { name: '去年', type: 'line', data: [140, 160, 110, 170, 150], smooth: false, label: { show: true, position: 'top' } }
        ]
      });
      this.taxLineChartInstance.resize();
    }
  }
};
</script>

<style scoped>
.filter-panel {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.tax-card {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #eee;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.card-trend {
  font-size: 12px;
}

.card-trend.up { color: #f56c6c; }
.card-trend.normal { color: #909399; }
.card-trend.warning { color: #e6a23c; }

.tax-table-panel,
.calc-panel,
.chart-panel,
.advice-panel {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.panel-actions {
  display: flex;
  gap: 10px;
}

.tax-overdue {
  color: #f56c6c;
  font-weight: bold;
}

.calc-result {
  margin-top: 15px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-label {
  color: #666;
  font-size: 14px;
}

.result-value {
  color: #303133;
  font-weight: bold;
}

.mock-chart {
  border: 1px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  text-align: center;
  background: #fafafa;
  width: 100%;
  height: 320px;
  min-height: 300px;
}
.chart-placeholder {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
#taxLineChart {
  width: 100% !important;
  height: 100% !important;
  min-height: 300px;
}

.advice-list {
  max-height: 300px;
  overflow-y: auto;
}

.advice-item {
  padding: 15px;
  margin-bottom: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  background: #fafafa;
}

.advice-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.advice-title {
  font-weight: bold;
  color: #303133;
}

.advice-content {
  color: #666;
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 10px;
}

.advice-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.advice-benefit {
  color: #67c23a;
  font-size: 12px;
  font-weight: bold;
}

.warning-panel {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.warning-badge {
  position: relative;
}

.warning-list {
  max-height: 300px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.warning-item:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
}

.warning-desc {
  font-size: 12px;
  color: #666;
}

.warning-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.warning-level.level-high {
  background: #fef0f0;
  color: #f56c6c;
}

.warning-level.level-medium {
  background: #fdf6ec;
  color: #e6a23c;
}

.warning-level.level-low {
  background: #f0f9ff;
  color: #409eff;
}
</style> 