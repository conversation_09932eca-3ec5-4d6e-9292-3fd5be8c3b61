<template>
  <!-- 指标管理 - 指标类型管理页面 -->
  <basic-container>
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <h3>指标类型管理</h3>
      <div class="header-actions">
        <el-button type="primary" @click="createIndicatorType">
          <el-icon><plus /></el-icon> 新增类型
        </el-button>
        <el-button @click="refreshData">
          <el-icon><refresh /></el-icon> 刷新
        </el-button>
        <el-button @click="expandAll">
          <el-icon><sort /></el-icon> 展开全部
        </el-button>
        <el-button @click="collapseAll">
          <el-icon><fold /></el-icon> 折叠全部
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-panel">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="类型名称">
          <el-input v-model="filterForm.name" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 指标类型树形表格 -->
    <div class="table-container">
      <el-table :data="typeList" border stripe>
        <el-table-column prop="name" label="类型名称" width="180" />
        <el-table-column prop="code" label="类型编码" width="120" />
        <el-table-column prop="indicatorCount" label="指标数量" width="100" />
        <el-table-column prop="description" label="描述说明" />
        <el-table-column prop="sort" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <el-button type="text" size="small" @click="editType(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="deleteType(scope.row)" style="color: #f56c6c;">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      destroy-on-close
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="上级类型" prop="parentId" v-if="formData.parentId">
          <el-input v-model="parentTypeName" disabled />
        </el-form-item>
        
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入类型名称" />
        </el-form-item>
        
        <el-form-item label="类型编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入类型编码" />
          <div class="form-tip">编码必须唯一，建议使用英文字母和数字</div>
        </el-form-item>
        
        <el-form-item label="业务分类" prop="category">
          <el-select v-model="formData.category" placeholder="选择业务分类">
            <el-option label="财务指标" value="finance" />
            <el-option label="业务指标" value="business" />
            <el-option label="管理指标" value="management" />
            <el-option label="风险指标" value="risk" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述说明" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入类型描述"
          />
        </el-form-item>
        
        <el-form-item label="排序值" prop="sort">
          <el-input-number
            v-model="formData.sort"
            :min="0"
            :max="999"
            placeholder="排序值，数字越小排序越靠前"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </template>
    </el-dialog>

    <!-- 指标列表对话框 -->
    <el-dialog
      v-model="indicatorDialogVisible"
      :title="`${currentType?.name} - 指标列表`"
      width="80%"
      destroy-on-close
    >
      <el-table :data="indicatorList" border>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="name" label="指标名称" min-width="150" />
        <el-table-column prop="code" label="指标编码" width="120" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="dataType" label="数据类型" width="100" />
        <el-table-column prop="frequency" label="计算频率" width="100" />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
      </el-table>
    </el-dialog>
  </basic-container>
</template>

<script>
import { Plus, Refresh, Sort, Fold, Folder, Document, Edit, Delete } from '@element-plus/icons-vue'

export default {
  name: 'IndicatorTypeMgmt',
  components: {
    Plus, Refresh, Sort, Fold, Folder, Document, Edit, Delete
  },
  data() {
    return {
      loading: false,
      searchKeyword: '',
      statusFilter: '',
      categoryFilter: '',
      expandAll: false,
      dialogVisible: false,
      indicatorDialogVisible: false,
      currentType: null,
      parentTypeName: '',
      
      // 表单数据
      formData: {
        id: null,
        parentId: null,
        name: '',
        code: '',
        category: '',
        description: '',
        sort: 0,
        status: 1
      },
      
      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入类型名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入类型编码', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_]+$/, message: '编码只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择业务分类', trigger: 'change' }
        ]
      },
      
      // 指标类型数据
      typeList: [
        { name: '费用指标', code: 'EXPENSE', indicatorCount: 12, description: '企业各项费用相关指标', sort: 1, status: 1, createTime: '2024-01-01 10:00:00' },
        { name: '税款指标', code: 'TAX', indicatorCount: 8, description: '企业税金及相关指标', sort: 2, status: 1, createTime: '2024-01-01 10:00:00' },
        { name: '资产负债指标', code: 'ASSET_LIABILITY', indicatorCount: 15, description: '企业资产、负债相关指标', sort: 3, status: 1, createTime: '2024-01-01 10:00:00' },
        { name: '利润指标', code: 'PROFIT', indicatorCount: 10, description: '企业利润相关指标', sort: 4, status: 1, createTime: '2024-01-01 10:00:00' }
      ],
      
      // 示例指标数据
      indicatorList: [
        {
          id: 1,
          name: '净利润率',
          code: 'NET_PROFIT_MARGIN',
          unit: '%',
          dataType: '百分比',
          frequency: '月度',
          status: 1,
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 2,
          name: '毛利率',
          code: 'GROSS_PROFIT_MARGIN',
          unit: '%',
          dataType: '百分比',
          frequency: '月度',
          status: 1,
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 3,
          name: '营业收入增长率',
          code: 'REVENUE_GROWTH_RATE',
          unit: '%',
          dataType: '百分比',
          frequency: '季度',
          status: 1,
          createTime: '2024-01-01 10:00:00'
        }
      ]
    }
  },
  computed: {
    filteredData() {
      let result = [...this.indicatorTypeData]
      
      // 状态筛选
      if (this.statusFilter !== '') {
        result = result.filter(item => this.filterByStatus(item, this.statusFilter))
      }
      
      // 分类筛选
      if (this.categoryFilter) {
        result = result.filter(item => this.filterByCategory(item, this.categoryFilter))
      }
      
      // 关键词搜索
      if (this.searchKeyword) {
        result = result.filter(item => this.filterByKeyword(item, this.searchKeyword))
      }
      
      return result
    },
    
    dialogTitle() {
      return this.formData.id ? '编辑指标类型' : '新增指标类型'
    }
  },
  methods: {
    // 递归筛选函数
    filterByStatus(item, status) {
      const statusMatch = item.status.toString() === status
      if (statusMatch) return true
      
      if (item.children) {
        item.children = item.children.filter(child => this.filterByStatus(child, status))
        return item.children.length > 0
      }
      return false
    },
    
    filterByCategory(item, category) {
      const categoryMatch = item.category === category
      if (categoryMatch) return true
      
      if (item.children) {
        item.children = item.children.filter(child => this.filterByCategory(child, category))
        return item.children.length > 0
      }
      return false
    },
    
    filterByKeyword(item, keyword) {
      const keywordMatch = item.name.toLowerCase().includes(keyword.toLowerCase()) ||
                          item.code.toLowerCase().includes(keyword.toLowerCase())
      if (keywordMatch) return true
      
      if (item.children) {
        item.children = item.children.filter(child => this.filterByKeyword(child, keyword))
        return item.children.length > 0
      }
      return false
    },
    
    // 工具方法
    getCategoryTag(category) {
      const tagMap = {
        finance: 'primary',
        business: 'success',
        management: 'warning',
        risk: 'danger'
      }
      return tagMap[category] || 'info'
    },
    
    getCategoryName(category) {
      const nameMap = {
        finance: '财务指标',
        business: '业务指标',
        management: '管理指标',
        risk: '风险指标'
      }
      return nameMap[category] || category
    },
    
    // 搜索和筛选
    handleSearch() {
      // 触发计算属性重新计算
    },
    
    resetFilter() {
      this.searchKeyword = ''
      this.statusFilter = ''
      this.categoryFilter = ''
    },
    
    // 数据操作
    refreshData() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('刷新成功')
      }, 1000)
    },
    
    // 树形表格操作
    expandAll() {
      this.$refs.treeTable.store.states.defaultExpandAll.value = true
      this.$refs.treeTable.store.states.data.value.forEach(item => {
        this.$refs.treeTable.store.toggleRowExpansion(item, true)
      })
    },
    
    collapseAll() {
      this.$refs.treeTable.store.states.defaultExpandAll.value = false
      this.$refs.treeTable.store.states.data.value.forEach(item => {
        this.$refs.treeTable.store.toggleRowExpansion(item, false)
      })
    },
    
    // 状态切换
    toggleStatus(row) {
      this.$message.success(`${row.name} 状态已${row.status === 1 ? '启用' : '禁用'}`)
    },
    
    // 查看指标
    viewIndicators(row) {
      this.currentType = row
      this.indicatorDialogVisible = true
    },
    
    // 新增指标类型
    createIndicatorType() {
      this.resetForm()
      this.dialogVisible = true
    },
    
    // 新增子类型
    addChild(row) {
      this.resetForm()
      this.formData.parentId = row.id
      this.parentTypeName = row.name
      this.dialogVisible = true
    },
    
    // 编辑指标类型
    editType(row) {
      this.formData = { ...row }
      if (row.parentId) {
        const parent = this.findNodeById(this.indicatorTypeData, row.parentId)
        this.parentTypeName = parent ? parent.name : ''
      }
      this.dialogVisible = true
    },
    
    // 删除指标类型
    deleteType(row) {
      this.$confirm(`确认删除指标类型"${row.name}"？删除后无法恢复。`, '确认删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.removeNode(this.indicatorTypeData, row.id)
        this.$message.success('删除成功')
      })
    },
    
    // 提交表单
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.formData.id) {
            // 编辑
            this.updateNode(this.indicatorTypeData, this.formData)
            this.$message.success('编辑成功')
          } else {
            // 新增
            const newNode = {
              ...this.formData,
              id: Date.now(),
              indicatorCount: 0,
              level: this.formData.parentId ? 2 : 1,
              isSystem: false,
              createTime: new Date().toLocaleString(),
              children: []
            }
            
            if (this.formData.parentId) {
              this.addChildNode(this.indicatorTypeData, this.formData.parentId, newNode)
            } else {
              this.indicatorTypeData.push(newNode)
            }
            this.$message.success('新增成功')
          }
          this.dialogVisible = false
        }
      })
    },
    
    // 重置表单
    resetForm() {
      this.formData = {
        id: null,
        parentId: null,
        name: '',
        code: '',
        category: '',
        description: '',
        sort: 0,
        status: 1
      }
      this.parentTypeName = ''
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }
    },
    
    // 树操作辅助方法
    findNodeById(tree, id) {
      for (let node of tree) {
        if (node.id === id) return node
        if (node.children) {
          const found = this.findNodeById(node.children, id)
          if (found) return found
        }
      }
      return null
    },
    
    updateNode(tree, data) {
      for (let node of tree) {
        if (node.id === data.id) {
          Object.assign(node, data)
          return true
        }
        if (node.children && this.updateNode(node.children, data)) {
          return true
        }
      }
      return false
    },
    
    addChildNode(tree, parentId, childNode) {
      for (let node of tree) {
        if (node.id === parentId) {
          if (!node.children) node.children = []
          node.children.push(childNode)
          return true
        }
        if (node.children && this.addChildNode(node.children, parentId, childNode)) {
          return true
        }
      }
      return false
    },
    
    removeNode(tree, id) {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === id) {
          tree.splice(i, 1)
          return true
        }
        if (tree[i].children && this.removeNode(tree[i].children, id)) {
          return true
        }
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.filter-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  margin-bottom: 20px;
}

.table-container {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  
  .type-name {
    display: flex;
    align-items: center;
    
    .level-icon {
      margin-right: 8px;
      color: #409eff;
    }
    
    .el-tag {
      margin-left: 8px;
    }
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>