<template>
  <div class="indicator-selector">
    <el-select
      v-model="selectedIndicator"
      filterable
      remote
      reserve-keyword
      placeholder="请输入指标名称搜索"
      :remote-method="remoteSearch"
      :loading="loading"
      clearable
      style="width: 100%"
      @change="handleChange"
    >
      <el-option
        v-for="item in indicatorOptions"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
        <div class="indicator-option">
          <div class="indicator-name">{{ item.name }}</div>
          <div class="indicator-info">
            <span class="indicator-code">{{ item.code }}</span>
          </div>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { getIndicatorList } from '@/api/yjzb/indicator'

export default {
  name: 'IndicatorSelector',
  props: {
    modelValue: {
      type: [Number, String],
      default: null
    },
    placeholder: {
      type: String,
      default: '请输入指标名称搜索'
    }
  },
  data() {
    return {
      selectedIndicator: this.modelValue,
      indicatorOptions: [],
      loading: false,
      searchKeyword: ''
    }
  },
  watch: {
    modelValue(newVal) {
      this.selectedIndicator = newVal
    }
  },
  methods: {
    async remoteSearch(query) {
      if (query !== '') {
        this.loading = true
        try {
          const response = await getIndicatorList(1, 20, { name: query })
          this.indicatorOptions = response.data.data.records || []
        } catch (error) {
          console.error('搜索指标失败:', error)
          this.indicatorOptions = []
        } finally {
          this.loading = false
        }
      } else {
        this.indicatorOptions = []
      }
    },
    
    handleChange(value) {
      this.$emit('update:modelValue', value)
      this.$emit('change', value)
      
      // 触发选中事件，传递完整的指标信息
      if (value) {
        const selectedItem = this.indicatorOptions.find(item => item.id === value)
        if (selectedItem) {
          this.$emit('select', selectedItem)
        }
      }
    },
    
    // 重置选择
    reset() {
      this.selectedIndicator = null
      this.indicatorOptions = []
      this.$emit('update:modelValue', null)
      this.$emit('change', null)
    }
  }
}
</script>

<style scoped>
.indicator-selector {
  width: 100%;
}

.indicator-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.indicator-name {
  font-weight: 500;
  color: #303133;
}

.indicator-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.indicator-code {
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.indicator-type {
  color: #67c23a;
}
</style>
