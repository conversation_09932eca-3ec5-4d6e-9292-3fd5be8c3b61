package org.springblade.modules.yjzb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import java.time.LocalDateTime;

/**
 * 办公费用AI解读分析实体类
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
@Data
@TableName("yjzb_expense_ai_analysis")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "办公费用AI解读分析")
public class ExpenseAiAnalysis extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 指标ID
     */
    @Schema(description = "指标ID")
    private Long indicatorId;

    /**
     * 期间
     */
    @Schema(description = "期间")
    private String period;

    /**
     * 分析类型
     */
    @Schema(description = "分析类型")
    private String analysisType;

    /**
     * 输入参数
     */
    @Schema(description = "输入参数")
    private String inputParams;

    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    private LocalDateTime executeTime;

    /**
     * 执行状态
     */
    @Schema(description = "执行状态")
    private String executeStatus;

    /**
     * 分析结果
     */
    @Schema(description = "分析结果")
    private String result;

    /**
     * 思考过程
     */
    @Schema(description = "思考过程")
    private String thinkProcess;

    /**
     * 回答内容
     */
    @Schema(description = "回答内容")
    private String answerContent;

    /**
     * 工作流运行ID
     */
    @Schema(description = "工作流运行ID")
    private String workflowRunId;
}
