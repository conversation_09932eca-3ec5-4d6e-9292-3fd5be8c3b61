/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.yjzb.pojo.entity.FinanceKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceKnowledgeVO;

/**
 * 财务知识库 Mapper 接口
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
public interface FinanceKnowledgeMapper extends BaseMapper<FinanceKnowledgeEntity> {

    /**
     * 自定义分页查询
     *
     * @param page 分页对象
     * @param financeKnowledge 查询条件
     * @return 分页结果
     */
    IPage<FinanceKnowledgeVO> selectFinanceKnowledgePage(IPage<FinanceKnowledgeVO> page, @Param("financeKnowledge") FinanceKnowledgeVO financeKnowledge);

    /**
     * 根据ID查询详情
     *
     * @param id 主键ID
     * @return 详情信息
     */
    FinanceKnowledgeVO selectFinanceKnowledgeById(@Param("id") Long id);
}
