/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceCategoryVO;
import java.util.List;

/**
 * 知识分类 Mapper 接口
 *
 * <AUTHOR> Assistant
 * @since 2025-08-06
 */
public interface FinanceCategoryMapper extends BaseMapper<FinanceCategoryEntity> {

    /**
     * 自定义分页查询
     *
     * @param page 分页对象
     * @param financeCategory 查询条件
     * @return 分页结果
     */
    IPage<FinanceCategoryVO> selectFinanceCategoryPage(IPage<FinanceCategoryVO> page, @Param("financeCategory") FinanceCategoryVO financeCategory);

    /**
     * 根据ID查询详情
     *
     * @param id 主键ID
     * @return 详情信息
     */
    FinanceCategoryVO selectFinanceCategoryById(@Param("id") Long id);

    /**
     * 查询树形结构
     *
     * @param knowledgeId 知识库ID
     * @return 树形结构列表
     */
    List<FinanceCategoryVO> selectCategoryTree(@Param("knowledgeId") Long knowledgeId);

    /**
     * 查询子分类列表
     *
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<FinanceCategoryVO> selectChildrenByParentId(@Param("parentId") Long parentId);

    /**
     * 统计分类下的文档数量
     *
     * @param categoryId 分类ID
     * @return 文档数量
     */
    Integer countDocumentsByCategory(@Param("categoryId") Long categoryId);
}
