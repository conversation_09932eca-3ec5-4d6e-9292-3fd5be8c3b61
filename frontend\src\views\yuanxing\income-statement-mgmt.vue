<template>
  <basic-container>
    <div class="panel-header">
      <h4>利润表管理</h4>
      <el-button type="primary" size="small" @click="handleExport">导出</el-button>
    </div>
    <div class="filter-panel">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="选择月份">
          <el-date-picker
            v-model="filterForm.selectedMonth"
            type="month"
            placeholder="选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item label="指标名">
          <el-input v-model="filterForm.indicatorName" placeholder="请输入指标名" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="display: flex;">
      <div style="flex: 1;">
        <el-table :data="indicatorList" stripe border>
          <el-table-column prop="name" label="指标名称" width="180" />
          <el-table-column prop="value" label="数值" width="120" align="right" />
          <el-table-column prop="description" label="说明" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button size="mini" type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button size="mini" type="text" @click="handleAnalysis(row)">分析</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="width: 300px; margin-left: 20px;">
        <div class="warning-panel">
          <div class="panel-header">
            <h4>预警信息</h4>
            <el-badge :value="warningList.length" class="warning-badge">
              <i class="el-icon-warning"></i>
            </el-badge>
          </div>
          <div class="warning-list">
            <div
              v-for="item in warningList"
              :key="item.id"
              class="warning-item"
              @click="handleWarningClick(item)"
            >
              <div class="warning-content">
                <div class="warning-title">{{ item.title }}</div>
                <div class="warning-desc">{{ item.description }}</div>
              </div>
              <div class="warning-level" :class="`level-${item.level}`">
                {{ item.levelName }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="指标分析" v-model="analysisDialogVisible" width="900px" destroy-on-close @open="onAnalysisDialogOpen" @close="onAnalysisDialogClose">
      <div v-show="currentAnalysisIndicator">
        <div class="analysis-content">
          <div class="indicator-info">
            <h4>{{ currentAnalysisIndicator.name }}</h4>
            <p class="indicator-value">当前值：{{ currentAnalysisIndicator.value }}</p>
            <p class="indicator-desc">说明：{{ currentAnalysisIndicator.description }}</p>
          </div>
          <div class="trend-chart-section">
            <h4>趋势分析</h4>
            <div class="mock-chart">
              <div class="chart-placeholder">
                <div id="incomeLineChart" style="width: 100%; height: 300px;"></div>
              </div>
            </div>
          </div>
          <div class="ai-analysis-section">
            <h4>🤖 AI智能解读</h4>
            <div class="ai-content">
              <div class="analysis-item">
                <div class="analysis-label">趋势分析：</div>
                <div class="analysis-text">该指标近5个月呈现平稳或小幅波动趋势，暂无明显异常。</div>
              </div>
              <div class="analysis-item">
                <div class="analysis-label">风险评估：</div>
                <div class="analysis-text risk-low">
                  <el-tag type="success" size="small">低风险</el-tag>
                  当前指标水平正常。
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="analysisDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="exportAnalysis">导出分析报告</el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>
<script>
import * as echarts from 'echarts';
export default {
  name: 'IncomeStatementMgmt',
  data() {
    return {
      filterForm: {
        selectedMonth: '',
        indicatorName: ''
      },
      monthOptions: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05'],
      warningList: [
        { id: 1, title: '营业总成本高于预警线', description: '本期营业总成本已超过预警线，请关注成本控制', level: 'high', levelName: '高风险' },
        { id: 2, title: '利润率低于行业平均', description: '本期利润率低于行业平均水平，需关注盈利能力', level: 'medium', levelName: '中风险' }
      ],
      indicatorList: [
        { name: '营业总收入', value: 2000000, description: '企业日常经营活动的总收入' },
        { name: '营业总成本', value: 1500000, description: '企业日常经营活动的总成本' },
        { name: '营业利润', value: 500000, description: '企业日常经营活动产生的利润' },
        { name: '利润总额', value: 480000, description: '企业的总利润' },
        { name: '所得税费用', value: 80000, description: '企业应缴纳的所得税' },
        { name: '净利润', value: 400000, description: '企业的净收益' },
        { name: '基本每股收益', value: 2.5, description: '企业每股收益的基本指标' },
        { name: '稀释每股收益', value: 2.3, description: '企业每股收益的稀释指标' }
      ],
      analysisDialogVisible: false,
      currentAnalysisIndicator: null,
      incomeLineChartInstance: null
    };
  },
  methods: {
    handleExport() { this.$message.success('导出中...'); },
    handleQuery() { this.$message.success('查询中...'); },
    handleReset() {
      this.filterForm = {
        selectedMonth: '',
        indicatorName: ''
      };
    },
    handleEdit(row) { this.$message.success(`编辑指标: ${row.name}`); },
    handleAnalysis(row) {
      this.currentAnalysisIndicator = row;
      this.analysisDialogVisible = true;
    },
    exportAnalysis() { this.$message.success('分析报告导出中...'); },
    onAnalysisDialogOpen() {
      this.$nextTick(() => {
        this.renderIncomeLineChart();
        if (this.incomeLineChartInstance) {
          this.incomeLineChartInstance.resize();
        }
      });
    },
    onAnalysisDialogClose() {
      if (this.incomeLineChartInstance) {
        this.incomeLineChartInstance.dispose();
        this.incomeLineChartInstance = null;
      }
    },
    renderIncomeLineChart() {
      const chartDom = document.getElementById('incomeLineChart');
      if (!chartDom) return;
      if (this.incomeLineChartInstance) {
        this.incomeLineChartInstance.dispose();
      }
      this.incomeLineChartInstance = echarts.init(chartDom);
      this.incomeLineChartInstance.setOption({
        title: { show: false },
        tooltip: { trigger: 'axis' },
        legend: { data: ['今年', '去年'] },
        grid: { left: 40, right: 20, bottom: 30, top: 30 },
        xAxis: { type: 'category', data: ['1月','2月','3月','4月','5月'] },
        yAxis: { type: 'value' },
        series: [
          { name: '今年', type: 'line', data: [200, 220, 210, 230, 225], smooth: false, label: { show: true, position: 'top' } },
          { name: '去年', type: 'line', data: [180, 200, 190, 210, 205], smooth: false, label: { show: true, position: 'top' } }
        ]
      });
      this.incomeLineChartInstance.resize();
    },
    handleWarningClick(item) {
      this.$message.warning(`查看预警详情：${item.title}`);
    }
  }
};
</script>
<style scoped>
.panel-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #eee; }
.filter-panel { margin-bottom: 15px; padding: 10px; background-color: #f5f7fa; border-radius: 4px; }
.mock-chart { border: 1px dashed #ddd; display: flex; align-items: center; justify-content: center; color: #999; text-align: center; background: #fafafa; width: 100%; height: 320px; min-height: 300px; }
.chart-placeholder { width: 100%; height: 100%; min-height: 300px; }
#incomeLineChart { width: 100% !important; height: 100% !important; min-height: 300px; }
.warning-panel {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}
.panel-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}
.warning-badge {
  position: relative;
}
.warning-list {
  max-height: 300px;
  overflow-y: auto;
}
.warning-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}
.warning-item:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
}
.warning-content {
  flex: 1;
}
.warning-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
}
.warning-desc {
  font-size: 12px;
  color: #666;
}
.warning-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}
.warning-level.level-high {
  background: #fef0f0;
  color: #f56c6c;
}
.warning-level.level-medium {
  background: #fdf6ec;
  color: #e6a23c;
}
.warning-level.level-low {
  background: #f0f9ff;
  color: #409eff;
}
</style> 